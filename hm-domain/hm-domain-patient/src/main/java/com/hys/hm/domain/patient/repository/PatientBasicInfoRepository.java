package com.hys.hm.domain.patient.repository;

import com.hys.hm.domain.patient.entity.PatientBasicInfoEntity;
import com.hys.hm.shared.framework.repository.BaseRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 患者基本信息仓储接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-30
 */
@Repository
public interface PatientBasicInfoRepository extends BaseRepository<PatientBasicInfoEntity, String> {

    /**
     * 根据身份证号查找患者基本信息
     */
    Optional<PatientBasicInfoEntity> findByIdcard(String idcard);

    /**
     * 根据手机号查找患者基本信息
     */
    Optional<PatientBasicInfoEntity> findByPhone(String phone);

    /**
     * 根据姓名模糊查询患者基本信息
     */
    List<PatientBasicInfoEntity> findByNameContaining(String name);

    /**
     * 根据地址模糊查询患者基本信息
     */
    List<PatientBasicInfoEntity> findByAddressContaining(String address);

    /**
     * 根据机构ID查找患者基本信息列表
     */
    List<PatientBasicInfoEntity> findByOrgId(String orgId);

    /**
     * 根据责任医生查找患者基本信息列表
     */
    List<PatientBasicInfoEntity> findByDutyDoctor(String dutyDoctor);

    /**
     * 根据档案状态查找患者基本信息列表
     */
    List<PatientBasicInfoEntity> findByRecordStatus(String recordStatus);

    /**
     * 根据签约状态查找患者基本信息列表
     */
    List<PatientBasicInfoEntity> findBySignState(String signState);

    /**
     * 根据人群属性查找患者基本信息列表
     */
    List<PatientBasicInfoEntity> findByCrowdAttributeContaining(String crowdAttribute);

    /**
     * 根据重点人群查找患者基本信息列表
     */
    List<PatientBasicInfoEntity> findByEmphasisCrowdContaining(String emphasisCrowd);

    /**
     * 根据社区查找患者基本信息列表
     */
    List<PatientBasicInfoEntity> findByCommunity(String community);

    /**
     * 根据街道编码查找患者基本信息列表
     */
    List<PatientBasicInfoEntity> findByStreetCode(String streetCode);

    /**
     * 根据居委会编码查找患者基本信息列表
     */
    List<PatientBasicInfoEntity> findByCommitteeCode(String committeeCode);

    /**
     * 检查身份证号是否已存在
     */
    boolean existsByIdcard(String idcard);

    /**
     * 检查手机号是否已存在
     */
    boolean existsByPhone(String phone);

    /**
     * 统计指定机构的患者基本信息数量
     */
    long countByOrgId(String orgId);

    /**
     * 统计指定责任医生的患者基本信息数量
     */
    long countByDutyDoctor(String dutyDoctor);

    /**
     * 根据多个条件查询患者基本信息
     */
    @Query("SELECT p FROM PatientBasicInfoEntity p WHERE " +
           "(:name IS NULL OR p.name LIKE %:name%) AND " +
           "(:idcard IS NULL OR p.idcard = :idcard) AND " +
           "(:phone IS NULL OR p.phone = :phone) AND " +
           "(:orgId IS NULL OR p.orgId = :orgId) AND " +
           "(:dutyDoctor IS NULL OR p.dutyDoctor = :dutyDoctor) AND " +
           "(:recordStatus IS NULL OR p.recordStatus = :recordStatus) AND " +
           "(:signState IS NULL OR p.signState = :signState)")
    List<PatientBasicInfoEntity> findByMultipleConditions(
            @Param("name") String name,
            @Param("idcard") String idcard,
            @Param("phone") String phone,
            @Param("orgId") String orgId,
            @Param("dutyDoctor") String dutyDoctor,
            @Param("recordStatus") String recordStatus,
            @Param("signState") String signState
    );

    /**
     * 根据年龄范围查询患者基本信息
     */
    @Query("SELECT p FROM PatientBasicInfoEntity p WHERE " +
           "YEAR(CURRENT_DATE) - YEAR(p.birthday) BETWEEN :minAge AND :maxAge")
    List<PatientBasicInfoEntity> findByAgeRange(@Param("minAge") int minAge, @Param("maxAge") int maxAge);

    /**
     * 根据BMI范围查询患者基本信息
     */
    @Query("SELECT p FROM PatientBasicInfoEntity p WHERE " +
           "p.bmi BETWEEN :minBmi AND :maxBmi")
    List<PatientBasicInfoEntity> findByBmiRange(@Param("minBmi") double minBmi, @Param("maxBmi") double maxBmi);

    /**
     * 查询有过敏史的患者基本信息
     */
    @Query("SELECT p FROM PatientBasicInfoEntity p WHERE " +
           "p.allergyHistory IS NOT NULL AND p.allergyHistory != ''")
    List<PatientBasicInfoEntity> findPatientsWithAllergyHistory();

    /**
     * 查询有家族病史的患者基本信息
     */
    @Query("SELECT p FROM PatientBasicInfoEntity p WHERE " +
           "p.fatherHistory IS NOT NULL OR p.motherHistory IS NOT NULL OR " +
           "p.brothersHistory IS NOT NULL OR p.childrenHistory IS NOT NULL OR " +
           "p.inheritHistory IS NOT NULL")
    List<PatientBasicInfoEntity> findPatientsWithFamilyHistory();

    /**
     * 根据地址关键词查询患者基本信息
     */
    @Query("SELECT p FROM PatientBasicInfoEntity p WHERE " +
           "p.address LIKE %:keyword% OR p.registerAddress LIKE %:keyword% OR " +
           "p.community LIKE %:keyword% OR p.street LIKE %:keyword%")
    List<PatientBasicInfoEntity> findByAddressKeyword(@Param("keyword") String keyword);
}
