package com.hys.hm.domain.patient.event.listener;

import com.hys.hm.domain.patient.event.PatientCreatedEvent;
import com.hys.hm.domain.patient.event.PatientDeletedEvent;
import com.hys.hm.shared.framework.event.DomainEventListener;
import com.hys.hm.shared.framework.event.EntityCreatedEvent;
import com.hys.hm.shared.framework.event.EntityDeletedEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 患者事件监听器
 * 监听患者相关的领域事件
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-30
 */
@Slf4j
@Component
public class PatientEventListener extends DomainEventListener {

    @Override
    protected void onEntityCreated(EntityCreatedEvent<?, ?> event) {
        // 只处理患者创建事件
        if (event instanceof PatientCreatedEvent) {
            PatientCreatedEvent patientEvent = (PatientCreatedEvent) event;
            handlePatientCreated(patientEvent);
        }
    }

    @Override
    protected void onEntityDeleted(EntityDeletedEvent<?, ?> event) {
        // 只处理患者删除事件
        if (event instanceof PatientDeletedEvent) {
            PatientDeletedEvent patientEvent = (PatientDeletedEvent) event;
            handlePatientDeleted(patientEvent);
        }
    }

    @Override
    protected void onEntityCreatedAsync(EntityCreatedEvent<?, ?> event) {
        // 只处理患者创建事件
        if (event instanceof PatientCreatedEvent) {
            PatientCreatedEvent patientEvent = (PatientCreatedEvent) event;
            handlePatientCreatedAsync(patientEvent);
        }
    }

    @Override
    protected void onEntityDeletedAsync(EntityDeletedEvent<?, ?> event) {
        // 只处理患者删除事件
        if (event instanceof PatientDeletedEvent) {
            PatientDeletedEvent patientEvent = (PatientDeletedEvent) event;
            handlePatientDeletedAsync(patientEvent);
        }
    }

    /**
     * 同步处理患者创建事件
     */
    private void handlePatientCreated(PatientCreatedEvent event) {
        log.info("【同步处理】患者创建事件 - 患者ID: {}, 姓名: {}, 身份证: {}, 责任医生: {}, 机构: {}, 操作者: {}",
                event.getAggregateId(),
                event.getPatientName(),
                maskIdcard(event.getPatientIdcard()),
                event.getDutyDoctorName(),
                event.getOrgName(),
                event.getOperatorId());

        // TODO: 同步处理逻辑
        // 1. 数据验证
        // 2. 发送欢迎短信
        // 3. 创建患者配置文件
        // 4. 更新统计信息
        
        log.info("患者创建同步处理完成 - 患者ID: {}", event.getAggregateId());
    }

    /**
     * 同步处理患者删除事件
     */
    private void handlePatientDeleted(PatientDeletedEvent event) {
        log.info("【同步处理】患者删除事件 - 患者ID: {}, 姓名: {}, 身份证: {}, 删除类型: {}, 删除原因: {}, 操作者: {}",
                event.getAggregateId(),
                event.getPatientName(),
                maskIdcard(event.getPatientIdcard()),
                event.isSoftDelete() ? "软删除" : "硬删除",
                event.getDeleteReason(),
                event.getOperatorId());

        // TODO: 同步处理逻辑
        // 1. 清理缓存
        // 2. 记录删除日志
        // 3. 数据一致性检查
        // 4. 更新统计信息
        
        log.info("患者删除同步处理完成 - 患者ID: {}", event.getAggregateId());
    }

    /**
     * 异步处理患者创建事件
     */
    private void handlePatientCreatedAsync(PatientCreatedEvent event) {
        log.info("【异步处理】患者创建事件 - 患者ID: {}, 姓名: {}, 责任医生: {}, 机构: {}",
                event.getAggregateId(),
                event.getPatientName(),
                event.getDutyDoctorName(),
                event.getOrgName());

        try {
            // 模拟异步处理耗时操作
            Thread.sleep(100);

            // TODO: 异步处理逻辑
            // 1. 发送通知给责任医生
            // 2. 数据统计分析
            // 3. 第三方系统同步
            // 4. 生成患者档案报告
            // 5. 发送邮件通知

            log.info("患者创建异步处理完成 - 患者ID: {}, 处理耗时: 100ms", event.getAggregateId());

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("患者创建异步处理被中断 - 患者ID: {}", event.getAggregateId(), e);
        } catch (Exception e) {
            log.error("患者创建异步处理失败 - 患者ID: {}", event.getAggregateId(), e);
        }
    }

    /**
     * 异步处理患者删除事件
     */
    private void handlePatientDeletedAsync(PatientDeletedEvent event) {
        log.info("【异步处理】患者删除事件 - 患者ID: {}, 姓名: {}, 删除类型: {}, 删除原因: {}",
                event.getAggregateId(),
                event.getPatientName(),
                event.isSoftDelete() ? "软删除" : "硬删除",
                event.getDeleteReason());

        try {
            // 模拟异步处理耗时操作
            Thread.sleep(150);

            // TODO: 异步处理逻辑
            // 1. 清理相关数据
            // 2. 发送删除通知
            // 3. 数据归档
            // 4. 更新搜索索引
            // 5. 同步到数据仓库

            log.info("患者删除异步处理完成 - 患者ID: {}, 处理耗时: 150ms", event.getAggregateId());

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("患者删除异步处理被中断 - 患者ID: {}", event.getAggregateId(), e);
        } catch (Exception e) {
            log.error("患者删除异步处理失败 - 患者ID: {}", event.getAggregateId(), e);
        }
    }

    /**
     * 脱敏身份证号
     */
    private String maskIdcard(String idcard) {
        if (idcard == null || idcard.length() < 8) {
            return idcard;
        }
        return idcard.substring(0, 4) + "**********" + idcard.substring(idcard.length() - 4);
    }
}
