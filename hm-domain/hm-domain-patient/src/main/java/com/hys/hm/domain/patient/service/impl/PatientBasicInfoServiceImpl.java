package com.hys.hm.domain.patient.service.impl;

import com.hys.hm.domain.patient.entity.PatientBasicInfoEntity;
import com.hys.hm.domain.patient.event.PatientCreatedEvent;
import com.hys.hm.domain.patient.event.PatientDeletedEvent;
import com.hys.hm.domain.patient.repository.PatientBasicInfoRepository;
import com.hys.hm.domain.patient.service.PatientBasicInfoService;
import com.hys.hm.shared.framework.event.DomainEventPublisher;
import com.hys.hm.shared.common.page.PageRequest;
import com.hys.hm.shared.common.page.PageResult;
import com.hys.hm.shared.common.query.QueryCondition;
import com.hys.hm.shared.framework.service.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 患者基本信息服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-30
 */
@Slf4j
@Service
@Transactional(readOnly = true)
public class PatientBasicInfoServiceImpl extends BaseServiceImpl<PatientBasicInfoEntity, String> implements PatientBasicInfoService {

    private final PatientBasicInfoRepository patientRepository;

    @Autowired(required = false)
    private DomainEventPublisher domainEventPublisher;

    public PatientBasicInfoServiceImpl(PatientBasicInfoRepository patientRepository) {
        super(patientRepository);
        this.patientRepository = patientRepository;
    }

    @Override
    public Optional<PatientBasicInfoEntity> findByIdcard(String idcard) {
        if (!StringUtils.hasText(idcard)) {
            return Optional.empty();
        }
        return patientRepository.findByIdcard(idcard);
    }

    @Override
    public List<PatientBasicInfoEntity> findByEncryptedField(String fieldName, Object value) {
        // TODO: 实现加密字段精确查询
        // 这里应该使用加密索引进行查询
        log.debug("加密字段精确查询: field={}, value=[MASKED]", fieldName);

        // 暂时使用普通查询作为fallback
        return findByField(fieldName, value);
    }

    @Override
    public Optional<PatientBasicInfoEntity> findFirstByEncryptedField(String fieldName, Object value) {
        // TODO: 实现加密字段精确查询（返回第一个）
        log.debug("加密字段精确查询（第一个）: field={}, value=[MASKED]", fieldName);

        List<PatientBasicInfoEntity> results = findByEncryptedField(fieldName, value);
        return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
    }

    @Override
    public List<PatientBasicInfoEntity> findByEncryptedFieldLike(String fieldName, Object value) {
        // TODO: 实现加密字段模糊查询
        // 这里应该使用加密分词索引进行查询
        log.debug("加密字段模糊查询: field={}, value=[MASKED]", fieldName);

        // 暂时使用普通查询作为fallback
        return findByFieldLike(fieldName, value);
    }

    @Override
    public Optional<PatientBasicInfoEntity> findByPhone(String phone) {
        if (!StringUtils.hasText(phone)) {
            return Optional.empty();
        }
        return patientRepository.findByPhone(phone);
    }

    @Override
    public List<PatientBasicInfoEntity> findByNameContaining(String name) {
        if (!StringUtils.hasText(name)) {
            return new ArrayList<>();
        }
        return patientRepository.findByNameContaining(name);
    }

    @Override
    public List<PatientBasicInfoEntity> findByOrgId(String orgId) {
        if (!StringUtils.hasText(orgId)) {
            return new ArrayList<>();
        }
        return patientRepository.findByOrgId(orgId);
    }

    @Override
    public List<PatientBasicInfoEntity> findByDutyDoctor(String dutyDoctor) {
        if (!StringUtils.hasText(dutyDoctor)) {
            return new ArrayList<>();
        }
        return patientRepository.findByDutyDoctor(dutyDoctor);
    }

    @Override
    public boolean existsByIdcard(String idcard) {
        if (!StringUtils.hasText(idcard)) {
            return false;
        }
        return patientRepository.existsByIdcard(idcard);
    }

    @Override
    public boolean existsByPhone(String phone) {
        if (!StringUtils.hasText(phone)) {
            return false;
        }
        return patientRepository.existsByPhone(phone);
    }

    @Override
    @Transactional
    public PatientBasicInfoEntity createPatient(PatientBasicInfoEntity patient) {
        validatePatient(patient);

        // 生成ID
        if (!StringUtils.hasText(patient.getId())) {
            patient.setId(UUID.randomUUID().toString().replace("-", ""));
        }

        // 生成拼音姓名
        if (StringUtils.hasText(patient.getName())) {
            patient.setPinyinName(generatePinyinName(patient.getName()));
        }

        // 计算BMI
        calculateBmi(patient);

        // 设置创建时间
        if (patient.getCreateTime() == null) {
            patient.setCreateTime(LocalDateTime.now());
        }

        // 设置建档日期
        if (patient.getBuildDate() == null) {
            patient.setBuildDate(LocalDateTime.now());
        }

        // 设置默认状态
        if (!StringUtils.hasText(patient.getRecordStatus())) {
            patient.setRecordStatus("1"); // 1-正常
        }

        // 保存患者信息
        PatientBasicInfoEntity savedPatient = save(patient);

        // 发布患者创建事件
        if (domainEventPublisher != null) {
            PatientCreatedEvent event = PatientCreatedEvent.of(savedPatient, getCurrentUserId());
            domainEventPublisher.publish(event);
        }

        return savedPatient;
    }

    @Override
    @Transactional
    public boolean deletePatient(String patientId, String deleteReason) {
        if (!StringUtils.hasText(patientId)) {
            throw new IllegalArgumentException("患者ID不能为空");
        }

        // 获取患者信息用于事件发布
        Optional<PatientBasicInfoEntity> patientOpt = findById(patientId);
        PatientBasicInfoEntity patient = patientOpt.orElse(null);

        // 执行删除
        boolean deleted = deleteById(patientId);

        if (deleted && domainEventPublisher != null) {
            // 发布患者删除事件
            PatientDeletedEvent event = patient != null
                    ? PatientDeletedEvent.hardDelete(patient, getCurrentUserId(), deleteReason)
                    : PatientDeletedEvent.hardDelete(patientId, getCurrentUserId(), deleteReason);
            domainEventPublisher.publish(event);
        }

        return deleted;
    }

    @Override
    @Transactional
    public boolean softDeletePatient(String patientId, String deleteReason) {
        if (!StringUtils.hasText(patientId)) {
            throw new IllegalArgumentException("患者ID不能为空");
        }

        // 获取患者信息用于事件发布
        Optional<PatientBasicInfoEntity> patientOpt = findById(patientId);
        PatientBasicInfoEntity patient = patientOpt.orElse(null);

        // 执行软删除
        boolean deleted = softDeleteById(patientId);

        if (deleted && domainEventPublisher != null) {
            // 发布患者软删除事件
            PatientDeletedEvent event = patient != null
                    ? PatientDeletedEvent.softDelete(patient, getCurrentUserId(), deleteReason)
                    : PatientDeletedEvent.softDelete(patientId, getCurrentUserId(), deleteReason);
            domainEventPublisher.publish(event);
        }

        return deleted;
    }

    @Override
    @Transactional
    public List<PatientBasicInfoEntity> batchImportPatients(List<PatientBasicInfoEntity> patients) {
        List<PatientBasicInfoEntity> savedPatients = new ArrayList<>();

        for (PatientBasicInfoEntity patient : patients) {
            try {
                PatientBasicInfoEntity saved = createPatient(patient);
                savedPatients.add(saved);
            } catch (Exception e) {
                log.error("批量导入患者失败: {}, 错误: {}", patient.getName(), e.getMessage());
                // 继续处理下一个，不中断整个批量操作
            }
        }

        log.info("批量导入患者完成，成功: {}, 总数: {}", savedPatients.size(), patients.size());
        return savedPatients;
    }

    @Override
    @Transactional
    public PatientBasicInfoEntity calculateAndUpdateBmi(String patientId) {
        Optional<PatientBasicInfoEntity> optional = findById(patientId);
        if (optional.isEmpty()) {
            throw new IllegalArgumentException("患者不存在: " + patientId);
        }

        PatientBasicInfoEntity patient = optional.get();
        calculateBmi(patient);
        return save(patient);
    }

    @Override
    public String generatePinyinName(String name) {
        if (!StringUtils.hasText(name)) {
            return "";
        }

        try {
            HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
            format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
            format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
            format.setVCharType(HanyuPinyinVCharType.WITH_V);

            StringBuilder pinyin = new StringBuilder();
            for (char c : name.toCharArray()) {
                if (Character.toString(c).matches("[\\u4E00-\\u9FA5]+")) {
                    String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, format);
                    if (pinyinArray != null && pinyinArray.length > 0) {
                        pinyin.append(pinyinArray[0]);
                    }
                } else {
                    pinyin.append(c);
                }
            }

            return pinyin.toString();
        } catch (Exception e) {
            log.warn("生成拼音姓名失败: {}, 错误: {}", name, e.getMessage());
            return name;
        }
    }

    @Override
    public void validatePatient(PatientBasicInfoEntity patient) {
        if (patient == null) {
            throw new IllegalArgumentException("患者信息不能为空");
        }

        if (!StringUtils.hasText(patient.getName())) {
            throw new IllegalArgumentException("姓名不能为空");
        }

        // 验证身份证号唯一性（更新时排除自己）
        if (StringUtils.hasText(patient.getIdcard())) {
            Optional<PatientBasicInfoEntity> existing = findByIdcard(patient.getIdcard());
            if (existing.isPresent() && !existing.get().getId().equals(patient.getId())) {
                throw new IllegalArgumentException("身份证号已存在: " + patient.getIdcard());
            }
        }

        // 验证手机号唯一性（更新时排除自己）
        if (StringUtils.hasText(patient.getPhone())) {
            Optional<PatientBasicInfoEntity> existing = findByPhone(patient.getPhone());
            if (existing.isPresent() && !existing.get().getId().equals(patient.getId())) {
                throw new IllegalArgumentException("手机号已存在: " + patient.getPhone());
            }
        }

        // 验证身高体重范围
        if (patient.getHeight() != null && (patient.getHeight() < 50 || patient.getHeight() > 250)) {
            throw new IllegalArgumentException("身高范围应在50-250cm之间");
        }

        if (patient.getWeight() != null && (patient.getWeight() < 10 || patient.getWeight() > 500)) {
            throw new IllegalArgumentException("体重范围应在10-500kg之间");
        }
    }

    @Override
    public PageResult<PatientBasicInfoEntity> findByMultipleConditionsWithPage(
            String name, String idcard, String phone, String orgId,
            String dutyDoctor, String recordStatus, String signState,
            PageRequest pageRequest) {

        List<QueryCondition> conditions = new ArrayList<>();

        if (StringUtils.hasText(name)) {
            conditions.add(QueryCondition.like("name", "%" + name + "%"));
        }
        if (StringUtils.hasText(idcard)) {
            conditions.add(QueryCondition.eq("idcard", idcard));
        }
        if (StringUtils.hasText(phone)) {
            conditions.add(QueryCondition.eq("phone", phone));
        }
        if (StringUtils.hasText(orgId)) {
            conditions.add(QueryCondition.eq("orgId", orgId));
        }
        if (StringUtils.hasText(dutyDoctor)) {
            conditions.add(QueryCondition.eq("dutyDoctor", dutyDoctor));
        }
        if (StringUtils.hasText(recordStatus)) {
            conditions.add(QueryCondition.eq("recordStatus", recordStatus));
        }
        if (StringUtils.hasText(signState)) {
            conditions.add(QueryCondition.eq("signState", signState));
        }

        pageRequest.setQueryConditions(conditions);
        return findByPageRequest(pageRequest);
    }

    @Override
    public List<PatientBasicInfoEntity> findByAgeRange(int minAge, int maxAge) {
        return patientRepository.findByAgeRange(minAge, maxAge);
    }

    @Override
    public List<PatientBasicInfoEntity> findByBmiRange(double minBmi, double maxBmi) {
        return patientRepository.findByBmiRange(minBmi, maxBmi);
    }

    @Override
    public List<PatientBasicInfoEntity> findPatientsWithAllergyHistory() {
        return patientRepository.findPatientsWithAllergyHistory();
    }

    @Override
    public List<PatientBasicInfoEntity> findPatientsWithFamilyHistory() {
        return patientRepository.findPatientsWithFamilyHistory();
    }

    @Override
    public PatientStatistics getStatistics() {
        return new PatientStatisticsImpl();
    }

    /**
     * 计算BMI
     */
    private void calculateBmi(PatientBasicInfoEntity patient) {
        if (patient.getHeight() != null && patient.getWeight() != null
                && patient.getHeight() > 0 && patient.getWeight() > 0) {

            double heightInMeters = patient.getHeight() / 100.0;
            double bmi = patient.getWeight() / (heightInMeters * heightInMeters);

            // 保留2位小数
            BigDecimal bmiDecimal = new BigDecimal(bmi).setScale(2, RoundingMode.HALF_UP);
            patient.setBmi(bmiDecimal.doubleValue());
        }
    }

    /**
     * 获取当前用户ID
     */
    private String getCurrentUserId() {
        // TODO: 从Spring Security上下文获取当前用户ID
        return "system";
    }

    /**
     * 患者统计信息实现类
     */
    private class PatientStatisticsImpl implements PatientStatistics {

        @Override
        public long getTotalCount() {
            return count();
        }

        @Override
        public long getActiveCount() {
            return countByRecordStatus("1");
        }

        @Override
        public long getSignedCount() {
            return countBySignState("1");
        }

        @Override
        public long getMaleCount() {
            return countBySex("1");
        }

        @Override
        public long getFemaleCount() {
            return countBySex("2");
        }

        @Override
        public double getAverageAge() {
            // TODO: 实现平均年龄计算逻辑
            return 0.0;
        }

        @Override
        public double getAverageBmi() {
            // TODO: 实现平均BMI计算逻辑
            return 0.0;
        }

        @Override
        public long getWithAllergyCount() {
            return findPatientsWithAllergyHistory().size();
        }

        @Override
        public long getWithFamilyHistoryCount() {
            return findPatientsWithFamilyHistory().size();
        }

        private long countByRecordStatus(String recordStatus) {
            return patientRepository.findByRecordStatus(recordStatus).size();
        }

        private long countBySignState(String signState) {
            return patientRepository.findBySignState(signState).size();
        }

        private long countBySex(String sex) {
            List<QueryCondition> conditions = List.of(
                    QueryCondition.eq("sex", sex)
            );
            return countByConditions(conditions);
        }
    }
}
