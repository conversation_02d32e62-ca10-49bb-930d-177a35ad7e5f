package com.hys.hm.domain.patient.event;

import com.hys.hm.domain.patient.entity.PatientBasicInfoEntity;
import com.hys.hm.shared.framework.event.EntityDeletedEvent;
import lombok.Getter;

/**
 * 患者删除事件
 * 当患者基本信息被删除时发布此事件
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-30
 */
@Getter
public class PatientDeletedEvent extends EntityDeletedEvent<PatientBasicInfoEntity, String> {

    /**
     * 患者姓名
     */
    private final String patientName;

    /**
     * 患者身份证号
     */
    private final String patientIdcard;

    /**
     * 删除原因
     */
    private final String deleteReason;

    /**
     * 构造函数（仅ID）
     */
    public PatientDeletedEvent(String patientId, boolean softDelete, String operatorId, String deleteReason) {
        super(patientId, PatientBasicInfoEntity.class, softDelete, operatorId);
        this.patientName = null;
        this.patientIdcard = null;
        this.deleteReason = deleteReason;
    }

    /**
     * 构造函数（带删除的患者信息）
     */
    public PatientDeletedEvent(PatientBasicInfoEntity deletedPatient, boolean softDelete, String operatorId, String deleteReason) {
        super(deletedPatient, deletedPatient.getId(), PatientBasicInfoEntity.class, softDelete, operatorId);
        this.patientName = deletedPatient.getName();
        this.patientIdcard = deletedPatient.getIdcard();
        this.deleteReason = deleteReason;
    }

    /**
     * 静态工厂方法（硬删除）
     */
    public static PatientDeletedEvent hardDelete(String patientId, String operatorId, String deleteReason) {
        return new PatientDeletedEvent(patientId, false, operatorId, deleteReason);
    }

    /**
     * 静态工厂方法（软删除）
     */
    public static PatientDeletedEvent softDelete(String patientId, String operatorId, String deleteReason) {
        return new PatientDeletedEvent(patientId, true, operatorId, deleteReason);
    }

    /**
     * 静态工厂方法（硬删除，带患者信息）
     */
    public static PatientDeletedEvent hardDelete(PatientBasicInfoEntity deletedPatient, String operatorId, String deleteReason) {
        return new PatientDeletedEvent(deletedPatient, false, operatorId, deleteReason);
    }

    /**
     * 静态工厂方法（软删除，带患者信息）
     */
    public static PatientDeletedEvent softDelete(PatientBasicInfoEntity deletedPatient, String operatorId, String deleteReason) {
        return new PatientDeletedEvent(deletedPatient, true, operatorId, deleteReason);
    }

    @Override
    public String toString() {
        return String.format("PatientDeletedEvent{patientId='%s', patientName='%s', patientIdcard='%s', " +
                "softDelete=%s, deleteReason='%s', operatorId='%s', occurredOn=%s}",
                getAggregateId(), patientName, patientIdcard, isSoftDelete(), deleteReason, 
                getOperatorId(), getOccurredOn());
    }
}
