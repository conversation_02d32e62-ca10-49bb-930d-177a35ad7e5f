<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.hys</groupId>
        <artifactId>hm-domain</artifactId>
        <version>3.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>hm-domain-patient</artifactId>
    <name>hm-domain-patient</name>
    <description>患者领域模块</description>

    <dependencies>
        <!-- 共享框架 -->
        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-shared-framework</artifactId>
        </dependency>

        <!-- 共享通用模块 -->
        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-shared-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-shared-types</artifactId>
        </dependency>

        <!-- Spring Boot Starter Data JPA -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>

        <!-- Spring Boot Starter Validation -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 拼音转换工具 -->
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.1</version>
        </dependency>
    </dependencies>

</project>
