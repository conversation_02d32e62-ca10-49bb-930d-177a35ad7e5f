package com.hys.hm.application.referral.service;

import com.hys.hm.application.referral.dto.ReferralCreateDTO;
import com.hys.hm.application.referral.dto.ReferralDTO;
import com.hys.hm.application.referral.mapper.ReferralMapper;
import com.hys.hm.domain.referral.entity.ReferralFormEntity;
import com.hys.hm.domain.referral.repository.ReferralFormRepository;
import com.hys.hm.domain.referral.service.impl.ReferralDomainServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 转诊应用服务测试类
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@ExtendWith(MockitoExtension.class)
class ReferralApplicationServiceTest {

    @Mock
    private ReferralFormRepository referralFormRepository;

    @Mock
    private ReferralDomainServiceImpl referralDomainService;

    @Mock
    private ApplicationEventPublisher eventPublisher;

    @Mock
    private ReferralMapper referralMapper;

    @Mock
    private PatientQueryService patientQueryService;

    @InjectMocks
    private ReferralApplicationService referralApplicationService;

    private ReferralCreateDTO createDTO;
    private ReferralFormEntity entity;
    private ReferralDTO dto;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        createDTO = ReferralCreateDTO.builder()
            .basicInfoId("patient-001")
            .patientName("张三")
            .age(45)
            .phone("13800138000")
            .fromHospital("市人民医院")
            .toHospital("省人民医院")
            .outUnitName("市人民医院")
            .inUnitName("省人民医院")
            .outDoctorName("李医生")
            .impression("高血压")
            .referralReason("需要进一步检查")
            .gender(1)
            .build();

        entity = new ReferralFormEntity();
        entity.setId("referral-001");
        entity.setBasicInfoId("patient-001");
        entity.setPatientName("张三");
        entity.setFromHospital("市人民医院");
        entity.setToHospital("省人民医院");
        entity.setStatus(1);
        entity.setCreateTime(LocalDateTime.now());

        dto = ReferralDTO.builder()
            .id("referral-001")
            .basicInfoId("patient-001")
            .patientName("张三")
            .fromHospital("市人民医院")
            .toHospital("省人民医院")
            .status(1)
            .statusDescription("待处理")
            .createTime(LocalDateTime.now())
            .build();
    }

    @Test
    void testCreateReferral_Success() {
        // Given
        when(patientQueryService.existsPatient("patient-001")).thenReturn(true);
        when(referralMapper.toEntity(createDTO)).thenReturn(entity);
        when(referralDomainService.createReferral(entity)).thenReturn(entity);
        when(referralMapper.toDTO(entity)).thenReturn(dto);

        // When
        ReferralDTO result = referralApplicationService.createReferral(createDTO);

        // Then
        assertNotNull(result);
        assertEquals("referral-001", result.getId());
        assertEquals("张三", result.getPatientName());
        assertEquals("市人民医院", result.getFromHospital());
        assertEquals("省人民医院", result.getToHospital());
        assertEquals(1, result.getStatus());

        // 验证方法调用
        verify(patientQueryService).existsPatient("patient-001");
        verify(referralMapper).toEntity(createDTO);
        verify(referralDomainService).createReferral(entity);
        verify(referralMapper).toDTO(entity);
        verify(eventPublisher).publishEvent(any());
    }

    @Test
    void testCreateReferral_PatientNotExists() {
        // Given
        when(patientQueryService.existsPatient("patient-001")).thenReturn(false);

        // When & Then
        assertThrows(Exception.class, () -> {
            referralApplicationService.createReferral(createDTO);
        });

        // 验证不会调用后续方法
        verify(patientQueryService).existsPatient("patient-001");
        verify(referralMapper, never()).toEntity(any());
        verify(referralDomainService, never()).createReferral(any());
    }

    @Test
    void testAcceptReferral_Success() {
        // Given
        String referralId = "referral-001";
        String processOpinion = "同意接收";
        
        when(referralFormRepository.findById(referralId)).thenReturn(java.util.Optional.of(entity));
        when(referralDomainService.acceptReferral(referralId, processOpinion)).thenReturn(entity);
        when(referralMapper.toDTO(entity)).thenReturn(dto);

        // When
        ReferralDTO result = referralApplicationService.acceptReferral(referralId, processOpinion);

        // Then
        assertNotNull(result);
        assertEquals("referral-001", result.getId());

        // 验证方法调用
        verify(referralFormRepository).findById(referralId);
        verify(referralDomainService).acceptReferral(referralId, processOpinion);
        verify(referralMapper).toDTO(entity);
        verify(eventPublisher).publishEvent(any());
    }

    @Test
    void testRejectReferral_Success() {
        // Given
        String referralId = "referral-001";
        String processOpinion = "患者病情不符合收治条件";
        
        when(referralFormRepository.findById(referralId)).thenReturn(java.util.Optional.of(entity));
        when(referralDomainService.rejectReferral(referralId, processOpinion)).thenReturn(entity);
        when(referralMapper.toDTO(entity)).thenReturn(dto);

        // When
        ReferralDTO result = referralApplicationService.rejectReferral(referralId, processOpinion);

        // Then
        assertNotNull(result);
        assertEquals("referral-001", result.getId());

        // 验证方法调用
        verify(referralFormRepository).findById(referralId);
        verify(referralDomainService).rejectReferral(referralId, processOpinion);
        verify(referralMapper).toDTO(entity);
        verify(eventPublisher).publishEvent(any());
    }

    @Test
    void testCompleteReferral_Success() {
        // Given
        String referralId = "referral-001";
        String processOpinion = "患者已成功转入";
        
        when(referralFormRepository.findById(referralId)).thenReturn(java.util.Optional.of(entity));
        when(referralDomainService.completeReferral(referralId, processOpinion)).thenReturn(entity);
        when(referralMapper.toDTO(entity)).thenReturn(dto);

        // When
        ReferralDTO result = referralApplicationService.completeReferral(referralId, processOpinion);

        // Then
        assertNotNull(result);
        assertEquals("referral-001", result.getId());

        // 验证方法调用
        verify(referralFormRepository).findById(referralId);
        verify(referralDomainService).completeReferral(referralId, processOpinion);
        verify(referralMapper).toDTO(entity);
        verify(eventPublisher).publishEvent(any());
    }

    @Test
    void testCancelReferral_Success() {
        // Given
        String referralId = "referral-001";
        String reason = "患者要求取消转诊";
        
        when(referralFormRepository.findById(referralId)).thenReturn(java.util.Optional.of(entity));
        when(referralDomainService.cancelReferral(referralId, reason)).thenReturn(entity);
        when(referralMapper.toDTO(entity)).thenReturn(dto);

        // When
        ReferralDTO result = referralApplicationService.cancelReferral(referralId, reason);

        // Then
        assertNotNull(result);
        assertEquals("referral-001", result.getId());

        // 验证方法调用
        verify(referralFormRepository).findById(referralId);
        verify(referralDomainService).cancelReferral(referralId, reason);
        verify(referralMapper).toDTO(entity);
        verify(eventPublisher).publishEvent(any());
    }

    @Test
    void testDeleteReferral_Success() {
        // Given
        String referralId = "referral-001";
        String deleteReason = "数据错误";
        
        when(referralDomainService.deleteReferral(referralId, deleteReason)).thenReturn(true);

        // When
        assertDoesNotThrow(() -> {
            referralApplicationService.deleteReferral(referralId, deleteReason);
        });

        // Then
        verify(referralDomainService).deleteReferral(referralId, deleteReason);
    }
}
