package com.hys.hm.application.patient.service;

import com.hys.hm.application.patient.dto.PatientDTO;
import com.hys.hm.application.patient.dto.PatientQueryDTO;
import com.hys.hm.application.patient.mapper.PatientMapper;
import com.hys.hm.domain.patient.entity.PatientBasicInfoEntity;
import com.hys.hm.domain.patient.repository.PatientBasicInfoRepository;
import com.hys.hm.shared.common.page.PageRequest;
import com.hys.hm.shared.common.page.PageResult;
import com.hys.hm.shared.encrypt.service.FrameworkMaskProcessor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 患者查询服务
 * 供其他模块调用，获取患者相关信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
@Slf4j
public class PatientQueryService {

    private final PatientBasicInfoRepository patientRepository;
    private final PatientMapper patientMapper;

    /**
     * 获取患者基础信息
     */
    public PatientDTO getPatientBasicInfo(String patientId) {
        log.debug("获取患者基础信息: patientId={}", patientId);

        return patientRepository.findById(patientId)
            .map(patientMapper::toDTO)
            .orElse(null);
    }

    /**
     * 批量获取患者基础信息
     */
    public List<PatientDTO> batchGetPatientBasicInfo(List<String> patientIds) {
        log.debug("批量获取患者基础信息: patientIds={}", patientIds);

        if (patientIds == null || patientIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 简化实现，直接使用repository的基础方法并脱敏
        return patientIds.stream()
            .map(patientRepository::findById)
            .filter(Optional::isPresent)
            .map(Optional::get)
            .map(patientMapper::toDTO)
            .collect(Collectors.toList());
    }

    /**
     * 检查患者是否存在
     */
    public boolean existsPatient(String patientId) {
        log.debug("检查患者是否存在: patientId={}", patientId);
        return patientRepository.existsById(patientId);
    }

    /**
     * 根据身份证号查找患者
     */
    public Optional<PatientDTO> findPatientByIdCard(String idCard) {
        log.debug("根据身份证号查找患者: idCard=[MASKED]");

        return patientRepository.findByIdcard(idCard)
            .map(patientMapper::toDTO);
    }

    /**
     * 根据手机号查找患者
     */
    public List<PatientDTO> findPatientsByPhone(String phone) {
        log.debug("根据手机号查找患者: phone=[MASKED]");

        // 简化实现，使用现有的repository方法并脱敏
        return patientRepository.findByPhone(phone)
            .stream()
            .map(patientMapper::toDTO)
            .collect(Collectors.toList());
    }

    /**
     * 分页查询患者
     */
    public PageResult<PatientDTO> queryPatients(PatientQueryDTO queryDTO, PageRequest pageRequest) {
        log.debug("分页查询患者: queryDTO={}, pageRequest={}", queryDTO, pageRequest);

        // 简化实现，暂时返回所有患者的分页结果
        // TODO: 后续实现复杂查询条件
        List<PatientBasicInfoEntity> allPatients = patientRepository.findAll();

        // 简单的内存分页（生产环境应该使用数据库分页）
        int start = pageRequest.getPage() * pageRequest.getSize();
        int end = Math.min(start + pageRequest.getSize(), allPatients.size());

        List<PatientBasicInfoEntity> pageContent = start < allPatients.size() ?
            allPatients.subList(start, end) : new ArrayList<>();

        // 转换为DTO并脱敏
        List<PatientDTO> dtoList = pageContent.stream()
            .map(patientMapper::toDTO)
            .collect(Collectors.toList());

        return PageResult.of(dtoList, pageRequest.getPage() + 1, pageRequest.getSize(), (long) allPatients.size());
    }

    /**
     * 根据姓名模糊查询患者
     */
    public List<PatientDTO> findPatientsByNameLike(String name) {
        log.debug("根据姓名模糊查询患者: name={}", name);

        return patientRepository.findByNameContaining(name)
            .stream()
            .map(patientMapper::toDTO)
            .collect(Collectors.toList());
    }

    /**
     * 根据机构ID查找患者列表
     */
    public List<PatientDTO> findPatientsByOrgId(String orgId) {
        log.debug("根据机构ID查找患者列表: orgId={}", orgId);

        return patientRepository.findByOrgId(orgId)
            .stream()
            .map(patientMapper::toDTO)
            .collect(Collectors.toList());
    }

    /**
     * 根据责任医生查找患者列表
     */
    public List<PatientDTO> findPatientsByDutyDoctor(String dutyDoctor) {
        log.debug("根据责任医生查找患者列表: dutyDoctor={}", dutyDoctor);

        return patientRepository.findByDutyDoctor(dutyDoctor)
            .stream()
            .map(patientMapper::toDTO)
            .collect(Collectors.toList());
    }

    /**
     * 统计患者数量
     */
    public long countPatients() {
        return patientRepository.count();
    }

    /**
     * 统计活跃患者数量（未删除）
     */
    public long countActivePatients() {
        return patientRepository.countNotDeleted();
    }

    /**
     * 脱敏处理辅助方法（现在由切面处理，这个方法可以删除）
     */
    private PatientDTO maskPatientDTO(PatientDTO dto) {
        return dto; // 切面会自动处理脱敏
    }

    /**
     * 根据地址模糊查询患者（加密字段）
     */
    public List<PatientDTO> findPatientsByAddressFuzzy(String addressKeyword) {
        log.debug("根据地址模糊查询患者: keyword=[MASKED]");

        // 简化实现，使用现有的repository方法并脱敏
        // TODO: 实现真正的加密字段模糊查询
        return patientRepository.findByAddressContaining(addressKeyword)
            .stream()
            .map(patientMapper::toDTO)
            .collect(Collectors.toList());
    }



    // TODO: 后续实现复杂查询条件构建方法
}
