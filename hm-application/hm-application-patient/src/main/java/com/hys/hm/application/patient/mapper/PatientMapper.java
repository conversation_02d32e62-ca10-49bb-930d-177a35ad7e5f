package com.hys.hm.application.patient.mapper;

import com.hys.hm.application.patient.dto.PatientCreateDTO;
import com.hys.hm.application.patient.dto.PatientDTO;
import com.hys.hm.application.patient.dto.PatientUpdateDTO;
import com.hys.hm.domain.patient.entity.PatientBasicInfoEntity;
import org.springframework.stereotype.Component;

/**
 * 患者对象映射器
 * 负责DTO与Entity之间的转换
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Component
public class PatientMapper {

    /**
     * 将Entity转换为DTO
     */
    public PatientDTO toDTO(PatientBasicInfoEntity entity) {
        if (entity == null) {
            return null;
        }

        PatientDTO dto = new PatientDTO();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setSex(entity.getSex());
        dto.setBirthday(entity.getBirthday());
        dto.setIdcard(entity.getIdcard());
        dto.setPhone(entity.getPhone());
        dto.setAddress(entity.getAddress());
        dto.setRegisterAddress(entity.getRegisterAddress());
        dto.setDutyDoctor(entity.getDutyDoctor());
        dto.setDutyDoctorName(entity.getDutyDoctorName());
        dto.setDutyDoctorPhone(entity.getDutyDoctorPhone());
        dto.setOrgId(entity.getOrgId());
        dto.setOrgName(entity.getOrgName());
        dto.setBuildDate(entity.getBuildDate());
        dto.setHeight(entity.getHeight());
        dto.setWeight(entity.getWeight());
        dto.setBmi(entity.getBmi());
        dto.setCompany(entity.getCompany());
        dto.setOccupation(entity.getVocation());
        dto.setEducation(entity.getEducation());
        dto.setMaritalStatus(entity.getMaritalStatus());
        dto.setPaymentMethod(entity.getPayType());
        dto.setAllergyHistory(entity.getAllergyHistory());
        dto.setPastHistory(entity.getDiseaseHistory());
        dto.setFamilyHistory(entity.getFatherHistory()); // 简化处理，实际可能需要组合多个字段
        dto.setCommunity(entity.getCommunity());
        dto.setBuilding(entity.getBuilding());
        dto.setUnit(entity.getUnit());
        dto.setHousehold(entity.getHousehold());
        dto.setCrowdAttribute(entity.getCrowdAttribute());
        dto.setEmphasisCrowd(entity.getEmphasisCrowd());
        dto.setRecordStatus(entity.getRecordStatus());
        dto.setSignState(entity.getSignState());
        dto.setManagerStatus(entity.getManagerStatus());
        dto.setPinyinName(entity.getPinyinName());
        dto.setCreateTime(entity.getCreateTime());
        dto.setUpdateTime(entity.getUpdateTime());
        dto.setCreateBy(entity.getCreateBy());
        dto.setUpdateBy(entity.getUpdateBy());
        dto.setRemark(entity.getRemark());

        return dto;
    }

    /**
     * 将CreateDTO转换为Entity
     */
    public PatientBasicInfoEntity toEntity(PatientCreateDTO createDTO) {
        if (createDTO == null) {
            return null;
        }

        PatientBasicInfoEntity entity = new PatientBasicInfoEntity();
        entity.setName(createDTO.getName());
        entity.setSex(createDTO.getSex());
        entity.setBirthday(createDTO.getBirthday());
        entity.setIdcard(createDTO.getIdcard());
        entity.setPhone(createDTO.getPhone());
        entity.setAddress(createDTO.getAddress());
        entity.setRegisterAddress(createDTO.getRegisterAddress());
        entity.setDutyDoctor(createDTO.getDutyDoctor());
        entity.setDutyDoctorName(createDTO.getDutyDoctorName());
        entity.setDutyDoctorPhone(createDTO.getDutyDoctorPhone());
        entity.setOrgId(createDTO.getOrgId());
        entity.setOrgName(createDTO.getOrgName());
        entity.setHeight(createDTO.getHeight());
        entity.setWeight(createDTO.getWeight());
        entity.setCompany(createDTO.getCompany());
        entity.setVocation(createDTO.getOccupation());
        entity.setEducation(createDTO.getEducation());
        entity.setMaritalStatus(createDTO.getMaritalStatus());
        entity.setPayType(createDTO.getPaymentMethod());
        entity.setAllergyHistory(createDTO.getAllergyHistory());
        entity.setDiseaseHistory(createDTO.getPastHistory());
        entity.setFatherHistory(createDTO.getFamilyHistory()); // 简化处理
        entity.setCommunity(createDTO.getCommunity());
        entity.setBuilding(createDTO.getBuilding());
        entity.setUnit(createDTO.getUnit());
        entity.setHousehold(createDTO.getHousehold());
        entity.setCrowdAttribute(createDTO.getCrowdAttribute());
        entity.setEmphasisCrowd(createDTO.getEmphasisCrowd());
        entity.setRemark(createDTO.getRemark());

        return entity;
    }

    /**
     * 将UpdateDTO的数据更新到Entity
     */
    public void updateEntity(PatientBasicInfoEntity entity, PatientUpdateDTO updateDTO) {
        if (entity == null || updateDTO == null) {
            return;
        }

        if (updateDTO.getName() != null) {
            entity.setName(updateDTO.getName());
        }
        if (updateDTO.getSex() != null) {
            entity.setSex(updateDTO.getSex());
        }
        if (updateDTO.getBirthday() != null) {
            entity.setBirthday(updateDTO.getBirthday());
        }
        if (updateDTO.getPhone() != null) {
            entity.setPhone(updateDTO.getPhone());
        }
        if (updateDTO.getAddress() != null) {
            entity.setAddress(updateDTO.getAddress());
        }
        if (updateDTO.getRegisterAddress() != null) {
            entity.setRegisterAddress(updateDTO.getRegisterAddress());
        }
        if (updateDTO.getDutyDoctor() != null) {
            entity.setDutyDoctor(updateDTO.getDutyDoctor());
        }
        if (updateDTO.getDutyDoctorName() != null) {
            entity.setDutyDoctorName(updateDTO.getDutyDoctorName());
        }
        if (updateDTO.getDutyDoctorPhone() != null) {
            entity.setDutyDoctorPhone(updateDTO.getDutyDoctorPhone());
        }
        if (updateDTO.getHeight() != null) {
            entity.setHeight(updateDTO.getHeight());
        }
        if (updateDTO.getWeight() != null) {
            entity.setWeight(updateDTO.getWeight());
        }
        if (updateDTO.getCompany() != null) {
            entity.setCompany(updateDTO.getCompany());
        }
        if (updateDTO.getOccupation() != null) {
            entity.setVocation(updateDTO.getOccupation());
        }
        if (updateDTO.getEducation() != null) {
            entity.setEducation(updateDTO.getEducation());
        }
        if (updateDTO.getMaritalStatus() != null) {
            entity.setMaritalStatus(updateDTO.getMaritalStatus());
        }
        if (updateDTO.getPaymentMethod() != null) {
            entity.setPayType(updateDTO.getPaymentMethod());
        }
        if (updateDTO.getAllergyHistory() != null) {
            entity.setAllergyHistory(updateDTO.getAllergyHistory());
        }
        if (updateDTO.getPastHistory() != null) {
            entity.setDiseaseHistory(updateDTO.getPastHistory());
        }
        if (updateDTO.getFamilyHistory() != null) {
            entity.setFatherHistory(updateDTO.getFamilyHistory());
        }
        if (updateDTO.getCommunity() != null) {
            entity.setCommunity(updateDTO.getCommunity());
        }
        if (updateDTO.getBuilding() != null) {
            entity.setBuilding(updateDTO.getBuilding());
        }
        if (updateDTO.getUnit() != null) {
            entity.setUnit(updateDTO.getUnit());
        }
        if (updateDTO.getHousehold() != null) {
            entity.setHousehold(updateDTO.getHousehold());
        }
        if (updateDTO.getCrowdAttribute() != null) {
            entity.setCrowdAttribute(updateDTO.getCrowdAttribute());
        }
        if (updateDTO.getEmphasisCrowd() != null) {
            entity.setEmphasisCrowd(updateDTO.getEmphasisCrowd());
        }
        if (updateDTO.getRecordStatus() != null) {
            entity.setRecordStatus(updateDTO.getRecordStatus());
        }
        if (updateDTO.getSignState() != null) {
            entity.setSignState(updateDTO.getSignState());
        }
        if (updateDTO.getManagerStatus() != null) {
            entity.setManagerStatus(updateDTO.getManagerStatus());
        }
        if (updateDTO.getRemark() != null) {
            entity.setRemark(updateDTO.getRemark());
        }
    }
}
