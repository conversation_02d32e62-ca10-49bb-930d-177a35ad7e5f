package com.hys.hm.application.patient.dto;

import com.hys.hm.shared.encrypt.annotation.EncryptField;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 患者DTO
 * 用于返回患者信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Data
public class PatientDTO {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别
     */
    private String sex;

    /**
     * 出生日期
     */
    private LocalDateTime birthday;

    /**
     * 身份证号
     */
    @EncryptField(type = EncryptField.EncryptType.AES, maskLevel = EncryptField.MaskLevel.PARTIAL, description = "身份证号")
    private String idcard;

    /**
     * 联系电话
     */
    @EncryptField(type = EncryptField.EncryptType.AES, maskLevel = EncryptField.MaskLevel.PARTIAL, description = "联系电话")
    private String phone;

    /**
     * 地址
     */
    @EncryptField(type = EncryptField.EncryptType.AES, fuzzySearch = true, tokenLength = 2, maskLevel = EncryptField.MaskLevel.PARTIAL, description = "现住址")
    private String address;

    /**
     * 户籍地址
     */
    @EncryptField(type = EncryptField.EncryptType.AES, fuzzySearch = true, tokenLength = 2, maskLevel = EncryptField.MaskLevel.PARTIAL, description = "户籍地址")
    private String registerAddress;

    /**
     * 责任医生ID
     */
    private String dutyDoctor;

    /**
     * 责任医生姓名
     */
    private String dutyDoctorName;

    /**
     * 责任医生电话
     */
    private String dutyDoctorPhone;

    /**
     * 机构ID
     */
    private String orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 建档日期
     */
    private LocalDateTime buildDate;

    /**
     * 身高
     */
    private Double height;

    /**
     * 体重
     */
    private Double weight;

    /**
     * BMI指数
     */
    private Double bmi;

    /**
     * 工作单位
     */
    private String company;

    /**
     * 职业
     */
    private String occupation;

    /**
     * 文化程度
     */
    private String education;

    /**
     * 婚姻状况
     */
    private String maritalStatus;

    /**
     * 医疗费用支付方式
     */
    private String paymentMethod;

    /**
     * 药物过敏史
     */
    private String allergyHistory;

    /**
     * 既往病史
     */
    private String pastHistory;

    /**
     * 家族病史
     */
    private String familyHistory;

    /**
     * 社区
     */
    private String community;

    /**
     * 楼栋
     */
    private String building;

    /**
     * 单元
     */
    private String unit;

    /**
     * 户号
     */
    private String household;

    /**
     * 人群属性
     */
    private String crowdAttribute;

    /**
     * 重点人群
     */
    private String emphasisCrowd;

    /**
     * 档案状态
     */
    private String recordStatus;

    /**
     * 签约状态
     */
    private String signState;

    /**
     * 管理状态
     */
    private String managerStatus;

    /**
     * 拼音姓名
     */
    private String pinyinName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建者ID
     */
    private String createBy;

    /**
     * 更新者ID
     */
    private String updateBy;

    /**
     * 备注
     */
    private String remark;
}
