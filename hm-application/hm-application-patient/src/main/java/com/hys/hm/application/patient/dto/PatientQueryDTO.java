package com.hys.hm.application.patient.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 患者查询DTO
 * 用于接收查询患者的条件
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Data
public class PatientQueryDTO {

    /**
     * 姓名（模糊查询）
     */
    private String name;

    /**
     * 性别
     */
    private String sex;

    /**
     * 身份证号
     */
    private String idcard;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 责任医生ID
     */
    private String dutyDoctor;

    /**
     * 责任医生姓名
     */
    private String dutyDoctorName;

    /**
     * 机构ID
     */
    private String orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 档案状态
     */
    private String recordStatus;

    /**
     * 签约状态
     */
    private String signState;

    /**
     * 管理状态
     */
    private String managerStatus;

    /**
     * 人群属性
     */
    private String crowdAttribute;

    /**
     * 重点人群
     */
    private String emphasisCrowd;

    /**
     * 社区
     */
    private String community;

    /**
     * 街道编码
     */
    private String streetCode;

    /**
     * 居委会编码
     */
    private String committeeCode;

    /**
     * 建档开始日期
     */
    private LocalDateTime buildDateStart;

    /**
     * 建档结束日期
     */
    private LocalDateTime buildDateEnd;

    /**
     * 年龄范围-最小值
     */
    private Integer minAge;

    /**
     * 年龄范围-最大值
     */
    private Integer maxAge;

    /**
     * BMI范围-最小值
     */
    private Double minBmi;

    /**
     * BMI范围-最大值
     */
    private Double maxBmi;

    /**
     * 是否有过敏史
     */
    private Boolean hasAllergyHistory;

    /**
     * 是否有家族病史
     */
    private Boolean hasFamilyHistory;

    /**
     * 地址关键词（模糊查询）
     */
    private String addressKeyword;

    /**
     * 创建开始时间
     */
    private LocalDateTime createTimeStart;

    /**
     * 创建结束时间
     */
    private LocalDateTime createTimeEnd;
}
