package com.hys.hm.application.patient.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 患者创建DTO
 * 用于接收创建患者的请求数据
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Data
public class PatientCreateDTO {

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空")
    private String name;

    /**
     * 性别
     */
    @Pattern(regexp = "^[男女]$", message = "性别只能是男或女")
    private String sex;

    /**
     * 出生日期
     */
    private LocalDateTime birthday;

    /**
     * 身份证号
     */
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx]$", 
             message = "身份证号格式不正确")
    private String idcard;

    /**
     * 联系电话
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    /**
     * 地址
     */
    private String address;

    /**
     * 户籍地址
     */
    private String registerAddress;

    /**
     * 责任医生ID
     */
    private String dutyDoctor;

    /**
     * 责任医生姓名
     */
    private String dutyDoctorName;

    /**
     * 责任医生电话
     */
    private String dutyDoctorPhone;

    /**
     * 机构ID
     */
    private String orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 身高
     */
    private Double height;

    /**
     * 体重
     */
    private Double weight;

    /**
     * 工作单位
     */
    private String company;

    /**
     * 职业
     */
    private String occupation;

    /**
     * 文化程度
     */
    private String education;

    /**
     * 婚姻状况
     */
    private String maritalStatus;

    /**
     * 医疗费用支付方式
     */
    private String paymentMethod;

    /**
     * 药物过敏史
     */
    private String allergyHistory;

    /**
     * 既往病史
     */
    private String pastHistory;

    /**
     * 家族病史
     */
    private String familyHistory;

    /**
     * 社区
     */
    private String community;

    /**
     * 楼栋
     */
    private String building;

    /**
     * 单元
     */
    private String unit;

    /**
     * 户号
     */
    private String household;

    /**
     * 人群属性
     */
    private String crowdAttribute;

    /**
     * 重点人群
     */
    private String emphasisCrowd;

    /**
     * 备注
     */
    private String remark;
}
