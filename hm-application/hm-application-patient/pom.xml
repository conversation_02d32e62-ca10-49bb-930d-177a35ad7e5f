<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.hys</groupId>
        <artifactId>hm-application</artifactId>
        <version>3.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>hm-application-patient</artifactId>
    <name>hm-application-patient</name>
    <description>患者应用服务模块</description>

    <dependencies>
        <!-- 患者领域模块 -->
        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-domain-patient</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- 共享框架 -->
        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-shared-framework</artifactId>
        </dependency>

        <!-- 共享通用模块 -->
        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-shared-common</artifactId>
        </dependency>

        <!-- 共享类型模块 -->
        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-shared-types</artifactId>
        </dependency>

        <!-- Spring Boot Starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <!-- Spring Boot Validation -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>

</project>
