# 项目目录结构

```
hm-health-management/
├── hm-bootstrap/                                    # 启动模块
│   ├── src/main/java/com/hys/hm/bootstrap/
│   │   ├── HealthManagementApplication.java
│   │   └── config/
│   │       └── ApplicationConfiguration.java
│   ├── src/main/resources/
│   │   ├── application.yml
│   │   └── application-dev.yml
│   └── pom.xml
│
├── hm-interfaces/                                   # 接口层
│   ├── hm-interfaces-web/                          # Web控制器
│   │   ├── src/main/java/com/hys/hm/interfaces/web/
│   │   │   ├── controller/
│   │   │   │   ├── referral/
│   │   │   │   │   └── ReferralController.java
│   │   │   │   ├── patient/
│   │   │   │   │   └── PatientController.java
│   │   │   │   └── shared/
│   │   │   │       └── OverviewController.java
│   │   │   ├── config/
│   │   │   │   └── WebConfiguration.java
│   │   │   └── exception/
│   │   │       └── GlobalExceptionHandler.java
│   │   └── pom.xml
│   │
│   └── hm-interfaces-api/                          # 对外API定义
│       ├── src/main/java/com/hys/hm/interfaces/api/
│       │   └── dto/
│       │       ├── referral/
│       │       └── patient/
│       └── pom.xml
│
├── hm-application/                                  # 应用服务层
│   ├── hm-application-referral/                    # 转诊应用服务
│   │   ├── src/main/java/com/hys/hm/application/referral/
│   │   │   ├── service/
│   │   │   │   ├── ReferralApplicationService.java
│   │   │   │   └── ReferralQueryApplicationService.java
│   │   │   ├── dto/
│   │   │   │   ├── ReferralCreateDTO.java
│   │   │   │   ├── ReferralUpdateDTO.java
│   │   │   │   └── ReferralQueryDTO.java
│   │   │   ├── converter/
│   │   │   │   └── ReferralDTOConverter.java
│   │   │   ├── listener/
│   │   │   │   └── ReferralEventListener.java
│   │   │   └── config/
│   │   │       └── ReferralApplicationConfiguration.java
│   │   └── pom.xml
│   │
│   ├── hm-application-patient/                     # 患者应用服务
│   ├── hm-application-health/                      # 健康应用服务
│   ├── hm-application-followup/                    # 随访应用服务
│   ├── hm-application-knowledge/                   # 知识库应用服务
│   └── hm-application-shared/                      # 共享应用服务
│       ├── src/main/java/com/hys/hm/application/shared/
│       │   └── service/
│       │       └── OverviewApplicationService.java
│       └── pom.xml
│
├── hm-domain/                                       # 领域层
│   ├── hm-domain-referral/                        # 转诊领域
│   │   ├── src/main/java/com/hys/hm/domain/referral/
│   │   │   ├── model/
│   │   │   │   ├── ReferralForm.java
│   │   │   │   ├── PatientInfo.java
│   │   │   │   ├── HospitalInfo.java
│   │   │   │   └── ReferralStatus.java
│   │   │   ├── service/
│   │   │   │   ├── ReferralDomainService.java
│   │   │   │   └── ReferralQueryService.java
│   │   │   ├── repository/
│   │   │   │   └── ReferralRepository.java
│   │   │   └── event/
│   │   │       ├── ReferralCreatedEvent.java
│   │   │       └── ReferralStatusChangedEvent.java
│   │   └── pom.xml
│   │
│   ├── hm-domain-patient/                          # 患者领域
│   ├── hm-domain-health/                           # 健康领域
│   ├── hm-domain-followup/                         # 随访领域
│   ├── hm-domain-knowledge/                        # 知识库领域
│   └── hm-domain-shared/                           # 共享领域
│       ├── src/main/java/com/hys/hm/domain/shared/
│       │   ├── event/
│       │   │   └── DomainEvent.java
│       │   └── valueobject/
│       │       ├── PersonalInfo.java
│       │       └── ContactInfo.java
│       └── pom.xml
│
├── hm-infrastructure/                               # 基础设施层
│   ├── hm-infrastructure-persistence-referral/     # 转诊持久化
│   │   ├── src/main/java/com/hys/hm/infrastructure/persistence/referral/
│   │   │   ├── entity/
│   │   │   │   ├── ReferralFormEntity.java
│   │   │   │   └── ReferralPatientInfoEntity.java
│   │   │   ├── repository/
│   │   │   │   ├── ReferralFormJpaRepository.java
│   │   │   │   ├── ReferralRepositoryImpl.java
│   │   │   │   └── ReferralPatientInfoRepository.java
│   │   │   ├── converter/
│   │   │   │   └── ReferralEntityConverter.java
│   │   │   └── config/
│   │   │       └── ReferralPersistenceConfiguration.java
│   │   └── pom.xml
│   │
│   ├── hm-infrastructure-persistence-patient/      # 患者持久化
│   ├── hm-infrastructure-persistence-health/       # 健康持久化
│   ├── hm-infrastructure-persistence-followup/     # 随访持久化
│   ├── hm-infrastructure-persistence-knowledge/    # 知识库持久化
│   ├── hm-infrastructure-external/                 # 外部系统集成
│   ├── hm-infrastructure-ai/                       # AI服务集成
│   └── hm-infrastructure-logging/                  # 日志服务
│
└── hm-shared/                                       # 共享内核
    ├── hm-shared-framework/                        # 技术框架
    │   ├── src/main/java/com/hys/hm/shared/framework/
    │   │   ├── base/
    │   │   ├── query/
    │   │   ├── page/
    │   │   └── config/
    │   └── pom.xml
    │
    ├── hm-shared-common/                           # 通用工具
    │   ├── src/main/java/com/hys/hm/shared/common/
    │   │   ├── util/
    │   │   ├── constant/
    │   │   └── exception/
    │   └── pom.xml
    │
    ├── hm-shared-types/                            # 通用类型
    │   ├── src/main/java/com/hys/hm/shared/types/
    │   │   ├── dto/
    │   │   │   ├── PatientBasicInfoDTO.java
    │   │   │   ├── ReferralSummaryDTO.java
    │   │   │   └── HospitalInfoDTO.java
    │   │   ├── enums/
    │   │   │   ├── ReferralStatus.java
    │   │   │   └── UrgencyLevel.java
    │   │   └── valueobject/
    │   └── pom.xml
    │
    └── hm-shared-events/                           # 事件定义
        ├── src/main/java/com/hys/hm/shared/events/
        │   ├── DomainEvent.java
        │   ├── EventPublisher.java
        │   ├── patient/
        │   │   ├── PatientCreatedEvent.java
        │   │   └── PatientUpdatedEvent.java
        │   ├── referral/
        │   │   ├── ReferralCreatedEvent.java
        │   │   └── ReferralStatusChangedEvent.java
        │   └── config/
        │       └── EventConfiguration.java
        └── pom.xml
```
