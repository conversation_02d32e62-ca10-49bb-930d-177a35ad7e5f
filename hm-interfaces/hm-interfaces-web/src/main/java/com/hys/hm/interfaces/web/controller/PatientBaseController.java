package com.hys.hm.interfaces.web.controller;

import com.hys.hm.application.patient.dto.*;
import com.hys.hm.application.patient.service.PatientApplicationService;
import com.hys.hm.application.patient.service.PatientQueryService;
import com.hys.hm.domain.patient.entity.PatientBasicInfoEntity;
import com.hys.hm.domain.patient.service.PatientBasicInfoService;
import com.hys.hm.shared.common.Result;
import com.hys.hm.shared.framework.controller.BaseController;
import com.hys.hm.shared.common.page.PageRequest;
import com.hys.hm.shared.common.page.PageResult;
import com.hys.hm.shared.common.query.QueryConditionParser;
import com.hys.hm.shared.logging.annotation.Log;
import com.hys.hm.shared.logging.enums.BusinessType;
import com.hys.hm.shared.logging.enums.PlatformType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;
import java.util.Optional;

/**
 * 患者管理控制器（BaseController版本）
 * 继承BaseController，充分利用框架提供的通用功能
 * 同时集成透明加密功能
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Slf4j
@RestController
@RequestMapping("/api/v3/patients")
@Tag(name = "患者管理（完整版）", description = "患者基本信息的完整CRUD操作（基于BaseController + 透明加密）")
public class PatientBaseController extends BaseController<PatientBasicInfoEntity, String> {

    private final PatientApplicationService patientApplicationService;
    private final PatientQueryService patientQueryService;

    public PatientBaseController(
            PatientBasicInfoService patientService,
            QueryConditionParser queryConditionParser,
            PatientApplicationService patientApplicationService,
            PatientQueryService patientQueryService) {
        super(patientService, queryConditionParser);
        this.patientApplicationService = patientApplicationService;
        this.patientQueryService = patientQueryService;
    }

    // BaseController已经提供了以下完整的REST API功能：
    // - POST /api/v3/patients (创建)
    // - POST /api/v3/patients/batch (批量创建)
    // - PUT /api/v3/patients/{id} (更新)
    // - PUT /api/v3/patients/batch (批量更新)
    // - DELETE /api/v3/patients/{id} (删除)
    // - DELETE /api/v3/patients/batch (批量删除)
    // - DELETE /api/v3/patients/{id}/soft (软删除)
    // - DELETE /api/v3/patients/batch/soft (批量软删除)
    // - POST /api/v3/patients/{id}/restore (恢复软删除)
    // - GET /api/v3/patients/{id} (根据ID查询)
    // - GET /api/v3/patients/ids (批量查询)
    // - GET /api/v3/patients (分页查询)
    // - GET /api/v3/patients/all (查询所有)
    // - GET /api/v3/patients/count (统计数量) ← 这个就是被误删的重要接口！
    // - GET /api/v3/patients/{id}/exists (检查是否存在)

    /**
     * 根据身份证号查询患者（加密查询）
     */
    @Operation(summary = "根据身份证号查询（加密）", description = "根据身份证号查询患者基本信息（支持加密字段查询）")
    @GetMapping("/by-idcard/{idcard}")
    @ResponseBody
    public Result<PatientDTO> getPatientByIdcard(
            @Parameter(description = "身份证号") @PathVariable String idcard) {
        try {
            // 使用加密查询服务
            Optional<PatientDTO> patient = patientQueryService.findPatientByIdCard(idcard);
            if (patient.isPresent()) {
                return Result.success(patient.get());
            } else {
                return Result.error("患者不存在");
            }
        } catch (Exception e) {
            log.error("根据身份证号查询患者失败: {}", e.getMessage(), e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据手机号查询患者（加密查询）
     */
    @Operation(summary = "根据手机号查询（加密）", description = "根据手机号查询患者列表（支持加密字段查询）")
    @GetMapping("/by-phone/{phone}")
    @ResponseBody
    public Result<List<PatientDTO>> getPatientsByPhone(
            @Parameter(description = "手机号") @PathVariable String phone) {
        try {
            // 使用加密查询服务
            List<PatientDTO> patients = patientQueryService.findPatientsByPhone(phone);
            return Result.success(patients);
        } catch (Exception e) {
            log.error("根据手机号查询患者失败: {}", e.getMessage(), e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据姓名模糊查询患者
     */
    @Operation(summary = "根据姓名模糊查询", description = "根据姓名模糊查询患者列表")
    @GetMapping("/by-name/{name}")
    @ResponseBody
    public Result<List<PatientDTO>> getPatientsByNameLike(
            @Parameter(description = "姓名") @PathVariable String name) {
        try {
            List<PatientDTO> patients = patientQueryService.findPatientsByNameLike(name);
            return Result.success(patients);
        } catch (Exception e) {
            log.error("根据姓名模糊查询患者失败: {}", e.getMessage(), e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据机构ID查询患者
     */
    @Operation(summary = "根据机构ID查询", description = "根据机构ID查询患者列表")
    @GetMapping("/by-org/{orgId}")
    @ResponseBody
    public Result<List<PatientDTO>> getPatientsByOrgId(
            @Parameter(description = "机构ID") @PathVariable String orgId) {
        try {
            List<PatientDTO> patients = patientQueryService.findPatientsByOrgId(orgId);
            return Result.success(patients);
        } catch (Exception e) {
            log.error("根据机构ID查询患者失败: {}", e.getMessage(), e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据责任医生查询患者
     */
    @Operation(summary = "根据责任医生查询", description = "根据责任医生查询患者列表")
    @GetMapping("/by-doctor/{dutyDoctor}")
    @ResponseBody
    public Result<List<PatientDTO>> getPatientsByDutyDoctor(
            @Parameter(description = "责任医生ID") @PathVariable String dutyDoctor) {
        try {
            List<PatientDTO> patients = patientQueryService.findPatientsByDutyDoctor(dutyDoctor);
            return Result.success(patients);
        } catch (Exception e) {
            log.error("根据责任医生查询患者失败: {}", e.getMessage(), e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 高级查询患者
     */
    @Operation(summary = "高级查询患者", description = "根据复杂条件查询患者列表")
    @PostMapping("/search")
    @ResponseBody
    public Result<PageResult<PatientDTO>> searchPatients(
            @Valid @RequestBody PatientQueryDTO queryDTO,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "排序") @RequestParam(required = false) String sort) {
        try {
            PageRequest pageRequest = PageRequest.of(page, size);
            if (sort != null) {
                pageRequest.setOrderString(sort);
            }

            PageResult<PatientDTO> result = patientQueryService.queryPatients(queryDTO, pageRequest);
            return Result.success(result);
        } catch (Exception e) {
            log.error("高级查询患者失败: {}", e.getMessage(), e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据地址模糊查询患者（加密查询）
     */
    @Operation(summary = "根据地址模糊查询（加密）", description = "根据地址关键词模糊查询患者列表（支持加密字段查询）")
    @GetMapping("/by-address/{keyword}")
    @ResponseBody
    public Result<List<PatientDTO>> getPatientsByAddressFuzzy(
            @Parameter(description = "地址关键词") @PathVariable String keyword) {
        try {
            List<PatientDTO> patients = patientQueryService.findPatientsByAddressFuzzy(keyword);
            return Result.success(patients);
        } catch (Exception e) {
            log.error("根据地址模糊查询患者失败: {}", e.getMessage(), e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 统计活跃患者数量
     */
    @Operation(summary = "统计活跃患者数量", description = "统计未删除的患者数量")
    @GetMapping("/count/active")
    @ResponseBody
    public Result<Long> countActivePatients() {
        try {
            long count = patientQueryService.countActivePatients();
            return Result.success(count);
        } catch (Exception e) {
            log.error("统计活跃患者数量失败: {}", e.getMessage(), e);
            return Result.error("统计失败: " + e.getMessage());
        }
    }

    /**
     * 计算并更新BMI
     */
    @Operation(summary = "计算并更新BMI", description = "根据身高体重计算并更新BMI")
    @PutMapping("/{id}/bmi")
    @ResponseBody
    @Log(title = "患者管理-更新BMI", businessType = BusinessType.UPDATE, platformType = PlatformType.HM)
    public Result<PatientDTO> calculateAndUpdateBmi(
            @Parameter(description = "患者ID") @PathVariable String id) {
        try {
            PatientDTO patient = patientApplicationService.calculateAndUpdateBmi(id);
            return Result.success(patient);
        } catch (Exception e) {
            log.error("计算并更新BMI失败: {}", e.getMessage(), e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 使用DTO创建患者（业务友好的接口）
     */
    @Operation(summary = "创建患者（DTO）", description = "使用DTO创建新的患者")
    @PostMapping("/dto")
    @ResponseBody
    @Log(title = "患者管理-创建患者", businessType = BusinessType.INSERT, platformType = PlatformType.HM)
    public Result<PatientDTO> createPatientWithDTO(
            @Valid @RequestBody PatientCreateDTO createDTO) {
        try {
            PatientDTO patient = patientApplicationService.createPatient(createDTO);
            return Result.success(patient);
        } catch (Exception e) {
            log.error("创建患者失败: {}", e.getMessage(), e);
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 使用DTO更新患者（业务友好的接口）
     */
    @Operation(summary = "更新患者（DTO）", description = "使用DTO更新患者信息")
    @PutMapping("/{id}/dto")
    @ResponseBody
    @Log(title = "患者管理-更新患者", businessType = BusinessType.UPDATE, platformType = PlatformType.HM)
    public Result<PatientDTO> updatePatientWithDTO(
            @Parameter(description = "患者ID") @PathVariable String id,
            @Valid @RequestBody PatientUpdateDTO updateDTO) {
        try {
            PatientDTO patient = patientApplicationService.updatePatient(id, updateDTO);
            return Result.success(patient);
        } catch (Exception e) {
            log.error("更新患者失败: {}", e.getMessage(), e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 批量导入患者（业务友好的接口）
     */
    @Operation(summary = "批量导入患者", description = "批量导入患者基本信息")
    @PostMapping("/import")
    @ResponseBody
    @Log(title = "患者管理-批量导入", businessType = BusinessType.IMPORT, platformType = PlatformType.HM)
    public Result<List<PatientDTO>> batchImportPatients(
            @Valid @RequestBody List<PatientCreateDTO> createDTOList) {
        try {
            List<PatientDTO> patients = patientApplicationService.batchImportPatients(createDTOList);
            return Result.success(patients);
        } catch (Exception e) {
            log.error("批量导入患者失败: {}", e.getMessage(), e);
            return Result.error("导入失败: " + e.getMessage());
        }
    }

    /**
     * 获取实体的ID
     * BaseController要求实现的抽象方法
     */
    @Override
    protected String getEntityId(PatientBasicInfoEntity entity) {
        return entity != null ? entity.getId() : null;
    }

    /**
     * 设置实体的ID
     * BaseController要求实现的抽象方法
     */
    @Override
    protected void setEntityId(PatientBasicInfoEntity entity, String id) {
        if (entity != null) {
            entity.setId(id);
        }
    }
}
