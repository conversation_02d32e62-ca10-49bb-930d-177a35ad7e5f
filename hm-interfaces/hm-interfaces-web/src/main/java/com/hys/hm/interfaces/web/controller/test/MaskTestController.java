package com.hys.hm.interfaces.web.controller.test;

import com.hys.hm.application.patient.dto.PatientDTO;
import com.hys.hm.shared.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 脱敏测试控制器
 * 用于测试脱敏功能是否正常工作
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Slf4j
@RestController
@RequestMapping("/api/test/mask")
@Tag(name = "脱敏测试", description = "测试脱敏功能的接口")
public class MaskTestController {

    @Operation(summary = "测试单个患者脱敏", description = "返回单个患者数据，测试脱敏功能")
    @GetMapping("/single-patient")
    public ResponseEntity<Result<PatientDTO>> testSinglePatient() {
        log.info("测试单个患者脱敏");
        
        PatientDTO patient = createTestPatient("test-001", "张三测试");
        
        return ResponseEntity.ok(Result.success(patient));
    }

    @Operation(summary = "测试患者列表脱敏", description = "返回患者列表，测试脱敏功能")
    @GetMapping("/patient-list")
    public ResponseEntity<Result<List<PatientDTO>>> testPatientList() {
        log.info("测试患者列表脱敏");
        
        List<PatientDTO> patients = Arrays.asList(
            createTestPatient("test-001", "张三测试"),
            createTestPatient("test-002", "李四测试"),
            createTestPatient("test-003", "王五测试")
        );
        
        return ResponseEntity.ok(Result.success(patients));
    }

    @Operation(summary = "测试直接返回患者", description = "直接返回患者对象，不包装Result")
    @GetMapping("/direct-patient")
    public PatientDTO testDirectPatient() {
        log.info("测试直接返回患者");
        
        return createTestPatient("test-direct", "直接返回测试");
    }

    /**
     * 创建测试患者数据
     */
    private PatientDTO createTestPatient(String id, String name) {
        PatientDTO patient = new PatientDTO();
        patient.setId(id);
        patient.setName(name);
        patient.setIdcard("110101199001011234");
        patient.setPhone("13800138000");
        patient.setAddress("北京市朝阳区某某街道123号");
        patient.setRegisterAddress("北京市海淀区某某路456号");
        patient.setSex("男");
        patient.setBirthday(LocalDateTime.of(1990, 1, 1, 0, 0));
        patient.setOrgId("org-001");
        patient.setOrgName("测试医院");
        patient.setDutyDoctor("doctor-001");
        patient.setDutyDoctorName("张医生");
        patient.setDutyDoctorPhone("13900139000");
        patient.setCreateTime(LocalDateTime.now());
        patient.setUpdateTime(LocalDateTime.now());
        
        return patient;
    }
}
