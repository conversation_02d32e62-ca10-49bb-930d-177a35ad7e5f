package com.hys.hm.interfaces.web.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户档案DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-30
 */
@Data
@Schema(description = "用户档案信息")
public class UserProfileDTO {

    @Schema(description = "主键ID")
    private String id;

    @Schema(description = "姓名", required = true)
    private String name;

    @Schema(description = "性别：1-男，2-女")
    private String sex;

    @Schema(description = "出生日期")
    private LocalDateTime birthday;

    @Schema(description = "身份证号")
    private String idcard;

    @Schema(description = "联系电话")
    private String phone;

    @Schema(description = "现住址")
    private String address;

    @Schema(description = "户籍地址")
    private String registerAddress;

    @Schema(description = "街道")
    private String street;

    @Schema(description = "街道编码")
    private String streetCode;

    @Schema(description = "居委会")
    private String residentsCommittee;

    @Schema(description = "机构名称")
    private String orgName;

    @Schema(description = "机构ID")
    private String orgId;

    @Schema(description = "责任医生姓名")
    private String dutyDoctorName;

    @Schema(description = "责任医生ID")
    private String dutyDoctor;

    @Schema(description = "责任医生电话")
    private String dutyDoctorPhone;

    @Schema(description = "建档日期")
    private LocalDateTime buildDate;

    @Schema(description = "纸质档案号")
    private String paperArchiveNo;

    @Schema(description = "工作单位")
    private String company;

    @Schema(description = "联系人")
    private String linkman;

    @Schema(description = "联系人电话")
    private String linkmanPhone;

    @Schema(description = "常住类型")
    private String residentType;

    @Schema(description = "民族")
    private String nation;

    @Schema(description = "民族名称")
    private String nationName;

    @Schema(description = "血型")
    private String bloodType;

    @Schema(description = "RH血型")
    private String bloodRh;

    @Schema(description = "文化程度")
    private String education;

    @Schema(description = "职业")
    private String vocation;

    @Schema(description = "婚姻状况")
    private String maritalStatus;

    @Schema(description = "医疗费用支付方式")
    private String payType;

    @Schema(description = "药物过敏史")
    private String allergyHistory;

    @Schema(description = "暴露史")
    private String exposure;

    @Schema(description = "疾病史")
    private String diseaseHistory;

    @Schema(description = "手术史")
    private String operativeHistory;

    @Schema(description = "外伤史")
    private String traumaHistory;

    @Schema(description = "输血史")
    private String transfusionHistory;

    @Schema(description = "父亲病史")
    private String fatherHistory;

    @Schema(description = "母亲病史")
    private String motherHistory;

    @Schema(description = "兄弟姐妹病史")
    private String brothersHistory;

    @Schema(description = "子女病史")
    private String childrenHistory;

    @Schema(description = "遗传病史")
    private String inheritHistory;

    @Schema(description = "遗传病名称")
    private String inheritName;

    @Schema(description = "残疾情况")
    private String disabilityCondition;

    @Schema(description = "身高(cm)")
    private Double height;

    @Schema(description = "体重(kg)")
    private Double weight;

    @Schema(description = "BMI指数")
    private Double bmi;

    @Schema(description = "社区")
    private String community;

    @Schema(description = "楼栋")
    private String building;

    @Schema(description = "单元")
    private String unit;

    @Schema(description = "户号")
    private String household;

    @Schema(description = "档案状态")
    private String recordStatus;

    @Schema(description = "签约状态")
    private String signState;

    @Schema(description = "人群属性")
    private String crowdAttribute;

    @Schema(description = "重点人群")
    private String emphasisCrowd;

    @Schema(description = "管理状态")
    private String managerStatus;

    @Schema(description = "管理日期")
    private LocalDateTime managerDate;

    @Schema(description = "数据来源")
    private String source;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "拼音姓名")
    private String pinyinName;

    /**
     * 获取性别描述
     */
    public String getSexDesc() {
        if ("1".equals(sex)) {
            return "男";
        } else if ("2".equals(sex)) {
            return "女";
        }
        return "未知";
    }

    /**
     * 获取档案状态描述
     */
    public String getRecordStatusDesc() {
        if ("1".equals(recordStatus)) {
            return "正常";
        } else if ("2".equals(recordStatus)) {
            return "停用";
        }
        return "未知";
    }

    /**
     * 获取签约状态描述
     */
    public String getSignStateDesc() {
        if ("1".equals(signState)) {
            return "已签约";
        } else if ("0".equals(signState)) {
            return "未签约";
        }
        return "未知";
    }

    /**
     * 获取BMI状态描述
     */
    public String getBmiStatusDesc() {
        if (bmi == null) {
            return "未知";
        }
        
        if (bmi < 18.5) {
            return "偏瘦";
        } else if (bmi < 24) {
            return "正常";
        } else if (bmi < 28) {
            return "超重";
        } else {
            return "肥胖";
        }
    }

    /**
     * 计算年龄
     */
    public Integer getAge() {
        if (birthday == null) {
            return null;
        }
        
        LocalDateTime now = LocalDateTime.now();
        int age = now.getYear() - birthday.getYear();
        
        // 如果还没到生日，年龄减1
        if (now.getMonthValue() < birthday.getMonthValue() || 
            (now.getMonthValue() == birthday.getMonthValue() && now.getDayOfMonth() < birthday.getDayOfMonth())) {
            age--;
        }
        
        return age;
    }

    /**
     * 获取脱敏手机号
     */
    public String getMaskedPhone() {
        if (phone == null || phone.length() < 7) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }

    /**
     * 获取脱敏身份证号
     */
    public String getMaskedIdcard() {
        if (idcard == null || idcard.length() < 8) {
            return idcard;
        }
        return idcard.substring(0, 4) + "**********" + idcard.substring(idcard.length() - 4);
    }

    /**
     * 是否有过敏史
     */
    public boolean hasAllergyHistory() {
        return allergyHistory != null && !allergyHistory.trim().isEmpty();
    }

    /**
     * 是否有家族病史
     */
    public boolean hasFamilyHistory() {
        return (fatherHistory != null && !fatherHistory.trim().isEmpty()) ||
               (motherHistory != null && !motherHistory.trim().isEmpty()) ||
               (brothersHistory != null && !brothersHistory.trim().isEmpty()) ||
               (childrenHistory != null && !childrenHistory.trim().isEmpty()) ||
               (inheritHistory != null && !inheritHistory.trim().isEmpty());
    }

    /**
     * 是否有疾病史
     */
    public boolean hasDiseaseHistory() {
        return diseaseHistory != null && !diseaseHistory.trim().isEmpty();
    }

    /**
     * 是否有手术史
     */
    public boolean hasOperativeHistory() {
        return operativeHistory != null && !operativeHistory.trim().isEmpty();
    }
}
