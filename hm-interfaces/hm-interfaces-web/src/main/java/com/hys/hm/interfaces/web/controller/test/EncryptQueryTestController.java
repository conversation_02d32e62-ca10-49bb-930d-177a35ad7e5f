package com.hys.hm.interfaces.web.controller.test;

import com.hys.hm.domain.patient.entity.PatientBasicInfoEntity;
import com.hys.hm.domain.patient.service.PatientBasicInfoService;
import com.hys.hm.shared.common.query.QueryCondition;
import com.hys.hm.shared.common.query.QueryConditionParser;
import com.hys.hm.shared.common.result.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 加密字段查询测试控制器
 * 用于测试加密字段的自动查询功能
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Slf4j
@RestController
@RequestMapping("/api/test/encrypt-query")
@RequiredArgsConstructor
public class EncryptQueryTestController {

    private final PatientBasicInfoService patientBasicInfoService;
    private final QueryConditionParser queryConditionParser;

    /**
     * 测试加密字段精确查询
     * 示例: GET /api/test/encrypt-query/exact?phone=13800138000
     */
    @GetMapping("/exact")
    public Result<List<PatientBasicInfoEntity>> testExactQuery(HttpServletRequest request) {
        log.info("测试加密字段精确查询");

        try {
            // 解析查询条件
            Map<String, String[]> parameterMap = request.getParameterMap();
            List<QueryCondition> conditions = queryConditionParser.parseConditions(parameterMap);

            log.info("解析到的查询条件: {}", conditions);

            // 执行查询
            List<PatientBasicInfoEntity> results = patientBasicInfoService.findByConditions(conditions);

            log.info("查询结果数量: {}", results.size());

            return Result.success(results);

        } catch (Exception e) {
            log.error("加密字段精确查询测试失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 测试加密字段模糊查询
     * 示例: GET /api/test/encrypt-query/like?phone_LIKE=138
     */
    @GetMapping("/like")
    public Result<List<PatientBasicInfoEntity>> testLikeQuery(HttpServletRequest request) {
        log.info("测试加密字段模糊查询");

        try {
            // 解析查询条件
            Map<String, String[]> parameterMap = request.getParameterMap();
            List<QueryCondition> conditions = queryConditionParser.parseConditions(parameterMap);

            log.info("解析到的查询条件: {}", conditions);

            // 执行查询
            List<PatientBasicInfoEntity> results = patientBasicInfoService.findByConditions(conditions);

            log.info("查询结果数量: {}", results.size());

            return Result.success(results);

        } catch (Exception e) {
            log.error("加密字段模糊查询测试失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 测试混合查询（加密字段 + 普通字段）
     * 示例: GET /api/test/encrypt-query/mixed?phone_LIKE=138&name=张三&sex=男
     */
    @GetMapping("/mixed")
    public Result<List<PatientBasicInfoEntity>> testMixedQuery(HttpServletRequest request) {
        log.info("测试混合查询（加密字段 + 普通字段）");

        try {
            // 解析查询条件
            Map<String, String[]> parameterMap = request.getParameterMap();
            List<QueryCondition> conditions = queryConditionParser.parseConditions(parameterMap);

            log.info("解析到的查询条件: {}", conditions);

            // 执行查询
            List<PatientBasicInfoEntity> results = patientBasicInfoService.findByConditions(conditions);

            log.info("查询结果数量: {}", results.size());

            return Result.success(results);

        } catch (Exception e) {
            log.error("混合查询测试失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 创建测试数据
     */
    @PostMapping("/create-test-data")
    public Result<String> createTestData() {
        log.info("创建加密字段查询测试数据");

        try {
            // 创建几个测试患者
            PatientBasicInfoEntity patient1 = new PatientBasicInfoEntity();
            patient1.setId("test-patient-001");
            patient1.setName("张三");
            patient1.setSex("男");
            patient1.setPhone("13800138000");
            patient1.setIdcard("110101199001011234");
            patient1.setAddress("北京市朝阳区测试街道123号");

            PatientBasicInfoEntity patient2 = new PatientBasicInfoEntity();
            patient2.setId("test-patient-002");
            patient2.setName("李四");
            patient2.setSex("女");
            patient2.setPhone("13900139000");
            patient2.setIdcard("110101199002022345");
            patient2.setAddress("北京市海淀区测试路456号");

            PatientBasicInfoEntity patient3 = new PatientBasicInfoEntity();
            patient3.setId("test-patient-003");
            patient3.setName("王五");
            patient3.setSex("男");
            patient3.setPhone("13700137000");
            patient3.setIdcard("110101199003033456");
            patient3.setAddress("上海市浦东新区测试大道789号");

            // 保存测试数据
            patientBasicInfoService.save(patient1);
            patientBasicInfoService.save(patient2);
            patientBasicInfoService.save(patient3);

            log.info("测试数据创建成功");
            return Result.success("测试数据创建成功，共创建3个患者记录");

        } catch (Exception e) {
            log.error("创建测试数据失败", e);
            return Result.error("创建测试数据失败: " + e.getMessage());
        }
    }

    /**
     * 清理测试数据
     */
    @DeleteMapping("/clean-test-data")
    public Result<String> cleanTestData() {
        log.info("清理加密字段查询测试数据");

        try {
            // 删除测试数据
            patientBasicInfoService.deleteById("test-patient-001");
            patientBasicInfoService.deleteById("test-patient-002");
            patientBasicInfoService.deleteById("test-patient-003");

            log.info("测试数据清理成功");
            return Result.success("测试数据清理成功");

        } catch (Exception e) {
            log.error("清理测试数据失败", e);
            return Result.error("清理测试数据失败: " + e.getMessage());
        }
    }
}
