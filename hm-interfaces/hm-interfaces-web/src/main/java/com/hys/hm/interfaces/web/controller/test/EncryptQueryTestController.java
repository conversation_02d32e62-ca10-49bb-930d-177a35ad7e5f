package com.hys.hm.interfaces.web.controller.test;

import com.hys.hm.domain.patient.entity.PatientBasicInfoEntity;
import com.hys.hm.domain.patient.service.PatientBasicInfoService;
import com.hys.hm.shared.common.query.QueryCondition;
import com.hys.hm.shared.common.query.QueryConditionParser;
import com.hys.hm.shared.common.Result;
import com.hys.hm.shared.framework.controller.BaseController;
import com.hys.hm.shared.framework.repository.BaseRepositoryImpl;
import com.hys.hm.shared.framework.service.BaseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 加密字段查询测试控制器
 * 用于测试加密字段的自动查询功能
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Slf4j
@RestController
@RequestMapping("/api/test/encrypt-query")
public class EncryptQueryTestController extends BaseController<PatientBasicInfoEntity, String> {

    private final PatientBasicInfoService patientBasicInfoService;
    private final QueryConditionParser queryConditionParser;

    public EncryptQueryTestController(BaseService<PatientBasicInfoEntity, String> baseService,
                                      QueryConditionParser queryConditionParser,
                                      PatientBasicInfoService patientBasicInfoService) {
        super(baseService, queryConditionParser);
        this.queryConditionParser = queryConditionParser;
        this.patientBasicInfoService = patientBasicInfoService;
    }

    /**
     * 测试加密字段精确查询
     * 示例: GET /api/test/encrypt-query/exact?phone=13800138000
     */
    @GetMapping("/exact")
    public Result<List<PatientBasicInfoEntity>> testExactQuery(HttpServletRequest request) {
        log.info("测试加密字段精确查询");

        try {
            // 解析查询条件
            Map<String, String[]> parameterMap = request.getParameterMap();
            List<QueryCondition> conditions = queryConditionParser.parseConditions(parameterMap);

            log.info("解析到的查询条件: {}", conditions);

            // 执行查询
            List<PatientBasicInfoEntity> results = patientBasicInfoService.findByConditions(conditions);

            log.info("查询结果数量: {}", results.size());

            return Result.success(results);

        } catch (Exception e) {
            log.error("加密字段精确查询测试失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 测试加密字段模糊查询
     * 示例: GET /api/test/encrypt-query/like?phone_LIKE=138
     */
    @GetMapping("/like")
    public Result<List<PatientBasicInfoEntity>> testLikeQuery(HttpServletRequest request) {
        log.info("测试加密字段模糊查询");

        try {
            // 解析查询条件
            Map<String, String[]> parameterMap = request.getParameterMap();
            List<QueryCondition> conditions = queryConditionParser.parseConditions(parameterMap);

            log.info("解析到的查询条件: {}", conditions);

            // 执行查询
            List<PatientBasicInfoEntity> results = patientBasicInfoService.findByConditions(conditions);

            log.info("查询结果数量: {}", results.size());

            return Result.success(results);

        } catch (Exception e) {
            log.error("加密字段模糊查询测试失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 测试混合查询（加密字段 + 普通字段）
     * 示例: GET /api/test/encrypt-query/mixed?phone_LIKE=138&name=张三&sex=男
     */
    @GetMapping("/mixed")
    public Result<List<PatientBasicInfoEntity>> testMixedQuery(HttpServletRequest request) {
        log.info("测试混合查询（加密字段 + 普通字段）");

        try {
            // 解析查询条件
            Map<String, String[]> parameterMap = request.getParameterMap();
            List<QueryCondition> conditions = queryConditionParser.parseConditions(parameterMap);

            log.info("解析到的查询条件: {}", conditions);

            // 执行查询
            List<PatientBasicInfoEntity> results = patientBasicInfoService.findByConditions(conditions);

            log.info("查询结果数量: {}", results.size());

            return Result.success(results);

        } catch (Exception e) {
            log.error("混合查询测试失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 检查依赖注入状态
     */
    @GetMapping("/check-injection")
    public Result<Map<String, Object>> checkInjection() {
        Map<String, Object> status = new HashMap<>();

        try {
            // 检查BaseService中的Repository
            Object repository = baseService.getClass().getDeclaredField("repository").get(baseService);
            status.put("repositoryClass", repository.getClass().getSimpleName());

            // 通过反射检查Repository中的EncryptFieldQueryHelper
            if (repository instanceof BaseRepositoryImpl) {
                BaseRepositoryImpl<?, ?> baseRepo = (BaseRepositoryImpl<?, ?>) repository;
                java.lang.reflect.Field field = BaseRepositoryImpl.class.getDeclaredField("encryptFieldQueryHelper");
                field.setAccessible(true);
                Object helper = field.get(baseRepo);

                status.put("encryptFieldQueryHelperInjected", helper != null);
                if (helper != null) {
                    status.put("encryptFieldQueryHelperClass", helper.getClass().getSimpleName());
                }
            }

            return Result.success(status);

        } catch (Exception e) {
            log.error("检查依赖注入状态失败", e);
            status.put("error", e.getMessage());
            return Result.error("检查失败");
        }
    }

    /**
     * 调试加密查询功能
     */
    @GetMapping("/debug")
    public Result<Map<String, Object>> debugEncryptQuery(@RequestParam String fieldName, @RequestParam String value) {
        log.info("调试加密查询功能: fieldName={}, value={}", fieldName, value);

        Map<String, Object> debugInfo = new HashMap<>();

        try {
            // 1. 检查字段是否为加密字段
            Class<PatientBasicInfoEntity> entityClass = PatientBasicInfoEntity.class;
            java.lang.reflect.Field field = entityClass.getDeclaredField(fieldName);
            boolean hasEncryptAnnotation = field.isAnnotationPresent(com.hys.hm.shared.encrypt.annotation.EncryptField.class);

            debugInfo.put("entityClass", entityClass.getSimpleName());
            debugInfo.put("fieldName", fieldName);
            debugInfo.put("hasEncryptAnnotation", hasEncryptAnnotation);

            if (hasEncryptAnnotation) {
                com.hys.hm.shared.encrypt.annotation.EncryptField annotation =
                    field.getAnnotation(com.hys.hm.shared.encrypt.annotation.EncryptField.class);
                debugInfo.put("fuzzySearch", annotation.fuzzySearch());
                debugInfo.put("tokenLength", annotation.tokenLength());
            }

            // 2. 测试查询
            List<QueryCondition> conditions = List.of(QueryCondition.eq(fieldName, value));
            List<PatientBasicInfoEntity> results = baseService.findByConditions(conditions);

            debugInfo.put("queryConditions", conditions);
            debugInfo.put("resultCount", results.size());
            debugInfo.put("results", results);

            return Result.success(debugInfo);

        } catch (Exception e) {
            log.error("调试加密查询失败", e);
            debugInfo.put("error", e.getMessage());
            return Result.error("调试失败");
        }
    }

    /**
     * 创建测试数据（包含加密数据和历史明文数据）
     */
    @PostMapping("/create-test-data")
    public Result<String> createTestData() {
        log.info("创建加密字段查询测试数据（包含加密数据和历史明文数据）");

        try {
            // 创建加密数据的测试患者
            PatientBasicInfoEntity patient1 = new PatientBasicInfoEntity();
            patient1.setId("test-patient-001");
            patient1.setName("张三");
            patient1.setSex("男");
            patient1.setPhone("13800138000");  // 这个会被自动加密
            patient1.setIdcard("110101199001011234");  // 这个会被自动加密
            patient1.setAddress("北京市朝阳区测试街道123号");  // 这个会被自动加密

            PatientBasicInfoEntity patient2 = new PatientBasicInfoEntity();
            patient2.setId("test-patient-002");
            patient2.setName("李四");
            patient2.setSex("女");
            patient2.setPhone("13900139000");  // 这个会被自动加密
            patient2.setIdcard("110101199002022345");  // 这个会被自动加密
            patient2.setAddress("北京市海淀区测试路456号");  // 这个会被自动加密

            PatientBasicInfoEntity patient3 = new PatientBasicInfoEntity();
            patient3.setId("test-patient-003");
            patient3.setName("王五");
            patient3.setSex("男");
            patient3.setPhone("13700137000");  // 这个会被自动加密
            patient3.setIdcard("110101199003033456");  // 这个会被自动加密
            patient3.setAddress("上海市浦东新区测试大道789号");  // 这个会被自动加密

            // 保存测试数据（会自动加密并创建索引）
            patientBasicInfoService.save(patient1);
            patientBasicInfoService.save(patient2);
            patientBasicInfoService.save(patient3);

            log.info("测试数据创建成功");
            return Result.success("测试数据创建成功，共创建3个患者记录（数据会自动加密存储）");

        } catch (Exception e) {
            log.error("创建测试数据失败", e);
            return Result.error("创建测试数据失败: " + e.getMessage());
        }
    }

    /**
     * 创建历史明文测试数据（模拟历史数据）
     */
    @PostMapping("/create-legacy-data")
    public Result<String> createLegacyData() {
        log.info("创建历史明文测试数据");

        try {
            // 注意：这里需要直接操作数据库来插入明文数据，模拟历史数据
            // 在实际场景中，这些数据是历史遗留的明文数据

            log.info("历史明文数据创建功能需要直接操作数据库，此处仅作演示");
            return Result.success("历史明文数据创建说明：需要直接在数据库中插入明文数据来模拟历史数据");

        } catch (Exception e) {
            log.error("创建历史明文数据失败", e);
            return Result.error("创建历史明文数据失败: " + e.getMessage());
        }
    }

    /**
     * 测试明文查询功能
     * 验证用明文参数查询加密字段的功能
     * 示例: GET /api/test/encrypt-query/plaintext-search?phone=13800138000
     */
    @GetMapping("/plaintext-search")
    public Result<List<PatientBasicInfoEntity>> testPlaintextSearch(HttpServletRequest request) {
        log.info("测试明文查询功能");

        try {
            // 解析查询条件
            Map<String, String[]> parameterMap = request.getParameterMap();
            List<QueryCondition> conditions = queryConditionParser.parseConditions(parameterMap);

            log.info("明文查询条件: {}", conditions);

            // 执行查询 - 系统会自动处理：
            // 1. 将明文参数转换为哈希在加密索引中查找
            // 2. 同时查询历史明文数据
            // 3. 合并两种查询结果
            List<PatientBasicInfoEntity> results = patientBasicInfoService.findByConditions(conditions);

            log.info("明文查询结果数量: {}", results.size());

            return Result.success(results);

        } catch (Exception e) {
            log.error("明文查询测试失败", e);
            return Result.error("明文查询失败: " + e.getMessage());
        }
    }

    /**
     * 验证查询逻辑
     * 显示查询过程的详细信息
     */
    @GetMapping("/verify-query-logic")
    public Result<Map<String, Object>> verifyQueryLogic(@RequestParam String phone) {
        log.info("验证查询逻辑: phone={}", phone);

        try {
            Map<String, Object> result = new HashMap<>();

            // 1. 测试明文参数查询
            List<QueryCondition> conditions = List.of(QueryCondition.eq("phone", phone));
            List<PatientBasicInfoEntity> queryResults = patientBasicInfoService.findByConditions(conditions);

            result.put("inputPhone", phone);
            result.put("queryConditions", conditions);
            result.put("resultCount", queryResults.size());
            result.put("results", queryResults);

            // 2. 显示查询说明
            result.put("explanation", "查询过程：1) 将明文参数'" + phone + "'生成哈希在加密索引中查找匹配的实体ID；" +
                                    "2) 同时直接用明文参数查询历史明文数据；" +
                                    "3) 使用OR条件合并两种查询结果");

            log.info("查询逻辑验证完成，找到 {} 条记录", queryResults.size());

            return Result.success(result);

        } catch (Exception e) {
            log.error("查询逻辑验证失败", e);
            return Result.error("查询逻辑验证失败: " + e.getMessage());
        }
    }

    /**
     * 清理测试数据
     */
    @DeleteMapping("/clean-test-data")
    public Result<String> cleanTestData() {
        log.info("清理加密字段查询测试数据");

        try {
            // 删除测试数据
            patientBasicInfoService.deleteById("test-patient-001");
            patientBasicInfoService.deleteById("test-patient-002");
            patientBasicInfoService.deleteById("test-patient-003");

            log.info("测试数据清理成功");
            return Result.success("测试数据清理成功");

        } catch (Exception e) {
            log.error("清理测试数据失败", e);
            return Result.error("清理测试数据失败: " + e.getMessage());
        }
    }

    @Override
    protected String getEntityId(PatientBasicInfoEntity entity) {
        return "";
    }

    @Override
    protected void setEntityId(PatientBasicInfoEntity entity, String s) {

    }
}
