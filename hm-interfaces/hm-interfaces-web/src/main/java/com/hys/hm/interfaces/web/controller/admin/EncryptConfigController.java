package com.hys.hm.interfaces.web.controller.admin;

import com.hys.hm.shared.encrypt.annotation.EncryptField;
import com.hys.hm.shared.encrypt.service.EncryptService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 加密配置管理控制器
 * 提供加密配置的查看和管理功能
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/encrypt-config")
@RequiredArgsConstructor
@Tag(name = "加密配置管理", description = "加密配置查看和管理接口")
public class EncryptConfigController {

    private final EncryptService encryptService;

    @Value("${app.security.encrypt.fields.enabled:true}")
    private boolean fieldsEnabled;

    @Value("${app.security.encrypt.fields.fuzzy-search-enabled:true}")
    private boolean fuzzySearchEnabled;

    @Value("${app.security.encrypt.fields.index-cleanup.enabled:true}")
    private boolean indexCleanupEnabled;

    @Value("${app.security.encrypt.fields.index-cleanup.interval-hours:24}")
    private int indexCleanupIntervalHours;

    @Value("${app.security.encrypt.fields.index-cleanup.retention-days:90}")
    private int indexCleanupRetentionDays;

    /**
     * 获取加密配置信息
     */
    @GetMapping("/info")
    @Operation(summary = "获取加密配置", description = "获取当前的加密配置信息")
    public Map<String, Object> getEncryptConfig() {
        Map<String, Object> result = new HashMap<>();

        try {
            Map<String, Object> config = new HashMap<>();

            // 基础配置（不包含敏感信息）
            config.put("fieldsEnabled", fieldsEnabled);
            config.put("fuzzySearchEnabled", fuzzySearchEnabled);

            // 索引清理配置
            Map<String, Object> cleanupConfig = new HashMap<>();
            cleanupConfig.put("enabled", indexCleanupEnabled);
            cleanupConfig.put("intervalHours", indexCleanupIntervalHours);
            cleanupConfig.put("retentionDays", indexCleanupRetentionDays);
            config.put("indexCleanup", cleanupConfig);

            // 密钥信息（不显示具体长度，仅显示是否配置）
            config.put("aesKeyConfigured", true);
            config.put("sm4KeyConfigured", true);
            config.put("saltConfigured", true);

            result.put("config", config);
            result.put("success", true);

        } catch (Exception e) {
            log.error("获取加密配置失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * 测试加密算法
     */
    @PostMapping("/test-algorithm")
    @Operation(summary = "测试加密算法", description = "测试指定的加密算法")
    public Map<String, Object> testEncryptAlgorithm(
            @Parameter(description = "加密类型", required = true) @RequestParam EncryptField.EncryptType encryptType,
            @Parameter(description = "测试文本", required = true) @RequestParam String testText) {

        Map<String, Object> result = new HashMap<>();

        try {
            long startTime = System.nanoTime();

            // 加密
            String encrypted = encryptService.encrypt(testText, encryptType);
            long encryptTime = System.nanoTime() - startTime;

            // 解密
            startTime = System.nanoTime();
            String decrypted = encryptService.decrypt(encrypted, encryptType);
            long decryptTime = System.nanoTime() - startTime;

            // 验证
            boolean isValid = testText.equals(decrypted);

            result.put("encryptType", encryptType.name());
            result.put("testText", testText);
            result.put("encrypted", encrypted);
            result.put("decrypted", decrypted);
            result.put("isValid", isValid);
            result.put("encryptTimeNs", encryptTime);
            result.put("decryptTimeNs", decryptTime);
            result.put("encryptTimeMicros", encryptTime / 1000.0);
            result.put("decryptTimeMicros", decryptTime / 1000.0);
            result.put("success", true);

            log.info("加密算法测试: type={}, valid={}, encryptTime={}μs, decryptTime={}μs",
                    encryptType, isValid, encryptTime / 1000.0, decryptTime / 1000.0);

        } catch (Exception e) {
            log.error("测试加密算法失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * 测试哈希算法
     */
    @PostMapping("/test-hash")
    @Operation(summary = "测试哈希算法", description = "测试哈希生成和验证")
    public Map<String, Object> testHashAlgorithm(
            @Parameter(description = "测试文本", required = true) @RequestParam String testText,
            @Parameter(description = "分词长度", example = "3") @RequestParam(defaultValue = "3") int tokenLength) {

        Map<String, Object> result = new HashMap<>();

        try {
            long startTime = System.nanoTime();

            // 生成精确哈希
            String exactHash = encryptService.generateExactHash(testText);
            long exactHashTime = System.nanoTime() - startTime;

            // 生成模糊查询分词
            startTime = System.nanoTime();
            var fuzzyTokens = encryptService.generateFuzzyTokens(testText, tokenLength);
            long fuzzyHashTime = System.nanoTime() - startTime;

            // 验证哈希
            startTime = System.nanoTime();
            boolean isValid = encryptService.verifyHash(testText, exactHash);
            long verifyTime = System.nanoTime() - startTime;

            result.put("testText", testText);
            result.put("tokenLength", tokenLength);
            result.put("exactHash", exactHash);
            result.put("fuzzyTokens", fuzzyTokens);
            result.put("fuzzyTokenCount", fuzzyTokens.size());
            result.put("isValid", isValid);
            result.put("exactHashTimeNs", exactHashTime);
            result.put("fuzzyHashTimeNs", fuzzyHashTime);
            result.put("verifyTimeNs", verifyTime);
            result.put("exactHashTimeMicros", exactHashTime / 1000.0);
            result.put("fuzzyHashTimeMicros", fuzzyHashTime / 1000.0);
            result.put("verifyTimeMicros", verifyTime / 1000.0);
            result.put("success", true);

            log.info("哈希算法测试: tokens={}, valid={}, exactTime={}μs, fuzzyTime={}μs, verifyTime={}μs",
                    fuzzyTokens.size(), isValid, exactHashTime / 1000.0, fuzzyHashTime / 1000.0, verifyTime / 1000.0);

        } catch (Exception e) {
            log.error("测试哈希算法失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * 批量性能测试
     */
    @PostMapping("/benchmark")
    @Operation(summary = "批量性能测试", description = "进行批量加密解密性能测试")
    public Map<String, Object> benchmark(
            @Parameter(description = "测试次数", example = "1000") @RequestParam(defaultValue = "1000") int iterations,
            @Parameter(description = "测试数据长度", example = "11") @RequestParam(defaultValue = "11") int dataLength) {

        Map<String, Object> result = new HashMap<>();

        try {
            // 生成测试数据
            String testData = "1".repeat(dataLength);

            // AES性能测试
            long aesEncryptTotal = 0;
            long aesDecryptTotal = 0;

            for (int i = 0; i < iterations; i++) {
                long start = System.nanoTime();
                String encrypted = encryptService.encrypt(testData, EncryptField.EncryptType.AES);
                aesEncryptTotal += System.nanoTime() - start;

                start = System.nanoTime();
                encryptService.decrypt(encrypted, EncryptField.EncryptType.AES);
                aesDecryptTotal += System.nanoTime() - start;
            }

            // 哈希性能测试
            long exactHashTotal = 0;
            long fuzzyHashTotal = 0;

            for (int i = 0; i < iterations; i++) {
                long start = System.nanoTime();
                encryptService.generateExactHash(testData);
                exactHashTotal += System.nanoTime() - start;

                start = System.nanoTime();
                encryptService.generateFuzzyTokens(testData, 3);
                fuzzyHashTotal += System.nanoTime() - start;
            }

            result.put("iterations", iterations);
            result.put("dataLength", dataLength);
            result.put("aesEncryptAvgMicros", aesEncryptTotal / 1000.0 / iterations);
            result.put("aesDecryptAvgMicros", aesDecryptTotal / 1000.0 / iterations);
            result.put("exactHashAvgMicros", exactHashTotal / 1000.0 / iterations);
            result.put("fuzzyHashAvgMicros", fuzzyHashTotal / 1000.0 / iterations);
            result.put("aesEncryptQps", 1_000_000.0 / (aesEncryptTotal / 1000.0 / iterations));
            result.put("aesDecryptQps", 1_000_000.0 / (aesDecryptTotal / 1000.0 / iterations));
            result.put("exactHashQps", 1_000_000.0 / (exactHashTotal / 1000.0 / iterations));
            result.put("fuzzyHashQps", 1_000_000.0 / (fuzzyHashTotal / 1000.0 / iterations));
            result.put("success", true);

            log.info("批量性能测试完成: {} 次迭代，数据长度 {} 字符", iterations, dataLength);

        } catch (Exception e) {
            log.error("批量性能测试失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }
}
