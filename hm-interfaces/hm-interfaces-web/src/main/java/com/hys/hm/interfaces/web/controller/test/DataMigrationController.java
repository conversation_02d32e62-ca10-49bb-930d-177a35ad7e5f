package com.hys.hm.interfaces.web.controller.test;

import com.hys.hm.shared.encrypt.service.EncryptService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据迁移控制器
 * 用于手动执行数据迁移和加密处理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
@Slf4j
@RestController
@RequestMapping("/api/test/migration")
@RequiredArgsConstructor
@Tag(name = "数据迁移", description = "数据迁移和加密处理接口")
public class DataMigrationController {

    private final JdbcTemplate jdbcTemplate;
    private final EncryptService encryptService;

    /**
     * 查看迁移状态
     */
    @GetMapping("/status")
    @Operation(summary = "查看迁移状态", description = "查看当前数据迁移状态")
    public Map<String, Object> getMigrationStatus() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 统计转诊表单总数
            Integer totalCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM dc_referral_form", Integer.class);

            // 统计有敏感数据的记录数
            Integer sensitiveCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM dc_referral_form WHERE id_card IS NOT NULL OR phone IS NOT NULL",
                Integer.class);

            // 统计加密索引数量
            Integer indexCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM encrypt_search_index", Integer.class);

            // 检查是否有已加密的数据（长度超过50的数据可能是加密的）
            Integer encryptedCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM dc_referral_form WHERE LENGTH(phone) > 50 OR LENGTH(id_card) > 50",
                Integer.class);

            result.put("totalRecords", totalCount);
            result.put("sensitiveRecords", sensitiveCount);
            result.put("indexRecords", indexCount);
            result.put("encryptedRecords", encryptedCount);
            result.put("migrationNeeded", sensitiveCount > 0 && indexCount == 0);
            result.put("success", true);

        } catch (Exception e) {
            log.error("查看迁移状态失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * 执行数据迁移
     */
    @PostMapping("/execute")
    @Operation(summary = "执行数据迁移", description = "执行转诊表单数据的加密迁移")
    public Map<String, Object> executeMigration(
            @Parameter(description = "是否实际执行（false为预览模式）") @RequestParam(defaultValue = "false") boolean execute) {

        Map<String, Object> result = new HashMap<>();

        try {
            log.info("开始执行数据迁移，执行模式: {}", execute);

            // 1. 查询需要迁移的数据
            String selectSql = """
                SELECT id, id_card, phone 
                FROM dc_referral_form 
                WHERE id_card IS NOT NULL OR phone IS NOT NULL
                ORDER BY CREATE_TIME
                """;

            List<Map<String, Object>> records = jdbcTemplate.queryForList(selectSql);

            log.info("找到 {} 条需要迁移的记录", records.size());

            int processedCount = 0;
            int errorCount = 0;

            for (Map<String, Object> record : records) {
                String id = (String) record.get("id");
                String idCard = (String) record.get("id_card");
                String phone = (String) record.get("phone");

                try {
                    if (execute) {
                        // 实际执行迁移
                        migrateRecord(id, idCard, phone);
                    } else {
                        // 预览模式，只记录日志
                        log.info("预览迁移记录: ID={}, 身份证={}, 手机号={}",
                                id, maskSensitiveData(idCard), maskSensitiveData(phone));
                    }

                    processedCount++;

                } catch (Exception e) {
                    log.error("迁移记录失败，ID: {}, 错误: {}", id, e.getMessage());
                    errorCount++;
                }
            }

            result.put("totalRecords", records.size());
            result.put("processedCount", processedCount);
            result.put("errorCount", errorCount);
            result.put("executeMode", execute);
            result.put("success", true);

            if (execute) {
                log.info("数据迁移完成，处理记录数: {}, 错误数: {}", processedCount, errorCount);
            } else {
                log.info("数据迁移预览完成，共 {} 条记录需要迁移", records.size());
            }

        } catch (Exception e) {
            log.error("数据迁移失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * 清理迁移数据
     */
    @PostMapping("/cleanup")
    @Operation(summary = "清理迁移数据", description = "清理加密索引数据（谨慎使用）")
    public Map<String, Object> cleanupMigration() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 删除所有加密索引
            int deletedCount = jdbcTemplate.update("DELETE FROM encrypt_search_index");

            result.put("deletedIndexCount", deletedCount);
            result.put("success", true);

            log.info("清理迁移数据完成，删除索引记录数: {}", deletedCount);

        } catch (Exception e) {
            log.error("清理迁移数据失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * 迁移单条记录
     */
    private void migrateRecord(String entityId, String idCard, String phone) {
        // 处理身份证号
        if (idCard != null && !idCard.trim().isEmpty()) {
            // 检查是否已经加密（简单判断：长度超过50可能已加密）
            if (idCard.length() < 50) {
                createSearchIndex("ReferralFormEntity", entityId, "idCard", idCard, true, 4);
                log.debug("为记录 {} 创建身份证号索引", entityId);
            }
        }

        // 处理手机号
        if (phone != null && !phone.trim().isEmpty()) {
            // 检查是否已经加密
            if (phone.length() < 50) {
                createSearchIndex("ReferralFormEntity", entityId, "phone", phone, true, 3);
                log.debug("为记录 {} 创建手机号索引", entityId);
            }
        }
    }

    /**
     * 创建搜索索引
     */
    private void createSearchIndex(String entityType, String entityId, String fieldName,
                                 String plainText, boolean fuzzySearch, int tokenLength) {

        // 删除旧索引
        String deleteSql = """
            DELETE FROM encrypt_search_index 
            WHERE entity_type = ? AND entity_id = ? AND field_name = ?
            """;
        jdbcTemplate.update(deleteSql, entityType, entityId, fieldName);

        // 创建精确查询索引
        String exactHash = encryptService.generateExactHash(plainText);
        String insertExactSql = """
            INSERT INTO encrypt_search_index 
            (entity_type, entity_id, field_name, exact_hash, create_time, update_time) 
            VALUES (?, ?, ?, ?, NOW(), NOW())
            """;
        jdbcTemplate.update(insertExactSql, entityType, entityId, fieldName, exactHash);

        // 如果支持模糊查询，创建分词索引
        if (fuzzySearch) {
            List<String> tokenHashes = encryptService.generateFuzzyTokens(plainText, tokenLength);

            String insertTokenSql = """
                INSERT INTO encrypt_search_index 
                (entity_type, entity_id, field_name, token_hash, create_time, update_time) 
                VALUES (?, ?, ?, ?, NOW(), NOW())
                """;

            for (String tokenHash : tokenHashes) {
                jdbcTemplate.update(insertTokenSql, entityType, entityId, fieldName, tokenHash);
            }
        }
    }

    /**
     * 脱敏敏感数据用于日志显示
     */
    private String maskSensitiveData(String data) {
        if (data == null || data.length() <= 4) {
            return "****";
        }
        return data.substring(0, 3) + "****" + data.substring(data.length() - 1);
    }
}
