package com.hys.hm.interfaces.web.controller.test;

import com.hys.hm.shared.encrypt.annotation.EncryptField;
import com.hys.hm.shared.encrypt.service.impl.EncryptFieldServiceImpl;
import com.hys.hm.shared.encrypt.service.EncryptService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 加密功能测试控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
@Slf4j
@RestController
@RequestMapping("/api/test/encrypt")
@RequiredArgsConstructor
@Tag(name = "加密测试", description = "加密功能测试接口")
public class EncryptTestController {

    private final EncryptService encryptService;
    private final EncryptFieldServiceImpl encryptFieldService;

    /**
     * 测试加密功能
     */
    @PostMapping("/test")
    @Operation(summary = "测试加密功能", description = "测试AES加密和解密功能")
    public Map<String, Object> testEncrypt(
            @Parameter(description = "要加密的明文", required = true) @RequestParam String plainText) {

        log.info("测试加密功能，明文: {}", plainText);

        Map<String, Object> result = new HashMap<>();

        try {
            // 测试AES加密
            String encrypted = encryptService.encrypt(plainText, EncryptField.EncryptType.AES);
            result.put("plainText", plainText);
            result.put("encrypted", encrypted);

            // 测试解密
            String decrypted = encryptService.decrypt(encrypted, EncryptField.EncryptType.AES);
            result.put("decrypted", decrypted);
            result.put("decryptSuccess", plainText.equals(decrypted));

            // 测试精确哈希
            String exactHash = encryptService.generateExactHash(plainText);
            result.put("exactHash", exactHash);

            // 测试模糊查询分词
            List<String> fuzzyTokens = encryptService.generateFuzzyTokens(plainText, 3);
            result.put("fuzzyTokens", fuzzyTokens);

            result.put("success", true);

        } catch (Exception e) {
            log.error("加密测试失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * 测试模糊查询功能
     */
    @PostMapping("/search")
    @Operation(summary = "测试模糊查询", description = "测试加密字段的模糊查询功能")
    public Map<String, Object> testFuzzySearch(
            @Parameter(description = "要搜索的文本", required = true) @RequestParam String searchText) {

        log.info("测试模糊查询功能，搜索文本: {}", searchText);

        Map<String, Object> result = new HashMap<>();

        try {
            // 测试精确查询
            List<String> exactResults = encryptFieldService.findByExactMatch(
                "ReferralFormEntity", "phone", searchText);
            result.put("exactResults", exactResults);

            // 测试模糊查询
            List<String> fuzzyResults = encryptFieldService.findByFuzzyMatch(
                "ReferralFormEntity", "phone", searchText, 3);
            result.put("fuzzyResults", fuzzyResults);

            result.put("success", true);

        } catch (Exception e) {
            log.error("模糊查询测试失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * 手动创建搜索索引
     */
    @PostMapping("/create-index")
    @Operation(summary = "创建搜索索引", description = "为指定的明文创建搜索索引")
    public Map<String, Object> createSearchIndex(
            @Parameter(description = "实体ID", required = true) @RequestParam String entityId,
            @Parameter(description = "字段名", required = true) @RequestParam String fieldName,
            @Parameter(description = "明文值", required = true) @RequestParam String plainText) {

        log.info("手动创建搜索索引，实体ID: {}, 字段: {}, 明文: {}", entityId, fieldName, plainText);

        Map<String, Object> result = new HashMap<>();

        try {
            // 创建精确查询索引
            String exactHash = encryptService.generateExactHash(plainText);

            // 创建模糊查询索引
            List<String> tokenHashes = encryptService.generateFuzzyTokens(plainText, 3);

            // 这里应该直接插入数据库，但为了简化，我们只返回生成的哈希值
            result.put("entityId", entityId);
            result.put("fieldName", fieldName);
            result.put("plainText", plainText);
            result.put("exactHash", exactHash);
            result.put("tokenHashes", tokenHashes);
            result.put("success", true);

        } catch (Exception e) {
            log.error("创建搜索索引失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }
}
