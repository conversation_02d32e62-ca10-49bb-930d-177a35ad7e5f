package com.hys.hm.interfaces.web.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

/**
 * Swagger/OpenAPI 配置类
 * 使用 ReDoc 替代默认的 Swagger UI
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-28
 */
@Configuration
public class SwaggerConfig implements WebMvcConfigurer {

    @Value("${server.servlet.context-path:}")
    private String contextPath;

    @Value("${server.port:8080}")
    private String serverPort;

    /**
     * 配置 OpenAPI 信息
     */
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("健康管理系统 API 文档")
                        .description("基于 Spring Boot 的健康管理系统 RESTful API 文档")
                        .version("3.0.1-SNAPSHOT")
                        .contact(new Contact()
                                .name("HYS 开发团队")
                                .email("<EMAIL>")
                                .url("https://github.com/hys"))
                        .license(new License()
                                .name("MIT License")
                                .url("https://opensource.org/licenses/MIT")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:" + serverPort + contextPath)
                                .description("开发环境"),
                        new Server()
                                .url("https://api.hm.hys.com")
                                .description("生产环境")
                ));
    }

    /**
     * 系统管理 API 分组
     */
    @Bean
    public GroupedOpenApi systemApi() {
        return GroupedOpenApi.builder()
                .group("01-系统管理")
                .pathsToMatch("/api/system/**")
                .build();
    }

    /**
     * 健康数据 API 分组
     */
    @Bean
    public GroupedOpenApi healthApi() {
        return GroupedOpenApi.builder()
                .group("02-健康数据")
                .pathsToMatch("/api/health/**")
                .build();
    }

    /**
     * 随访管理 API 分组
     */
    @Bean
    public GroupedOpenApi followupApi() {
        return GroupedOpenApi.builder()
                .group("03-随访管理")
                .pathsToMatch("/api/followup/**")
                .build();
    }

    /**
     * 转诊管理 API 分组
     */
    @Bean
    public GroupedOpenApi referralApi() {
        return GroupedOpenApi.builder()
                .group("04-转诊管理")
                .pathsToMatch("/api/referral/**")
                .build();
    }

    /**
     * 知识库 API 分组
     */
    @Bean
    public GroupedOpenApi knowledgeApi() {
        return GroupedOpenApi.builder()
                .group("05-知识库")
                .pathsToMatch("/api/knowledge/**")
                .build();
    }

    /**
     * 沟通交流 API 分组
     */
    @Bean
    public GroupedOpenApi communicationApi() {
        return GroupedOpenApi.builder()
                .group("06-沟通交流")
                .pathsToMatch("/api/communication/**")
                .build();
    }

    /**
     * 管理员 API 分组
     */
    @Bean
    public GroupedOpenApi adminApi() {
        return GroupedOpenApi.builder()
                .group("07-管理员")
                .pathsToMatch("/api/admin/**")
                .build();
    }

    /**
     * 测试 API 分组
     */
    @Bean
    public GroupedOpenApi testApi() {
        return GroupedOpenApi.builder()
                .group("99-测试接口")
                .pathsToMatch("/api/test/**")
                .build();
    }

    /**
     * 配置静态资源处理
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // ReDoc 通过 CDN 引入，无需配置本地静态资源
        // 保留 Swagger UI 静态资源作为备选（如果需要的话）
        registry.addResourceHandler("/swagger-ui/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/swagger-ui/");
    }

    /**
     * 配置视图控制器
     */
    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        // 重定向文档相关路径到 ReDoc
        registry.addRedirectViewController("/docs", "/redoc.html");
        registry.addRedirectViewController("/swagger-ui.html", "/redoc.html");
        registry.addRedirectViewController("/swagger-ui/", "/redoc.html");
        registry.addRedirectViewController("/swagger-ui/index.html", "/redoc.html");
    }
}
