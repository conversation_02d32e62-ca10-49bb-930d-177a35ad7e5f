# 健康管理系统架构重构文档

## 文档概览

本目录包含了健康管理系统架构重构的完整文档，从单体架构向微服务就绪的模块化单体架构演进。

## 文档结构

### 📋 [架构重构规划](./architecture-refactoring-plan.md)
- **目标**：构建微服务就绪的单体架构
- **内容**：
  - 当前架构问题分析
  - 目标架构设计
  - 业务强依赖解决方案
  - 重构实施计划
  - 风险评估与应对

### 🔧 [模块化单体实现指南](./modular-monolith-implementation.md)
- **目标**：详细的技术实现方案
- **内容**：
  - 模块间接口设计
  - 数据传输对象（DTO）定义
  - 内部事件机制实现
  - 数据同步服务设计

### ⚙️ [模块配置和依赖管理](./module-configuration-guide.md)
- **目标**：配置和依赖管理规范
- **内容**：
  - 依赖注入配置
  - Maven模块依赖管理
  - 模块边界控制
  - 架构规则检查

## 重构目标

### 🎯 主要目标

1. **解决业务强依赖问题**
   - 患者服务查询转诊信息
   - 转诊表单关联患者档案
   - 跨模块数据访问

2. **为微服务拆分做准备**
   - 清晰的模块边界
   - 标准化的模块间接口
   - 数据隔离和同步机制

3. **保持单体架构的优势**
   - 简单的部署模式
   - 统一的事务管理
   - 较低的运维复杂度

### 🏗️ 架构原则

- **模块自治**：每个业务模块边界清晰，低耦合高内聚
- **接口隔离**：模块间通过明确定义的接口通信
- **数据隔离**：每个模块管理自己的数据，避免直接跨模块数据访问
- **依赖倒置**：通过接口和事件解耦模块间依赖

## 核心解决方案

### 1. 模块间接口设计

```java
// 患者查询服务接口
public interface PatientQueryService {
    PatientBasicInfoDTO getPatientBasicInfo(String patientId);
    List<PatientBasicInfoDTO> batchGetPatientBasicInfo(List<String> patientIds);
    boolean existsPatient(String patientId);
}

// 转诊查询服务接口
public interface ReferralQueryService {
    List<ReferralSummaryDTO> getPatientReferralSummary(String patientId);
    ReferralDetailDTO getReferralDetail(String referralId);
}
```

### 2. 内部事件机制

```java
// 事件发布
@Component
public class InternalEventPublisher {
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    
    public void publishEvent(DomainEvent event) {
        eventPublisher.publishEvent(event);
    }
}

// 事件监听
@EventListener
@Async
@Transactional
public void handlePatientCreated(PatientCreatedEvent event) {
    // 同步患者基础信息到转诊模块
    syncService.syncPatientBasicInfo(event.getBasicInfo());
}
```

### 3. 数据同步策略

- **数据冗余**：在需要的模块中冗余必要的基础数据
- **事件驱动同步**：通过事件机制保持数据一致性
- **异步处理**：避免同步调用影响性能
- **补偿机制**：处理同步失败的情况

## 模块划分

### 核心业务模块

```
hm-application/
├── hm-application-patient/     # 患者管理
├── hm-application-referral/    # 转诊管理
├── hm-application-health/      # 健康管理
├── hm-application-followup/    # 随访管理
├── hm-application-knowledge/   # 知识库管理
└── hm-application-shared/      # 共享应用服务（聚合服务）
```

### 领域模块

```
hm-domain/
├── hm-domain-patient/          # 患者领域
├── hm-domain-referral/         # 转诊领域
├── hm-domain-health/           # 健康领域
├── hm-domain-followup/         # 随访领域
├── hm-domain-knowledge/        # 知识库领域
└── hm-domain-shared/           # 共享领域（事件定义）
```

### 基础设施模块

```
hm-infrastructure/
├── hm-infrastructure-persistence-patient/    # 患者持久化
├── hm-infrastructure-persistence-referral/   # 转诊持久化
├── hm-infrastructure-persistence-health/     # 健康持久化
├── hm-infrastructure-external/               # 外部系统集成
├── hm-infrastructure-ai/                     # AI服务集成
└── hm-infrastructure-logging/                # 日志服务
```

## 实施计划

### 第一阶段：模块重组 (3-4周)
- [ ] 按业务域重新划分模块
- [ ] 创建清晰的模块边界
- [ ] 建立模块间接口定义
- [ ] 重构共享框架模块

### 第二阶段：模块解耦 (4-5周)
- [ ] 重构患者模块对外接口
- [ ] 重构转诊模块并建立数据冗余
- [ ] 建立模块间通信机制
- [ ] 实现异步事件处理

### 第三阶段：数据同步机制 (3-4周)
- [ ] 实现数据冗余和同步
- [ ] 建立数据一致性保障
- [ ] 实现补偿机制
- [ ] 建立数据修复工具

### 第四阶段：完善和优化 (2-3周)
- [ ] 性能优化
- [ ] 测试和验证
- [ ] 建立监控指标
- [ ] 完善文档

## 技术选型

### 核心技术栈
- **Spring Boot 3.2.x** - 应用框架
- **Spring Data JPA** - 数据访问
- **Spring Events** - 内部事件机制
- **MySQL 8.0** - 主数据库
- **Redis** - 缓存和会话

### 开发工具
- **Maven** - 构建工具
- **Lombok** - 代码生成
- **MapStruct** - 对象映射
- **ArchUnit** - 架构规则检查

## 未来微服务拆分

### 拆分就绪特征
重构完成后，系统将具备：
- ✅ 清晰的模块边界
- ✅ 标准化的接口
- ✅ 数据隔离
- ✅ 事件驱动架构

### 拆分路径
1. **选择拆分模块** - 优先拆分独立性强的模块
2. **独立部署** - 将模块独立部署为微服务
3. **服务发现** - 引入服务注册与发现
4. **API网关** - 统一对外服务入口
5. **分布式事务** - 升级为分布式事件总线

## 风险控制

### 主要风险
- **模块间数据一致性风险** - 通过事件驱动和补偿机制应对
- **重构过程中的业务中断** - 采用渐进式重构
- **开发复杂度增加** - 建立清晰的规范和指南
- **性能影响** - 通过缓存和异步处理优化

### 应对策略
- **渐进式重构** - 逐个模块进行重构
- **向后兼容** - 保持现有API兼容性
- **充分测试** - 每个阶段都进行测试验证
- **监控告警** - 建立完善的监控体系

## 总结

本重构方案通过模块化设计、接口隔离、数据冗余和事件驱动等技术手段，在保持单体架构简单性的同时，解决了业务强依赖问题，并为未来的微服务拆分奠定了坚实的基础。

重构后的架构将具备：
- **模块自治** - 每个模块可以独立开发和测试
- **低耦合** - 模块间依赖关系清晰且最小化  
- **高内聚** - 模块内部功能紧密相关
- **易扩展** - 为未来的微服务拆分做好准备

这是一个既解决当前问题，又面向未来的架构重构方案。
