# 模块化单体架构实现指南

## 概述

本文档详细说明如何实现模块化单体架构（Modular Monolith），为未来的微服务拆分做好准备。

## 模块间接口设计

### 1. 查询服务接口

#### 患者查询服务接口

```java
package com.hys.hm.domain.patient.service;

/**
 * 患者查询服务接口
 * 供其他模块调用，获取患者相关信息
 */
public interface PatientQueryService {
    
    /**
     * 获取患者基础信息
     */
    PatientBasicInfoDTO getPatientBasicInfo(String patientId);
    
    /**
     * 批量获取患者基础信息
     */
    List<PatientBasicInfoDTO> batchGetPatientBasicInfo(List<String> patientIds);
    
    /**
     * 检查患者是否存在
     */
    boolean existsPatient(String patientId);
    
    /**
     * 根据身份证号查找患者
     */
    Optional<PatientBasicInfoDTO> findPatientByIdCard(String idCard);
    
    /**
     * 根据手机号查找患者
     */
    List<PatientBasicInfoDTO> findPatientsByPhone(String phone);
}
```

#### 转诊查询服务接口

```java
package com.hys.hm.domain.referral.service;

/**
 * 转诊查询服务接口
 * 供其他模块调用，获取转诊相关信息
 */
public interface ReferralQueryService {
    
    /**
     * 获取患者的转诊记录摘要
     */
    List<ReferralSummaryDTO> getPatientReferralSummary(String patientId);
    
    /**
     * 获取转诊详情
     */
    ReferralDetailDTO getReferralDetail(String referralId);
    
    /**
     * 获取医院的转诊统计
     */
    ReferralStatisticsDTO getHospitalReferralStatistics(String unitId);
    
    /**
     * 检查转诊是否存在
     */
    boolean existsReferral(String referralId);
    
    /**
     * 获取患者最近的转诊记录
     */
    Optional<ReferralSummaryDTO> getLatestReferral(String patientId);
}
```

### 2. 数据传输对象（DTO）

#### 患者基础信息DTO

```java
package com.hys.hm.shared.types.dto;

/**
 * 患者基础信息DTO
 * 用于模块间数据传输
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PatientBasicInfoDTO {
    
    /**
     * 患者ID
     */
    private String patientId;
    
    /**
     * 患者姓名
     */
    private String name;
    
    /**
     * 性别：1-男，2-女
     */
    private Integer gender;
    
    /**
     * 年龄
     */
    private Integer age;
    
    /**
     * 联系电话（脱敏）
     */
    private String phone;
    
    /**
     * 地址
     */
    private String address;
    
    /**
     * 出生日期
     */
    private LocalDate birthDate;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 获取性别描述
     */
    public String getGenderDesc() {
        return gender != null && gender == 1 ? "男" : "女";
    }
    
    /**
     * 获取脱敏手机号
     */
    public String getMaskedPhone() {
        if (phone == null || phone.length() < 7) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }
}
```

#### 转诊摘要DTO

```java
package com.hys.hm.shared.types.dto;

/**
 * 转诊摘要DTO
 * 用于模块间数据传输
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReferralSummaryDTO {
    
    /**
     * 转诊ID
     */
    private String referralId;
    
    /**
     * 转诊编号
     */
    private String referralNo;
    
    /**
     * 患者ID
     */
    private String patientId;
    
    /**
     * 转出医院名称
     */
    private String outHospitalName;
    
    /**
     * 转入医院名称
     */
    private String inHospitalName;
    
    /**
     * 转诊状态：1-待处理，2-已确认，3-已拒绝，4-已取消，5-已完成
     */
    private Integer status;
    
    /**
     * 转诊日期
     */
    private LocalDateTime referralDate;
    
    /**
     * 紧急程度：1-普通，2-紧急，3-急诊
     */
    private Integer urgencyLevel;
    
    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        switch (status) {
            case 1: return "待处理";
            case 2: return "已确认";
            case 3: return "已拒绝";
            case 4: return "已取消";
            case 5: return "已完成";
            default: return "未知";
        }
    }
    
    /**
     * 获取紧急程度描述
     */
    public String getUrgencyDesc() {
        switch (urgencyLevel) {
            case 1: return "普通";
            case 2: return "紧急";
            case 3: return "急诊";
            default: return "普通";
        }
    }
}
```

## 内部事件机制

### 1. 事件定义

#### 基础事件接口

```java
package com.hys.hm.shared.events;

/**
 * 领域事件基础接口
 */
public interface DomainEvent {
    
    /**
     * 事件ID
     */
    String getEventId();
    
    /**
     * 聚合根ID
     */
    String getAggregateId();
    
    /**
     * 事件时间戳
     */
    LocalDateTime getTimestamp();
    
    /**
     * 事件类型
     */
    String getEventType();
    
    /**
     * 事件版本
     */
    Integer getVersion();
}
```

#### 患者相关事件

```java
package com.hys.hm.shared.events.patient;

/**
 * 患者创建事件
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PatientCreatedEvent implements DomainEvent {
    
    private String eventId = UUID.randomUUID().toString();
    private String patientId;
    private PatientBasicInfoDTO basicInfo;
    private String createdBy;
    private LocalDateTime timestamp = LocalDateTime.now();
    private Integer version = 1;
    
    @Override
    public String getAggregateId() {
        return patientId;
    }
    
    @Override
    public String getEventType() {
        return "PatientCreated";
    }
}

/**
 * 患者信息更新事件
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PatientUpdatedEvent implements DomainEvent {
    
    private String eventId = UUID.randomUUID().toString();
    private String patientId;
    private PatientBasicInfoDTO oldInfo;
    private PatientBasicInfoDTO newInfo;
    private List<String> changedFields;
    private String updatedBy;
    private LocalDateTime timestamp = LocalDateTime.now();
    private Integer version = 1;
    
    @Override
    public String getAggregateId() {
        return patientId;
    }
    
    @Override
    public String getEventType() {
        return "PatientUpdated";
    }
}
```

### 2. 事件发布器

```java
package com.hys.hm.shared.events;

/**
 * 内部事件发布器
 */
@Component
@Slf4j
public class InternalEventPublisher {
    
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    
    /**
     * 发布领域事件
     */
    public void publishEvent(DomainEvent event) {
        try {
            log.info("发布内部事件: eventType={}, aggregateId={}, eventId={}", 
                    event.getEventType(), event.getAggregateId(), event.getEventId());
            
            eventPublisher.publishEvent(event);
            
        } catch (Exception e) {
            log.error("发布内部事件失败: eventType={}, aggregateId={}, error={}", 
                    event.getEventType(), event.getAggregateId(), e.getMessage(), e);
            throw new EventPublishException("事件发布失败", e);
        }
    }
    
    /**
     * 批量发布事件
     */
    @Transactional
    public void publishEvents(List<DomainEvent> events) {
        for (DomainEvent event : events) {
            publishEvent(event);
        }
    }
}
```

### 3. 事件监听器

```java
package com.hys.hm.application.referral.listener;

/**
 * 转诊模块的患者事件监听器
 */
@Component
@Slf4j
public class ReferralPatientEventListener {
    
    @Autowired
    private ReferralPatientInfoSyncService syncService;
    
    /**
     * 处理患者创建事件
     */
    @EventListener
    @Async
    @Transactional
    public void handlePatientCreated(PatientCreatedEvent event) {
        log.info("转诊模块处理患者创建事件: patientId={}", event.getPatientId());
        
        try {
            // 同步患者基础信息到转诊模块
            syncService.syncPatientBasicInfo(event.getBasicInfo());
            
        } catch (Exception e) {
            log.error("处理患者创建事件失败: patientId={}, error={}", 
                    event.getPatientId(), e.getMessage(), e);
            // 这里可以实现重试机制或者发送告警
        }
    }
    
    /**
     * 处理患者更新事件
     */
    @EventListener
    @Async
    @Transactional
    public void handlePatientUpdated(PatientUpdatedEvent event) {
        log.info("转诊模块处理患者更新事件: patientId={}, changedFields={}", 
                event.getPatientId(), event.getChangedFields());
        
        try {
            // 只同步变更的字段
            syncService.updatePatientBasicInfo(
                event.getPatientId(), 
                event.getNewInfo(), 
                event.getChangedFields()
            );
            
        } catch (Exception e) {
            log.error("处理患者更新事件失败: patientId={}, error={}", 
                    event.getPatientId(), e.getMessage(), e);
        }
    }
}
```

## 数据同步服务

### 转诊模块的患者信息同步服务

```java
package com.hys.hm.application.referral.service;

/**
 * 转诊模块的患者信息同步服务
 */
@Service
@Transactional
@Slf4j
public class ReferralPatientInfoSyncService {
    
    @Autowired
    private ReferralPatientInfoRepository referralPatientInfoRepository;
    
    /**
     * 同步患者基础信息
     */
    public void syncPatientBasicInfo(PatientBasicInfoDTO basicInfo) {
        log.info("同步患者基础信息到转诊模块: patientId={}", basicInfo.getPatientId());
        
        // 检查是否已存在
        Optional<ReferralPatientInfo> existingOpt = 
            referralPatientInfoRepository.findByPatientId(basicInfo.getPatientId());
            
        if (existingOpt.isPresent()) {
            // 更新现有记录
            ReferralPatientInfo existing = existingOpt.get();
            updatePatientInfo(existing, basicInfo);
            referralPatientInfoRepository.save(existing);
            
            log.info("更新转诊模块患者信息成功: patientId={}", basicInfo.getPatientId());
        } else {
            // 创建新记录
            ReferralPatientInfo newInfo = createPatientInfo(basicInfo);
            referralPatientInfoRepository.save(newInfo);
            
            log.info("创建转诊模块患者信息成功: patientId={}", basicInfo.getPatientId());
        }
    }
    
    /**
     * 增量更新患者信息
     */
    public void updatePatientBasicInfo(String patientId, PatientBasicInfoDTO newInfo, 
                                     List<String> changedFields) {
        log.info("增量更新转诊模块患者信息: patientId={}, fields={}", patientId, changedFields);
        
        ReferralPatientInfo patientInfo = referralPatientInfoRepository
            .findByPatientId(patientId)
            .orElseThrow(() -> new IllegalArgumentException("转诊模块中患者信息不存在: " + patientId));
            
        // 只更新变更的字段
        for (String field : changedFields) {
            updateField(patientInfo, newInfo, field);
        }
        
        patientInfo.setUpdateTime(LocalDateTime.now());
        referralPatientInfoRepository.save(patientInfo);
        
        log.info("增量更新转诊模块患者信息成功: patientId={}", patientId);
    }
    
    private void updateField(ReferralPatientInfo entity, PatientBasicInfoDTO newInfo, String field) {
        switch (field) {
            case "name":
                entity.setName(newInfo.getName());
                break;
            case "gender":
                entity.setGender(newInfo.getGender());
                break;
            case "age":
                entity.setAge(newInfo.getAge());
                break;
            case "phone":
                entity.setPhone(newInfo.getPhone());
                break;
            case "address":
                entity.setAddress(newInfo.getAddress());
                break;
            default:
                log.warn("未知的字段更新: {}", field);
        }
    }
    
    private ReferralPatientInfo createPatientInfo(PatientBasicInfoDTO basicInfo) {
        return ReferralPatientInfo.builder()
            .patientId(basicInfo.getPatientId())
            .name(basicInfo.getName())
            .gender(basicInfo.getGender())
            .age(basicInfo.getAge())
            .phone(basicInfo.getPhone())
            .address(basicInfo.getAddress())
            .createTime(LocalDateTime.now())
            .updateTime(LocalDateTime.now())
            .build();
    }
    
    private void updatePatientInfo(ReferralPatientInfo existing, PatientBasicInfoDTO basicInfo) {
        existing.setName(basicInfo.getName());
        existing.setGender(basicInfo.getGender());
        existing.setAge(basicInfo.getAge());
        existing.setPhone(basicInfo.getPhone());
        existing.setAddress(basicInfo.getAddress());
        existing.setUpdateTime(LocalDateTime.now());
    }
}
```

这个实现指南展示了如何在单体架构中实现模块化设计，为未来的微服务拆分做好准备。关键点包括：

1. **清晰的模块接口**：定义标准的查询服务接口
2. **标准化的DTO**：用于模块间数据传输
3. **内部事件机制**：基于Spring Events实现模块解耦
4. **数据同步服务**：通过事件驱动实现数据一致性

这样的设计既保持了单体架构的简单性，又为未来的微服务拆分提供了良好的基础。
