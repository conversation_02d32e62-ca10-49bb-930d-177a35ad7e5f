# 转诊模块标杆示例使用指南

## 概述

转诊模块是按照新架构规范实现的标杆示例，展示了如何正确使用框架封装和模块化设计。其他模块应该参考这个实现方式。

## 架构特点

### 1. 严格的分层架构

```
hm-interfaces-web (接口层)
    ↓ 依赖
hm-application-referral (应用服务层)
    ↓ 依赖  
hm-domain-referral (领域层)
    ↓ 依赖
hm-infrastructure-persistence-referral (基础设施层)
    ↓ 依赖
hm-shared-* (共享内核)
```

### 2. 使用Framework封装

- **BaseEntity**: 实体类继承获得审计字段和通用功能
- **BaseRepository**: 仓储接口继承获得完整CRUD和动态查询
- **BaseService**: 应用服务继承获得标准业务操作
- **BaseController**: 控制器继承获得REST API标准实现

### 3. 事件驱动设计

- 使用Spring Events实现模块间解耦
- 通过事件发布器发布领域事件
- 异步事件处理保证性能

## 核心组件说明

### 领域层 (Domain Layer)

#### 1. 聚合根 - ReferralForm
```java
@Data
@Builder
public class ReferralForm {
    // 业务方法
    public void confirm(String operatorId) { ... }
    public void reject(String reason, String operatorId) { ... }
    
    // 查询方法
    public boolean isPending() { ... }
    public boolean isUrgent() { ... }
}
```

#### 2. 仓储接口 - ReferralRepository
```java
public interface ReferralRepository {
    ReferralForm save(ReferralForm referralForm);
    Optional<ReferralForm> findById(String id);
    List<ReferralForm> findByStatus(ReferralStatus status);
}
```

#### 3. 领域服务 - ReferralDomainService
```java
@Service
public class ReferralDomainService {
    public ReferralForm createReferralForm(ReferralForm referralForm, String operatorId) {
        // 1. 业务验证
        // 2. 设置默认值
        // 3. 保存数据
        // 4. 发布事件
    }
}
```

### 基础设施层 (Infrastructure Layer)

#### 1. JPA实体 - ReferralFormEntity
```java
@Entity
@Table(name = "dc_referral_form")
public class ReferralFormEntity extends BaseEntity<String> {
    // 继承BaseEntity获得审计字段
    // 使用@EncryptField注解加密敏感字段
}
```

#### 2. JPA仓储 - ReferralFormJpaRepository
```java
public interface ReferralFormJpaRepository extends BaseRepository<ReferralFormEntity, String> {
    // 继承BaseRepository获得完整CRUD功能
    Optional<ReferralFormEntity> findByReferralNo(String referralNo);
}
```

### 应用服务层 (Application Layer)

#### 1. 应用服务 - ReferralApplicationService
```java
@Service
public class ReferralApplicationService extends BaseServiceImpl<ReferralForm, String> {
    // 继承BaseServiceImpl获得完整CRUD功能
    // 协调领域服务完成业务用例
}
```

### 接口层 (Interface Layer)

#### Web控制器 - ReferralController
```java
@RestController
@RequestMapping("/api/referrals")
public class ReferralController extends BaseController<ReferralQueryDTO, String> {
    // 继承BaseController获得标准REST API
}
```

## 使用示例

### 1. 创建转诊表单

```java
ReferralCreateDTO createDTO = ReferralCreateDTO.builder()
    .basicInfoId("patient-001")
    .patientName("张三")
    .referralReason("心脏病需要专科治疗")
    .outUnitName("北京市第一医院")
    .inUnitName("北京协和医院")
    .build();

ReferralQueryDTO result = referralApplicationService.createReferralForm(createDTO, "operator-001");
```

### 2. 转诊状态流转

```java
// 确认转诊
ReferralQueryDTO confirmed = referralApplicationService.confirmReferral(referralId, "operator-001");

// 拒绝转诊
ReferralQueryDTO rejected = referralApplicationService.rejectReferral(referralId, "医疗资源不足", "operator-001");
```

### 3. 查询操作

```java
// 获取患者转诊记录
List<ReferralSummaryDTO> patientReferrals = referralApplicationService.getPatientReferralSummary("patient-001");

// 获取待处理转诊
List<ReferralSummaryDTO> pendingReferrals = referralApplicationService.getPendingReferrals();
```

### 4. REST API调用

```bash
# 创建转诊表单
POST /api/referrals
Content-Type: application/json
X-User-Id: operator-001

{
  "basicInfoId": "patient-001",
  "patientName": "张三",
  "referralReason": "心脏病需要专科治疗",
  "outUnitName": "北京市第一医院",
  "inUnitName": "北京协和医院"
}

# 确认转诊
PUT /api/referrals/{id}/confirm
X-User-Id: operator-001

# 获取患者转诊记录
GET /api/referrals/patient/{patientId}
```

## 事件处理示例

### 1. 事件发布

```java
@Service
public class ReferralDomainService {
    
    @Autowired
    private EventPublisher eventPublisher;
    
    public ReferralForm createReferralForm(ReferralForm referralForm, String operatorId) {
        // 发布转诊创建事件
        ReferralCreatedEvent event = ReferralCreatedEvent.builder()
            .referralId(referralForm.getId())
            .referralNo(referralForm.getReferralNo())
            .build();
            
        eventPublisher.publishEvent(event);
        
        return referralForm;
    }
}
```

### 2. 事件监听

```java
@Component
public class PatientEventListener {
    
    @EventListener
    @Async
    @Transactional
    public void handleReferralCreated(ReferralCreatedEvent event) {
        // 更新患者的转诊记录
        patientService.updateReferralHistory(event.getPatientInfo().getPatientId(), event);
    }
}
```

## 最佳实践

### 1. 严格遵循分层架构
- 接口层只处理HTTP请求响应
- 应用服务层协调业务用例
- 领域层包含核心业务逻辑
- 基础设施层处理技术实现

### 2. 充分利用Framework封装
- 继承Base类获得通用功能
- 使用注解简化配置
- 遵循框架约定

### 3. 合理使用事件机制
- 模块间通过事件解耦
- 异步处理提高性能
- 事件包含必要的业务信息

### 4. 完善的异常处理
- 业务异常在领域层抛出
- 应用服务层统一处理
- 控制器层标准化响应

### 5. 充分的单元测试
- 测试覆盖主要业务场景
- 使用Builder模式构建测试数据
- 验证业务逻辑正确性

## 总结

转诊模块展示了新架构的完整实现方式，其他模块开发时应该：

1. **参考这个模块的分层结构**
2. **使用相同的Framework封装**
3. **遵循相同的命名约定**
4. **实现相同的异常处理**
5. **编写相同质量的测试**

这样可以保证整个系统的架构一致性和代码质量。
