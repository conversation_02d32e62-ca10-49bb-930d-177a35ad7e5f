# 加密字段自动查询功能说明

## 📋 功能概述

Framework现在支持对带有`@EncryptField`注解的字段进行自动加密查询，无需修改现有的查询代码。当查询条件涉及加密字段时，系统会自动使用加密索引进行查询，同时保持对现有`_LIKE`等操作符的完全支持。

## 🚀 核心特性

### 1. 自动检测加密字段
- 系统在构建查询条件时自动检测字段是否带有`@EncryptField`注解
- 对于加密字段，自动切换到加密索引查询模式
- 对于普通字段，保持原有的SQL查询方式

### 2. 明文参数查询支持
- **用户传入明文参数**：查询时传入的参数是明文（如手机号13800138000）
- **自动哈希匹配**：系统自动将明文参数生成哈希，在加密索引中查找匹配记录
- **透明处理**：用户无需关心数据库中的加密存储，直接使用明文查询

### 3. 历史数据兼容
- **混合数据支持**：同时支持查询加密数据和历史明文数据
- **OR查询逻辑**：使用OR条件同时查询加密索引和明文字段
- **平滑迁移**：支持数据从明文到加密的平滑过渡

### 4. 支持的查询操作
- **精确查询 (EQ)**：`fieldName=value` 或 `fieldName_EQ=value`
- **模糊查询 (LIKE)**：`fieldName_LIKE=value`
- **向后兼容**：其他操作符对加密字段会降级为普通查询

### 5. 透明集成
- 无需修改现有的Service或Repository代码
- 现有的查询API保持不变
- 自动处理加密字段和普通字段的混合查询

## 🔧 使用方法

### 1. 实体类配置

确保实体类上有加密监听器和加密字段注解：

```java
@Entity
@Table(name = "dp_basic_info")
@EntityListeners(FrameworkEncryptEntityListener.class)  // 必须添加
public class PatientBasicInfoEntity extends BaseEntity<String> {

    @Column(name = "PHONE", length = 200)
    @EncryptField(
        type = EncryptField.EncryptType.AES,
        fuzzySearch = true,  // 支持模糊查询
        maskLevel = EncryptField.MaskLevel.PARTIAL,
        description = "联系电话"
    )
    private String phone;

    @Column(name = "IDCARD", length = 200)
    @EncryptField(
        type = EncryptField.EncryptType.AES,
        fuzzySearch = true,  // 支持模糊查询
        maskLevel = EncryptField.MaskLevel.PARTIAL,
        description = "身份证号"
    )
    private String idcard;
}
```

### 2. 查询使用示例

#### HTTP请求查询
```bash
# 精确查询加密字段
GET /api/patients?phone=13800138000

# 模糊查询加密字段
GET /api/patients?phone_LIKE=138

# 混合查询（加密字段 + 普通字段）
GET /api/patients?phone_LIKE=138&name=张三&sex=男

# 多个加密字段查询
GET /api/patients?phone=13800138000&idcard=110101199001011234
```

#### 代码中使用
```java
@Service
public class PatientQueryService {
    
    @Autowired
    private PatientBasicInfoService patientService;
    
    @Autowired
    private QueryConditionParser queryParser;
    
    public List<PatientBasicInfoEntity> searchPatients(HttpServletRequest request) {
        // 解析查询条件
        Map<String, String[]> params = request.getParameterMap();
        List<QueryCondition> conditions = queryParser.parseConditions(params);
        
        // 执行查询 - 自动处理加密字段
        return patientService.findByConditions(conditions);
    }
    
    public List<PatientBasicInfoEntity> searchByPhone(String phone) {
        // 直接使用QueryCondition - 自动处理加密字段
        List<QueryCondition> conditions = List.of(
            QueryCondition.eq("phone", phone)
        );
        return patientService.findByConditions(conditions);
    }
    
    public List<PatientBasicInfoEntity> searchByPhoneLike(String phonePattern) {
        // 模糊查询 - 自动处理加密字段
        List<QueryCondition> conditions = List.of(
            QueryCondition.like("phone", phonePattern)
        );
        return patientService.findByConditions(conditions);
    }
}
```

## 🔍 工作原理

### 1. 查询流程
```
HTTP请求(明文参数) → QueryConditionParser → BaseRepositoryImpl →
检测加密字段 → 混合查询(加密索引 + 明文历史数据) → 返回结果
```

### 2. 加密字段检测
- 在`BaseRepositoryImpl.buildSinglePredicate()`方法中进行检测
- 使用`EncryptFieldQueryHelper.isEncryptField()`判断字段是否为加密字段
- 如果是加密字段，调用`buildEncryptedFieldPredicate()`构建混合查询条件

### 3. 混合查询处理
#### 对于加密字段，系统会构建两个查询条件：

**A. 加密数据查询**：
- **精确查询**：将明文参数生成哈希，在加密索引中查找匹配的实体ID
- **模糊查询**：将明文参数生成分词哈希，在加密索引中查找匹配的实体ID
- **ID匹配**：将找到的实体ID列表转换为SQL的IN查询条件

**B. 历史明文数据查询**：
- **精确查询**：直接用明文参数查询字段值
- **模糊查询**：直接用明文参数进行LIKE查询

**C. 结果合并**：
- 使用OR条件将加密查询和明文查询合并
- 确保既能查到新的加密数据，也能查到历史明文数据

### 4. 查询示例
```sql
-- 实际生成的SQL类似于：
SELECT * FROM patient_info
WHERE (
    -- 加密数据查询：通过ID匹配
    id IN ('id1', 'id2', 'id3')  -- 从加密索引中找到的ID
    OR
    -- 历史明文数据查询：直接字段匹配
    phone = '13800138000'
)
```

## 🧪 测试功能

提供了专门的测试控制器来验证功能：

### 基础测试
```bash
# 创建测试数据（会自动加密存储）
POST /api/test/encrypt-query/create-test-data

# 创建历史明文数据（模拟历史数据）
POST /api/test/encrypt-query/create-legacy-data

# 测试精确查询（明文参数查询加密数据）
GET /api/test/encrypt-query/exact?phone=13800138000

# 测试模糊查询（明文参数模糊查询加密数据）
GET /api/test/encrypt-query/like?phone_LIKE=138

# 测试混合查询（加密字段 + 普通字段）
GET /api/test/encrypt-query/mixed?phone_LIKE=138&name=张三&sex=男

# 清理测试数据
DELETE /api/test/encrypt-query/clean-test-data
```

### 高级测试
```bash
# 测试明文查询功能（验证明文参数查询）
GET /api/test/encrypt-query/plaintext-search?phone=13800138000

# 验证查询逻辑（显示详细查询过程）
GET /api/test/encrypt-query/verify-query-logic?phone=13800138000
```

### 测试场景说明

1. **新数据测试**：
   - 保存数据时会自动加密并创建索引
   - 查询时用明文参数，系统自动匹配加密索引

2. **历史数据兼容测试**：
   - 数据库中同时存在加密数据和明文数据
   - 一次查询能同时找到两种类型的数据

3. **混合查询测试**：
   - 同时查询加密字段和普通字段
   - 验证查询条件的正确组合

## ⚠️ 注意事项

### 1. 性能考虑
- 加密字段查询需要先查询加密索引，然后根据ID查询主表
- 对于大量数据的模糊查询，性能可能不如普通SQL查询
- 建议合理设置分词长度(`tokenLength`)以平衡查询精度和性能

### 2. 数据一致性
- 确保实体保存时正确创建了加密索引
- 定期清理过期的加密索引数据
- 数据迁移时需要重新生成加密索引

### 3. 降级机制
- 当加密服务不可用时，自动降级为普通查询
- 不支持的操作符会降级为普通查询
- 查询异常时会降级为普通查询

## 🔧 配置说明

在`application-encrypt.yml`中配置加密相关参数：

```yaml
app:
  security:
    encrypt:
      fields:
        enabled: true  # 启用字段加密
        fuzzy-search-enabled: true  # 启用模糊查询索引
```

## 📈 后续优化

1. **查询性能优化**：考虑使用缓存机制优化频繁查询
2. **索引管理**：提供更完善的索引清理和维护功能
3. **查询统计**：添加查询性能监控和统计功能
4. **更多操作符支持**：逐步支持更多查询操作符的加密查询
