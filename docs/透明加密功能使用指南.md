# 透明加密功能使用指南

## 📖 概述

本文档详细介绍如何使用Framework层提供的透明加密功能。该功能可以让开发者通过简单的注解配置，实现数据的自动加密存储、智能查询和安全脱敏，完全无需关心底层的加密实现细节。

## 🎯 核心特性

### ✨ 完全透明
- **自动加密存储**：保存时自动加密敏感字段
- **自动解密读取**：查询时自动解密数据
- **自动索引维护**：创建和维护加密字段的搜索索引
- **自动脱敏处理**：返回数据时自动脱敏显示

### 🔍 智能查询
- **精确查询**：支持加密字段的精确匹配查询
- **模糊查询**：支持加密字段的模糊搜索（基于分词索引）
- **性能优化**：使用索引避免全表扫描
- **降级处理**：加密服务不可用时自动降级为普通查询

### 🛡️ 安全脱敏
- **多级脱敏**：支持5种脱敏级别
- **智能识别**：根据数据格式自动选择脱敏方式
- **字段隐藏**：支持完全隐藏敏感字段
- **日志安全**：查询日志自动脱敏

## 🚀 快速开始

### 1. 启用加密功能

在`application.yml`中启用加密功能：

```yaml
hm:
  framework:
    encrypt:
      enabled: true  # 启用透明加密功能
```

### 2. 实体配置

在需要加密的字段上添加`@EncryptField`注解：

```java
@Entity
@Table(name = "patient_info")
@EntityListeners(FrameworkEncryptEntityListener.class)  // 添加加密监听器
public class PatientEntity extends BaseEntity<String> {

    @Column(name = "name", length = 50)
    private String name;  // 普通字段，不加密

    @Column(name = "phone", length = 200)  // 加密后长度会增加
    @EncryptField(
        type = EncryptField.EncryptType.AES,           // 加密算法
        maskLevel = EncryptField.MaskLevel.PARTIAL,    // 脱敏级别
        description = "手机号"                          // 字段描述
    )
    private String phone;

    @Column(name = "id_card", length = 200)
    @EncryptField(
        type = EncryptField.EncryptType.AES,
        maskLevel = EncryptField.MaskLevel.PARTIAL,
        description = "身份证号"
    )
    private String idCard;

    @Column(name = "address", length = 500)
    @EncryptField(
        type = EncryptField.EncryptType.AES,
        maskLevel = EncryptField.MaskLevel.PARTIAL,
        fuzzySearch = true,      // 支持模糊查询
        tokenLength = 2,         // 分词长度
        description = "地址"
    )
    private String address;
}
```

### 3. 服务层使用

服务层代码无需任何修改，Framework会自动处理加密逻辑：

```java
@Service
public class PatientService {

    @Autowired
    private PatientRepository patientRepository;

    // 普通保存 - Framework自动加密
    public Patient savePatient(Patient patient) {
        return patientRepository.save(patient);  // 自动加密敏感字段
    }

    // 普通查询 - Framework自动解密和脱敏
    public Patient findById(String id) {
        return patientRepository.findById(id)    // 自动解密
            .orElse(null);                       // 返回时自动脱敏
    }

    // 加密字段精确查询
    public Optional<Patient> findByPhone(String phone) {
        return patientService.findFirstByEncryptedField("phone", phone);
    }

    // 加密字段模糊查询
    public List<Patient> findByAddressLike(String keyword) {
        return patientService.findByEncryptedFieldLike("address", keyword);
    }
}
```

## 📋 注解详细配置

### @EncryptField 注解参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `type` | `EncryptType` | `AES` | 加密算法类型 |
| `maskLevel` | `MaskLevel` | `PARTIAL` | 脱敏级别 |
| `fuzzySearch` | `boolean` | `false` | 是否支持模糊查询 |
| `tokenLength` | `int` | `2` | 模糊查询分词长度 |
| `hideInResult` | `boolean` | `false` | 是否在查询结果中隐藏 |
| `description` | `String` | `""` | 字段描述 |

### 加密算法类型 (EncryptType)

```java
public enum EncryptType {
    AES,    // AES对称加密（推荐）
    SM4     // 国密SM4算法
}
```

### 脱敏级别 (MaskLevel)

| 级别 | 说明 | 示例 |
|------|------|------|
| `NONE` | 不脱敏 | `***********` |
| `PARTIAL` | 部分脱敏 | `138****8000` |
| `FULL` | 完全脱敏 | `***********` |
| `FIRST_LAST` | 首尾保留 | `1*********0` |
| `MIDDLE` | 中间保留 | `***0138***` |

## 🔍 查询功能详解

### 1. 普通字段查询

对于未加密的字段，使用标准的查询方法：

```java
// 使用BaseService的标准方法
List<Patient> patients = patientService.findByField("name", "张三");
List<Patient> patients = patientService.findByFieldLike("name", "张");
```

### 2. 加密字段精确查询

对于加密字段，使用专门的加密查询方法：

```java
// 根据手机号精确查询（自动使用加密索引）
Optional<Patient> patient = patientService.findFirstByEncryptedField("phone", "***********");

// 根据身份证号精确查询
List<Patient> patients = patientService.findByEncryptedField("idCard", "110101199001011234");
```

### 3. 加密字段模糊查询

对于支持模糊查询的加密字段：

```java
// 根据地址模糊查询（自动使用分词索引）
List<Patient> patients = patientService.findByEncryptedFieldLike("address", "北京市");
```

### 4. 复杂查询

在复杂查询中，Framework会自动识别加密字段：

```java
// 构建查询条件
PageRequest pageRequest = PageRequest.of(1, 20);
pageRequest.addCondition(QueryCondition.eq("phone", "***********"));  // 自动使用加密索引
pageRequest.addCondition(QueryCondition.like("address", "北京"));       // 自动使用分词索引

PageResult<Patient> result = patientService.findByPageRequest(pageRequest);
```

## 🛡️ 脱敏功能详解

### 1. 自动脱敏

Framework会根据注解配置自动对返回数据进行脱敏：

```java
Patient patient = patientService.findById("123");
// patient.getPhone() 返回 "138****8000" 而不是原始手机号
// patient.getIdCard() 返回 "110101********1234" 而不是原始身份证
```

### 2. 智能脱敏

脱敏服务会根据数据格式智能选择脱敏方式：

```java
// 手机号自动识别并脱敏
maskService.mask("***********", MaskLevel.PARTIAL);  // 返回: 138****8000

// 身份证号自动识别并脱敏
maskService.mask("110101199001011234", MaskLevel.PARTIAL);  // 返回: 110101********1234

// 中文姓名自动识别并脱敏
maskService.mask("张三", MaskLevel.PARTIAL);  // 返回: 张*
```

### 3. 字段隐藏

对于极度敏感的字段，可以完全隐藏：

```java
@EncryptField(
    type = EncryptField.EncryptType.AES,
    hideInResult = true,  // 在查询结果中完全隐藏
    description = "银行卡号"
)
private String bankCard;
```

## ⚡ 性能优化

### 1. 索引自动维护

Framework会自动为加密字段创建和维护搜索索引：

- **精确查询索引**：基于字段值的哈希索引
- **模糊查询索引**：基于分词的倒排索引
- **自动更新**：数据变更时自动更新索引
- **自动清理**：数据删除时自动清理索引

### 2. 异步处理

索引的创建和更新采用异步处理，不影响主业务性能：

```java
// 保存操作立即返回，索引创建异步进行
Patient savedPatient = patientService.save(patient);
```

### 3. 降级策略

当加密服务不可用时，自动降级为普通查询：

```java
// 如果加密索引服务不可用，自动使用普通数据库查询
List<Patient> patients = patientService.findByEncryptedField("phone", "***********");
```

## 🔧 高级配置

### 1. 自定义脱敏规则

可以通过实现`MaskService`接口来自定义脱敏规则：

```java
@Service
public class CustomMaskService implements MaskService {
    
    @Override
    public String mask(String data, EncryptField.MaskLevel maskLevel) {
        // 自定义脱敏逻辑
        return customMaskLogic(data, maskLevel);
    }
}
```

### 2. 自定义加密算法

可以通过实现`EncryptService`接口来支持自定义加密算法：

```java
@Service
public class CustomEncryptService implements EncryptService {
    
    @Override
    public String encrypt(String plainText, EncryptField.EncryptType type) {
        // 自定义加密逻辑
        return customEncryptLogic(plainText, type);
    }
}
```

### 3. 监控和日志

Framework提供了详细的监控和日志功能：

```yaml
logging:
  level:
    com.hys.hm.shared.framework.encrypt: DEBUG  # 启用加密功能调试日志
```

## 🚨 注意事项

### 1. 数据库字段长度

加密后的数据长度会增加，需要调整数据库字段长度：

```java
@Column(name = "phone", length = 200)  // 原来50，加密后需要200
@EncryptField(...)
private String phone;
```

### 2. 历史数据兼容

Framework支持历史数据的平滑迁移：

- 新数据自动加密存储
- 历史数据解密时自动识别并兼容
- 支持批量数据加密迁移

### 3. 性能考虑

- 加密字段查询性能优于全表扫描
- 模糊查询需要合理设置分词长度
- 大量数据时建议分批处理

### 4. 安全建议

- 定期轮换加密密钥
- 监控加密操作日志
- 限制敏感数据的访问权限
- 定期备份加密索引

## 📚 示例代码

完整的患者管理示例请参考：
- 实体定义：`hm-domain-patient/PatientBasicInfoEntity.java`
- 服务实现：`hm-application-patient/PatientApplicationServiceSimple.java`
- 查询服务：`hm-application-patient/PatientQueryServiceSimple.java`

## 🆘 常见问题

### Q: 如何判断字段是否已加密？
A: Framework会自动判断，对于历史数据会尝试解密，失败时保持原值。

### Q: 加密功能对性能有多大影响？
A: 加密操作异步进行，查询使用索引，对主业务性能影响很小。

### Q: 可以关闭某个字段的脱敏吗？
A: 可以，设置`maskLevel = MaskLevel.NONE`即可。

### Q: 支持哪些数据库？
A: 支持所有JPA兼容的数据库，索引存储可配置。

---

**版本**: 1.0  
**更新时间**: 2025-07-31  
**作者**: HYS团队
