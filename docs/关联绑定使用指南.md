# 基于注解的关联绑定使用指南

## 📖 快速开始

### 1. 启用关联绑定功能

在 `application.yml` 中配置：

```yaml
hm:
  relation:
    enabled: true                    # 启用关联处理（默认true）
    cache:
      enabled: true                  # 启用缓存（默认true）
      default-expire-seconds: 300    # 默认缓存5分钟
    batch:
      enabled: true                  # 启用批量加载（默认true）
      max-batch-size: 100           # 最大批量大小
```

### 2. 在实体/DTO中标记关联关系

```java
// 转诊实体
@Entity
@Table(name = "dc_referral_form")
public class ReferralFormEntity extends BaseEntity<String> {
    
    @Column(name = "BASIC_INFO_ID")
    @EntityRelation(
        entityType = "patient",              // 关联实体类型
        relationField = "id",                // 关联字段（默认"id"）
        type = RelationType.MANY_TO_ONE,     // 关联类型
        description = "关联患者基本信息"      // 描述
    )
    private String basicInfoId;
    
    // 用于存储关联数据的字段（必须是@Transient）
    @Transient
    private Object patientInfo;              // 自动填充患者信息
    
    // 其他字段...
}
```

```java
// 转诊DTO
@Data
public class ReferralDTO {
    
    private String id;
    
    @EntityRelation(
        entityType = "patient",
        type = RelationType.MANY_TO_ONE,
        description = "关联患者信息"
    )
    private String basicInfoId;
    
    // 关联数据字段
    private Object patientInfo;              // 自动填充
    
    // 其他字段...
}
```

### 3. 创建关联数据提供者

```java
@Service
@RelationProvider(
    entityTypes = {"patient"},               // 提供的实体类型
    name = "患者信息提供者",
    description = "为其他模块提供患者基本信息"
)
public class PatientRelationService {
    
    @Autowired
    private PatientBasicInfoRepository patientRepository;
    
    /**
     * 批量加载患者信息（推荐方式，性能更好）
     */
    @RelationLoader(
        entityTypes = {"patient"},           // 支持的实体类型
        priority = 0,                        // 优先级（数字越小优先级越高）
        supportBatch = true,                 // 支持批量加载
        cacheSeconds = 300                   // 缓存5分钟
    )
    public Map<String, PatientBasicInfo> batchLoadPatients(List<String> patientIds) {
        log.debug("批量加载患者信息: {}", patientIds);
        
        Map<String, PatientBasicInfo> result = new HashMap<>();
        
        // 批量查询数据库
        List<PatientBasicInfoEntity> entities = patientRepository.findAllById(patientIds);
        
        // 转换为Map
        for (PatientBasicInfoEntity entity : entities) {
            PatientBasicInfo info = convertToBasicInfo(entity);
            result.put(entity.getId(), info);
        }
        
        return result;
    }
    
    /**
     * 单个加载患者信息（备用方式）
     */
    @RelationLoader(
        entityTypes = {"patient"},
        priority = 1,                        // 较低优先级
        supportBatch = false,
        cacheSeconds = 300
    )
    public PatientBasicInfo loadSinglePatient(String patientId) {
        log.debug("单个加载患者信息: {}", patientId);
        
        return patientRepository.findById(patientId)
            .map(this::convertToBasicInfo)
            .orElse(null);
    }
    
    /**
     * 根据身份证号加载患者（不同的实体类型）
     */
    @RelationLoader(
        entityTypes = {"patient_by_idcard"},
        priority = 1,
        supportBatch = false,
        cacheSeconds = 600                   // 缓存10分钟
    )
    public PatientBasicInfo loadPatientByIdCard(String idCard) {
        return patientRepository.findByIdcard(idCard)
            .map(this::convertToBasicInfo)
            .orElse(null);
    }
    
    private PatientBasicInfo convertToBasicInfo(PatientBasicInfoEntity entity) {
        // 转换逻辑
        PatientBasicInfo info = new PatientBasicInfo();
        info.setId(entity.getId());
        info.setName(entity.getName());
        info.setPhone(entity.getPhone());
        // ... 其他字段
        return info;
    }
}
```

### 4. 在查询服务中使用

```java
@Service
@Transactional(readOnly = true)
public class ReferralQueryService {
    
    @Autowired
    private ReferralFormRepository referralRepository;
    
    @Autowired
    private ReferralMapper referralMapper;
    
    /**
     * 根据ID查询转诊（自动处理关联）
     */
    public ReferralDTO getReferralById(String id) {
        ReferralFormEntity entity = referralRepository.findById(id)
            .orElse(null);
        
        if (entity == null) {
            return null;
        }
        
        ReferralDTO dto = referralMapper.toDTO(entity);
        
        // AOP会自动处理dto中的@EntityRelation注解
        // dto.getPatientInfo() 将自动填充患者信息
        
        return dto;
    }
    
    /**
     * 查询转诊列表（自动批量处理关联）
     */
    public List<ReferralDTO> getReferralsByStatus(Integer status) {
        List<ReferralFormEntity> entities = referralRepository.findByStatus(status);
        List<ReferralDTO> dtos = referralMapper.toDTOList(entities);
        
        // AOP会自动批量处理所有dto的关联关系
        // 相同的basicInfoId只会查询一次数据库
        
        return dtos;
    }
    
    /**
     * 分页查询（也支持自动关联处理）
     */
    public PageResult<ReferralDTO> queryReferrals(ReferralQueryDTO queryDTO, PageRequest pageRequest) {
        // 执行分页查询
        PageResult<ReferralFormEntity> entityPage = referralRepository.findByPageRequest(pageRequest);
        
        // 转换为DTO
        List<ReferralDTO> dtos = referralMapper.toDTOList(entityPage.getContent());
        
        // AOP自动处理关联关系
        
        return PageResult.of(dtos, entityPage.getPage(), entityPage.getSize(), entityPage.getTotal());
    }
}
```

## 🔧 高级用法

### 1. 多种关联类型

```java
public class OrderDTO {
    
    // 一对一关联
    @EntityRelation(
        entityType = "customer",
        type = RelationType.ONE_TO_ONE
    )
    private String customerId;
    private Object customerInfo;
    
    // 一对多关联
    @EntityRelation(
        entityType = "order_item",
        type = RelationType.ONE_TO_MANY
    )
    private String orderId;  // 这里是自己的ID
    private List<Object> orderItems;
    
    // 多对一关联
    @EntityRelation(
        entityType = "product",
        type = RelationType.MANY_TO_ONE
    )
    private String productId;
    private Object productInfo;
}
```

### 2. 条件加载器

```java
@Service
@RelationProvider(entityTypes = {"patient"})
public class PatientRelationService {
    
    /**
     * 优先从缓存加载
     */
    @RelationLoader(
        entityTypes = {"patient"},
        priority = 0
    )
    @ConditionalOnProperty("feature.patient.cache.enabled")
    public PatientBasicInfo loadFromCache(String patientId) {
        // 从Redis等缓存加载
        return cacheService.get("patient:" + patientId);
    }
    
    /**
     * 从数据库加载（备用）
     */
    @RelationLoader(
        entityTypes = {"patient"},
        priority = 1
    )
    public PatientBasicInfo loadFromDatabase(String patientId) {
        // 从数据库加载
        return patientRepository.findById(patientId)
            .map(this::convert)
            .orElse(null);
    }
}
```

### 3. 自定义关联字段名

```java
public class ReferralDTO {
    
    @EntityRelation(
        entityType = "patient",
        relationField = "id"
    )
    private String basicInfoId;
    
    // 自动设置到 patient 字段（去掉Id后缀）
    private Object patient;
    
    // 或者显式指定字段名
    @EntityRelation(
        entityType = "doctor",
        relationField = "id"
    )
    private String doctorId;
    private Object doctorData;  // 设置到 doctorData 字段
}
```

### 4. 嵌套关联

```java
// 患者信息中也可以有关联
public class PatientBasicInfo {
    private String id;
    private String name;
    
    @EntityRelation(
        entityType = "organization",
        type = RelationType.MANY_TO_ONE
    )
    private String orgId;
    private Object organization;  // 自动填充机构信息
}

// 需要在机构模块中提供加载器
@Service
@RelationProvider(entityTypes = {"organization"})
public class OrganizationRelationService {
    
    @RelationLoader(entityTypes = {"organization"})
    public OrganizationInfo loadOrganization(String orgId) {
        // 加载机构信息
    }
}
```

## 🎯 最佳实践

### 1. 性能优化

```java
// ✅ 推荐：使用批量加载器
@RelationLoader(
    entityTypes = {"patient"},
    supportBatch = true,
    priority = 0
)
public Map<String, PatientInfo> batchLoad(List<String> ids) {
    // 一次查询多个记录
    return patientRepository.findAllById(ids)
        .stream()
        .collect(Collectors.toMap(
            PatientEntity::getId,
            this::convert
        ));
}

// ❌ 不推荐：只有单个加载器（会导致N+1查询）
@RelationLoader(entityTypes = {"patient"})
public PatientInfo singleLoad(String id) {
    return patientRepository.findById(id).orElse(null);
}
```

### 2. 缓存策略

```java
// 热点数据长缓存
@RelationLoader(
    entityTypes = {"user"},
    cacheSeconds = 1800  // 30分钟
)
public UserInfo loadUser(String userId) { ... }

// 变化频繁的数据短缓存
@RelationLoader(
    entityTypes = {"order_status"},
    cacheSeconds = 60    // 1分钟
)
public OrderStatus loadOrderStatus(String orderId) { ... }

// 基础数据长缓存
@RelationLoader(
    entityTypes = {"dictionary"},
    cacheSeconds = 3600  // 1小时
)
public DictItem loadDictItem(String code) { ... }
```

### 3. 错误处理

```java
@RelationLoader(entityTypes = {"patient"})
public PatientInfo loadPatient(String patientId) {
    try {
        return patientRepository.findById(patientId)
            .map(this::convert)
            .orElse(null);  // 返回null表示未找到
    } catch (Exception e) {
        log.error("加载患者信息失败: patientId={}", patientId, e);
        return null;  // 异常时返回null，不影响主流程
    }
}
```

### 4. 日志和监控

```java
@RelationLoader(entityTypes = {"patient"})
public Map<String, PatientInfo> batchLoadPatients(List<String> patientIds) {
    long startTime = System.currentTimeMillis();
    
    try {
        Map<String, PatientInfo> result = doLoad(patientIds);
        
        long duration = System.currentTimeMillis() - startTime;
        log.info("批量加载患者信息完成: count={}, duration={}ms", 
            patientIds.size(), duration);
        
        return result;
    } catch (Exception e) {
        log.error("批量加载患者信息失败: ids={}", patientIds, e);
        return new HashMap<>();
    }
}
```

## 🔍 调试和故障排除

### 1. 启用调试日志

```yaml
logging:
  level:
    com.hys.hm.shared.framework.relation: DEBUG
```

### 2. 检查注册情况

```java
@Autowired
private RelationManager relationManager;

@PostConstruct
public void checkRegistration() {
    // 查看注册的提供者
    Map<String, List<String>> providers = relationManager.getProviderRegistry();
    log.info("注册的关联提供者: {}", providers);
    
    // 查看注册的加载器
    Map<String, List<LoaderInfo>> loaders = relationManager.getLoaderRegistry();
    log.info("注册的关联加载器: {}", loaders);
}
```

### 3. 手动处理关联（调试用）

```java
@Autowired
private RelationManager relationManager;

public void debugRelation() {
    ReferralDTO dto = new ReferralDTO();
    dto.setBasicInfoId("patient-001");
    
    // 手动处理关联关系
    relationManager.processRelations(dto);
    
    // 检查结果
    log.info("关联处理结果: {}", dto.getPatientInfo());
}
```

## ⚠️ 注意事项

### 1. 避免循环关联

```java
// ❌ 错误：可能导致无限循环
public class PatientInfo {
    @EntityRelation(entityType = "referral")
    private String patientId;
    private List<Object> referrals;
}

public class ReferralInfo {
    @EntityRelation(entityType = "patient")
    private String basicInfoId;
    private Object patient;
}
```

### 2. 关联字段命名规范

```java
// ✅ 推荐的命名方式
private String patientId;      // 关联ID字段
private Object patientInfo;    // 关联数据字段（加Info后缀）

private String doctorId;
private Object doctor;         // 或者直接用实体名

private String orgId;
private Object organization;   // 完整名称
```

### 3. 事务边界

```java
// 关联数据加载器会在查询事务中执行
// 确保加载器方法也是只读事务
@RelationLoader(entityTypes = {"patient"})
@Transactional(readOnly = true)  // 推荐加上
public PatientInfo loadPatient(String patientId) {
    return patientRepository.findById(patientId).orElse(null);
}
```

这个使用指南涵盖了从基础使用到高级特性的完整内容，您可以根据实际需求选择合适的使用方式。有什么具体问题可以随时询问！
