# 患者模块架构重构方案

## 问题分析

### 当前架构问题

1. **违反DDD分层原则**：
   - `hm-domain-patient` 模块包含了Repository接口（应该在Infrastructure层）
   - 混合了领域服务和应用服务的职责
   - 缺少明确的聚合根和值对象设计

2. **不符合项目模块化架构**：
   - 项目采用"微服务就绪的模块化单体架构"
   - 应该严格按照 Application → Domain → Infrastructure 分层
   - 当前所有功能都堆积在domain层

3. **缺少对应的分层模块**：
   - 缺少 `hm-application-patient` 应用服务层
   - 缺少 `hm-infrastructure-persistence-patient` 持久化层

## 重构方案

### 目标架构

```
患者模块架构
├── hm-application-patient/              # 应用服务层
│   ├── service/                         # 应用服务
│   │   ├── PatientApplicationService    # 患者应用服务
│   │   └── PatientQueryService          # 患者查询服务
│   └── dto/                             # 数据传输对象
│       ├── PatientCreateDTO
│       ├── PatientUpdateDTO
│       └── PatientQueryDTO
│
├── hm-domain-patient/                   # 领域层（重构）
│   ├── aggregate/                       # 聚合根
│   │   └── Patient                      # 患者聚合根
│   ├── entity/                          # 实体
│   │   ├── PatientBasicInfo             # 患者基本信息实体
│   │   ├── PatientHealthInfo            # 患者健康信息实体
│   │   └── PatientContactInfo           # 患者联系信息实体
│   ├── valueobject/                     # 值对象
│   │   ├── PatientId                    # 患者ID值对象
│   │   ├── IdCard                       # 身份证值对象
│   │   ├── PhoneNumber                  # 电话号码值对象
│   │   └── BMI                          # BMI值对象
│   ├── service/                         # 领域服务
│   │   ├── PatientDomainService         # 患者领域服务
│   │   └── PatientValidationService     # 患者验证服务
│   ├── repository/                      # 仓储接口（只定义接口）
│   │   └── PatientRepository            # 患者仓储接口
│   └── event/                           # 领域事件
│       ├── PatientCreatedEvent
│       └── PatientDeletedEvent
│
└── hm-infrastructure-persistence-patient/  # 基础设施层（新建）
    ├── entity/                          # JPA实体
    │   └── PatientBasicInfoEntity       # 数据库实体
    ├── repository/                      # 仓储实现
    │   └── PatientRepositoryImpl        # 患者仓储实现
    └── mapper/                          # 对象映射
        └── PatientMapper                # 领域对象与JPA实体映射
```

## 重构步骤

### 第一步：创建应用服务层模块

#### 1. 创建 hm-application-patient 模块

```xml
<!-- hm-application-patient/pom.xml -->
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>
    
    <parent>
        <groupId>com.hys</groupId>
        <artifactId>hm-application</artifactId>
        <version>3.0.1-SNAPSHOT</version>
    </parent>
    
    <artifactId>hm-application-patient</artifactId>
    <name>hm-application-patient</name>
    <description>患者应用服务模块</description>
    
    <dependencies>
        <!-- 患者领域模块 -->
        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-domain-patient</artifactId>
        </dependency>
        
        <!-- 共享框架 -->
        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-shared-framework</artifactId>
        </dependency>
        
        <!-- 共享通用模块 -->
        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-shared-common</artifactId>
        </dependency>
        
        <!-- 共享类型模块 -->
        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-shared-types</artifactId>
        </dependency>
    </dependencies>
</project>
```

#### 2. 患者应用服务

```java
// hm-application-patient/src/main/java/com/hys/hm/application/patient/service/PatientApplicationService.java
package com.hys.hm.application.patient.service;

import com.hys.hm.application.patient.dto.*;
import com.hys.hm.domain.patient.aggregate.Patient;
import com.hys.hm.domain.patient.repository.PatientRepository;
import com.hys.hm.domain.patient.service.PatientDomainService;
import com.hys.hm.shared.framework.event.DomainEventPublisher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 患者应用服务
 * 负责协调领域对象完成业务用例
 */
@Service
@Transactional
@RequiredArgsConstructor
@Slf4j
public class PatientApplicationService {
    
    private final PatientRepository patientRepository;
    private final PatientDomainService patientDomainService;
    private final DomainEventPublisher eventPublisher;
    private final PatientMapper patientMapper;
    
    /**
     * 创建患者
     */
    public PatientDTO createPatient(PatientCreateDTO createDTO) {
        log.info("创建患者: {}", createDTO.getName());
        
        // 1. 领域验证
        patientDomainService.validatePatientCreation(createDTO);
        
        // 2. 创建患者聚合
        Patient patient = Patient.create(
            createDTO.getName(),
            createDTO.getIdCard(),
            createDTO.getPhone(),
            createDTO.getBirthday(),
            createDTO.getSex()
        );
        
        // 3. 保存患者
        Patient savedPatient = patientRepository.save(patient);
        
        // 4. 发布领域事件
        eventPublisher.publish(savedPatient.getUncommittedEvents());
        
        // 5. 返回DTO
        return patientMapper.toDTO(savedPatient);
    }
    
    /**
     * 更新患者信息
     */
    public PatientDTO updatePatient(String patientId, PatientUpdateDTO updateDTO) {
        log.info("更新患者信息: patientId={}", patientId);
        
        // 1. 获取患者聚合
        Patient patient = patientRepository.findById(patientId)
            .orElseThrow(() -> new PatientNotFoundException(patientId));
        
        // 2. 更新患者信息
        patient.updateBasicInfo(
            updateDTO.getName(),
            updateDTO.getPhone(),
            updateDTO.getAddress()
        );
        
        // 3. 保存患者
        Patient savedPatient = patientRepository.save(patient);
        
        // 4. 发布领域事件
        eventPublisher.publish(savedPatient.getUncommittedEvents());
        
        return patientMapper.toDTO(savedPatient);
    }
    
    /**
     * 删除患者
     */
    public void deletePatient(String patientId, String deleteReason) {
        log.info("删除患者: patientId={}, reason={}", patientId, deleteReason);
        
        // 1. 获取患者聚合
        Patient patient = patientRepository.findById(patientId)
            .orElseThrow(() -> new PatientNotFoundException(patientId));
        
        // 2. 领域验证
        patientDomainService.validatePatientDeletion(patient);
        
        // 3. 删除患者
        patient.delete(deleteReason);
        
        // 4. 保存患者
        patientRepository.save(patient);
        
        // 5. 发布领域事件
        eventPublisher.publish(patient.getUncommittedEvents());
    }
}
```

#### 3. 患者查询服务

```java
// hm-application-patient/src/main/java/com/hys/hm/application/patient/service/PatientQueryService.java
package com.hys.hm.application.patient.service;

import com.hys.hm.application.patient.dto.PatientDTO;
import com.hys.hm.application.patient.dto.PatientQueryDTO;
import com.hys.hm.domain.patient.repository.PatientRepository;
import com.hys.hm.shared.framework.base.PageResult;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 患者查询服务
 * 供其他模块调用，获取患者相关信息
 */
@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
public class PatientQueryService {
    
    private final PatientRepository patientRepository;
    private final PatientMapper patientMapper;
    
    /**
     * 获取患者基础信息
     */
    public PatientDTO getPatientBasicInfo(String patientId) {
        return patientRepository.findById(patientId)
            .map(patientMapper::toDTO)
            .orElse(null);
    }
    
    /**
     * 批量获取患者基础信息
     */
    public List<PatientDTO> batchGetPatientBasicInfo(List<String> patientIds) {
        return patientRepository.findByIds(patientIds)
            .stream()
            .map(patientMapper::toDTO)
            .collect(Collectors.toList());
    }
    
    /**
     * 检查患者是否存在
     */
    public boolean existsPatient(String patientId) {
        return patientRepository.existsById(patientId);
    }
    
    /**
     * 根据身份证号查找患者
     */
    public Optional<PatientDTO> findPatientByIdCard(String idCard) {
        return patientRepository.findByIdCard(idCard)
            .map(patientMapper::toDTO);
    }
    
    /**
     * 分页查询患者
     */
    public PageResult<PatientDTO> queryPatients(PatientQueryDTO queryDTO) {
        PageResult<Patient> pageResult = patientRepository.queryPatients(queryDTO);
        
        List<PatientDTO> dtoList = pageResult.getContent()
            .stream()
            .map(patientMapper::toDTO)
            .collect(Collectors.toList());
            
        return PageResult.of(dtoList, pageResult.getTotal(), pageResult.getPageable());
    }
}
```

### 第二步：重构领域层

#### 1. 患者聚合根

```java
// hm-domain-patient/src/main/java/com/hys/hm/domain/patient/aggregate/Patient.java
package com.hys.hm.domain.patient.aggregate;

import com.hys.hm.domain.patient.entity.PatientBasicInfo;
import com.hys.hm.domain.patient.entity.PatientHealthInfo;
import com.hys.hm.domain.patient.entity.PatientContactInfo;
import com.hys.hm.domain.patient.valueobject.*;
import com.hys.hm.domain.patient.event.PatientCreatedEvent;
import com.hys.hm.domain.patient.event.PatientDeletedEvent;
import com.hys.hm.shared.framework.base.AggregateRoot;
import lombok.Getter;

/**
 * 患者聚合根
 * 负责维护患者的完整性和业务规则
 */
@Getter
public class Patient extends AggregateRoot<PatientId> {
    
    private PatientBasicInfo basicInfo;
    private PatientHealthInfo healthInfo;
    private PatientContactInfo contactInfo;
    
    // 私有构造函数，通过工厂方法创建
    private Patient(PatientId id, PatientBasicInfo basicInfo) {
        super(id);
        this.basicInfo = basicInfo;
        this.healthInfo = new PatientHealthInfo();
        this.contactInfo = new PatientContactInfo();
    }
    
    /**
     * 创建患者（工厂方法）
     */
    public static Patient create(String name, String idCard, String phone, 
                               LocalDate birthday, String sex) {
        // 1. 创建值对象
        PatientId patientId = PatientId.generate();
        IdCard idCardVO = IdCard.of(idCard);
        PhoneNumber phoneVO = PhoneNumber.of(phone);
        
        // 2. 创建基本信息实体
        PatientBasicInfo basicInfo = PatientBasicInfo.create(
            name, idCardVO, phoneVO, birthday, sex
        );
        
        // 3. 创建患者聚合
        Patient patient = new Patient(patientId, basicInfo);
        
        // 4. 发布领域事件
        patient.addDomainEvent(new PatientCreatedEvent(patient));
        
        return patient;
    }
    
    /**
     * 更新基本信息
     */
    public void updateBasicInfo(String name, String phone, String address) {
        PhoneNumber phoneVO = PhoneNumber.of(phone);
        this.basicInfo.updateInfo(name, phoneVO, address);
        
        // 发布更新事件
        this.addDomainEvent(new PatientUpdatedEvent(this));
    }
    
    /**
     * 更新健康信息
     */
    public void updateHealthInfo(Double height, Double weight) {
        BMI bmi = BMI.calculate(height, weight);
        this.healthInfo.updateHealthMetrics(height, weight, bmi);
    }
    
    /**
     * 删除患者
     */
    public void delete(String deleteReason) {
        this.markAsDeleted();
        
        // 发布删除事件
        this.addDomainEvent(new PatientDeletedEvent(this, deleteReason));
    }
    
    /**
     * 业务规则：检查是否可以删除
     */
    public boolean canBeDeleted() {
        // 业务规则：有活跃转诊记录的患者不能删除
        return !hasActiveReferrals();
    }
    
    private boolean hasActiveReferrals() {
        // 这里可以通过领域服务查询
        return false; // 简化实现
    }
}
```

#### 2. 值对象定义

```java
// hm-domain-patient/src/main/java/com/hys/hm/domain/patient/valueobject/PatientId.java
package com.hys.hm.domain.patient.valueobject;

import com.hys.hm.shared.framework.base.ValueObject;
import lombok.Value;

/**
 * 患者ID值对象
 */
@Value
public class PatientId implements ValueObject {
    String value;
    
    private PatientId(String value) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("患者ID不能为空");
        }
        this.value = value;
    }
    
    public static PatientId of(String value) {
        return new PatientId(value);
    }
    
    public static PatientId generate() {
        return new PatientId(UUID.randomUUID().toString().replace("-", ""));
    }
}
```

```java
// hm-domain-patient/src/main/java/com/hys/hm/domain/patient/valueobject/IdCard.java
package com.hys.hm.domain.patient.valueobject;

import com.hys.hm.shared.framework.base.ValueObject;
import lombok.Value;

/**
 * 身份证号值对象
 */
@Value
public class IdCard implements ValueObject {
    String number;
    
    private IdCard(String number) {
        if (!isValid(number)) {
            throw new IllegalArgumentException("身份证号格式不正确");
        }
        this.number = number;
    }
    
    public static IdCard of(String number) {
        return new IdCard(number);
    }
    
    private boolean isValid(String number) {
        // 身份证号验证逻辑
        return number != null && number.matches("^[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx]$");
    }
    
    /**
     * 获取出生日期
     */
    public LocalDate getBirthDate() {
        String birthStr = number.substring(6, 14);
        return LocalDate.parse(birthStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
    }
    
    /**
     * 获取性别
     */
    public String getSex() {
        int sexCode = Integer.parseInt(number.substring(16, 17));
        return sexCode % 2 == 0 ? "女" : "男";
    }
}
```

### 第三步：创建基础设施层

#### 1. 创建 hm-infrastructure-persistence-patient 模块

```xml
<!-- hm-infrastructure-persistence-patient/pom.xml -->
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>
    
    <parent>
        <groupId>com.hys</groupId>
        <artifactId>hm-infrastructure</artifactId>
        <version>3.0.1-SNAPSHOT</version>
    </parent>
    
    <artifactId>hm-infrastructure-persistence-patient</artifactId>
    <name>hm-infrastructure-persistence-patient</name>
    <description>患者持久化基础设施模块</description>
    
    <dependencies>
        <!-- 患者领域模块 -->
        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-domain-patient</artifactId>
        </dependency>
        
        <!-- Spring Boot Starter Data JPA -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
    </dependencies>
</project>
```

#### 2. 仓储实现

```java
// hm-infrastructure-persistence-patient/src/main/java/com/hys/hm/infrastructure/persistence/patient/repository/PatientRepositoryImpl.java
package com.hys.hm.infrastructure.persistence.patient.repository;

import com.hys.hm.domain.patient.aggregate.Patient;
import com.hys.hm.domain.patient.repository.PatientRepository;
import com.hys.hm.domain.patient.valueobject.PatientId;
import com.hys.hm.infrastructure.persistence.patient.entity.PatientBasicInfoEntity;
import com.hys.hm.infrastructure.persistence.patient.mapper.PatientMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

/**
 * 患者仓储实现
 */
@Repository
@RequiredArgsConstructor
public class PatientRepositoryImpl implements PatientRepository {
    
    private final PatientJpaRepository jpaRepository;
    private final PatientMapper patientMapper;
    
    @Override
    public Optional<Patient> findById(PatientId patientId) {
        return jpaRepository.findById(patientId.getValue())
            .map(patientMapper::toDomain);
    }
    
    @Override
    public Patient save(Patient patient) {
        PatientBasicInfoEntity entity = patientMapper.toEntity(patient);
        PatientBasicInfoEntity savedEntity = jpaRepository.save(entity);
        return patientMapper.toDomain(savedEntity);
    }
    
    @Override
    public void delete(PatientId patientId) {
        jpaRepository.deleteById(patientId.getValue());
    }
    
    @Override
    public boolean existsById(PatientId patientId) {
        return jpaRepository.existsById(patientId.getValue());
    }
    
    @Override
    public Optional<Patient> findByIdCard(String idCard) {
        return jpaRepository.findByIdcard(idCard)
            .map(patientMapper::toDomain);
    }
}
```

## 重构收益

### 1. 清晰的职责分离
- **Application层**：协调领域对象完成业务用例
- **Domain层**：纯粹的业务逻辑和规则
- **Infrastructure层**：技术实现细节

### 2. 符合DDD原则
- 聚合根管理业务完整性
- 值对象封装业务概念
- 领域服务处理跨聚合业务

### 3. 易于测试
- 领域逻辑与技术实现分离
- 可以独立测试业务规则

### 4. 为微服务拆分做准备
- 清晰的模块边界
- 标准化的接口定义
- 事件驱动的解耦设计

## 迁移计划

### 阶段一：创建新模块结构
1. 创建 `hm-application-patient` 模块
2. 创建 `hm-infrastructure-persistence-patient` 模块
3. 重构 `hm-domain-patient` 模块

### 阶段二：迁移现有代码
1. 将应用服务逻辑迁移到Application层
2. 将Repository实现迁移到Infrastructure层
3. 重构Entity为聚合根和值对象

### 阶段三：更新依赖关系
1. 更新其他模块的依赖引用
2. 更新启动类的扫描路径
3. 更新配置文件

### 阶段四：测试验证
1. 单元测试验证
2. 集成测试验证
3. 功能测试验证

这样的重构将使患者模块完全符合项目的架构设计原则，为未来的扩展和微服务拆分奠定良好基础。

## 技术约束解决方案

### 问题：BaseRepository的JPA实体依赖

由于BaseRepository需要JPA实体作为泛型参数：
```java
public interface BaseRepository<T, ID extends Serializable>
```

这导致PatientRepository必须引用JPA实体类，产生了技术约束。

### 解决方案对比

#### 方案一：实用主义方案（推荐）

**核心思想**：在Domain层保留JPA实体，但明确其职责边界和使用约束。

**实现方式**：
```java
// hm-domain-patient/src/main/java/com/hys/hm/domain/patient/entity/PatientBasicInfoEntity.java
@Entity
@Table(name = "dp_basic_info")
@Data
@EqualsAndHashCode(callSuper = true)
@Comment("患者基本信息表")
public class PatientBasicInfoEntity extends BaseEntity<String> {
    // JPA实体定义，但仅作为数据载体
    // 不包含业务逻辑，只有数据映射
}

// hm-domain-patient/src/main/java/com/hys/hm/domain/patient/repository/PatientRepository.java
@Repository
public interface PatientRepository extends BaseRepository<PatientBasicInfoEntity, String> {
    // 仓储接口定义
    Optional<PatientBasicInfoEntity> findByIdcard(String idcard);
    List<PatientBasicInfoEntity> findByPhone(String phone);
}

// hm-domain-patient/src/main/java/com/hys/hm/domain/patient/aggregate/Patient.java
public class Patient extends AggregateRoot<PatientId> {
    // 纯业务逻辑的聚合根
    // 通过工厂方法与Entity转换

    public static Patient fromEntity(PatientBasicInfoEntity entity) {
        // 从Entity创建聚合根
    }

    public PatientBasicInfoEntity toEntity() {
        // 转换为Entity用于持久化
    }
}
```

**优势**：
- ✅ 简单直接，不增加复杂性
- ✅ 充分利用BaseRepository的功能
- ✅ 符合项目现有架构风格
- ✅ 开发效率高

**约束**：
- 📋 Entity仅作为数据载体，不包含业务逻辑
- 📋 业务逻辑全部在聚合根中实现
- 📋 通过工厂方法进行Entity与聚合根的转换

#### 方案二：适配器模式方案

**核心思想**：在Infrastructure层创建适配器，完全隔离JPA依赖。

**实现方式**：
```java
// hm-domain-patient/src/main/java/com/hys/hm/domain/patient/repository/PatientRepository.java
public interface PatientRepository {
    // 纯领域接口，不依赖JPA
    Optional<Patient> findById(PatientId id);
    Patient save(Patient patient);
    Optional<Patient> findByIdCard(String idCard);
}

// hm-infrastructure-persistence-patient/src/main/java/.../PatientJpaRepository.java
@Repository
public interface PatientJpaRepository extends BaseRepository<PatientBasicInfoEntity, String> {
    // JPA仓储接口
}

// hm-infrastructure-persistence-patient/src/main/java/.../PatientRepositoryImpl.java
@Repository
public class PatientRepositoryImpl implements PatientRepository {

    @Autowired
    private PatientJpaRepository jpaRepository;

    @Override
    public Optional<Patient> findById(PatientId id) {
        return jpaRepository.findById(id.getValue())
            .map(this::entityToDomain);
    }

    @Override
    public Patient save(Patient patient) {
        PatientBasicInfoEntity entity = domainToEntity(patient);
        PatientBasicInfoEntity saved = jpaRepository.save(entity);
        return entityToDomain(saved);
    }
}
```

**优势**：
- ✅ 完全符合DDD原则
- ✅ 领域层完全独立
- ✅ 易于单元测试

**劣势**：
- ❌ 增加了代码复杂性
- ❌ 需要额外的转换逻辑
- ❌ 可能影响性能

#### 方案三：混合方案

**核心思想**：将Entity放在shared模块，供domain和infrastructure共享。

**实现方式**：
```java
// hm-shared-types/src/main/java/com/hys/hm/shared/types/entity/PatientBasicInfoEntity.java
@Entity
@Table(name = "dp_basic_info")
public class PatientBasicInfoEntity extends BaseEntity<String> {
    // 共享的JPA实体
}

// hm-domain-patient依赖hm-shared-types
// hm-infrastructure-persistence-patient依赖hm-shared-types
```

**优势**：
- ✅ 避免了循环依赖
- ✅ 实体可以被多个模块使用

**劣势**：
- ❌ 违反了模块独立性原则
- ❌ 增加了模块间耦合

### 推荐方案：实用主义方案

基于项目的实际情况和技术栈，推荐使用**方案一：实用主义方案**，原因如下：

1. **符合项目风格**：项目已经大量使用BaseRepository，这种方案与现有架构一致
2. **开发效率**：不增加额外的复杂性，开发效率高
3. **功能完整**：充分利用BaseRepository的强大功能
4. **易于维护**：代码结构清晰，易于理解和维护

### 实用主义方案的实施细节

#### 1. 明确Entity的职责边界

```java
/**
 * 患者基本信息实体类
 *
 * 职责说明：
 * 1. 仅作为数据载体，映射数据库表结构
 * 2. 不包含任何业务逻辑
 * 3. 通过聚合根进行业务操作
 * 4. 提供基础的数据验证（如@NotNull等）
 *
 * 使用约束：
 * 1. 禁止在Entity中添加业务方法
 * 2. 禁止在Entity中进行复杂计算
 * 3. 所有业务逻辑必须在Patient聚合根中实现
 */
@Entity
@Table(name = "dp_basic_info")
@Data
@EqualsAndHashCode(callSuper = true)
@Comment("患者基本信息表")
public class PatientBasicInfoEntity extends BaseEntity<String> {

    @Id
    @Column(name = "ID", length = 32)
    @Comment("主键ID")
    private String id;

    @Column(name = "NAME", length = 50)
    @Comment("姓名")
    @NotBlank(message = "姓名不能为空")
    private String name;

    // 其他字段...

    // 仅提供数据访问方法，不包含业务逻辑
}
```

#### 2. 聚合根与Entity的转换

```java
public class Patient extends AggregateRoot<PatientId> {

    /**
     * 从Entity创建聚合根（工厂方法）
     */
    public static Patient fromEntity(PatientBasicInfoEntity entity) {
        if (entity == null) {
            return null;
        }

        Patient patient = new Patient();
        patient.id = PatientId.of(entity.getId());
        patient.name = entity.getName();
        patient.idCard = IdCard.of(entity.getIdcard());
        patient.phone = PhoneNumber.of(entity.getPhone());
        // 其他字段转换...

        return patient;
    }

    /**
     * 转换为Entity用于持久化
     */
    public PatientBasicInfoEntity toEntity() {
        PatientBasicInfoEntity entity = new PatientBasicInfoEntity();
        entity.setId(this.id.getValue());
        entity.setName(this.name);
        entity.setIdcard(this.idCard.getNumber());
        entity.setPhone(this.phone.getNumber());
        // 其他字段转换...

        return entity;
    }

    /**
     * 更新Entity（用于更新操作）
     */
    public void updateEntity(PatientBasicInfoEntity entity) {
        entity.setName(this.name);
        entity.setPhone(this.phone.getNumber());
        // 只更新变更的字段...
    }
}
```

#### 3. 应用服务中的使用

```java
@Service
@Transactional
public class PatientApplicationService {

    @Autowired
    private PatientRepository patientRepository;

    public PatientDTO createPatient(PatientCreateDTO createDTO) {
        // 1. 创建聚合根
        Patient patient = Patient.create(
            createDTO.getName(),
            createDTO.getIdCard(),
            createDTO.getPhone()
        );

        // 2. 转换为Entity并保存
        PatientBasicInfoEntity entity = patient.toEntity();
        PatientBasicInfoEntity savedEntity = patientRepository.save(entity);

        // 3. 从保存的Entity重新创建聚合根（获取生成的ID等）
        Patient savedPatient = Patient.fromEntity(savedEntity);

        // 4. 发布领域事件
        eventPublisher.publish(savedPatient.getUncommittedEvents());

        return PatientMapper.toDTO(savedPatient);
    }

    public PatientDTO updatePatient(String patientId, PatientUpdateDTO updateDTO) {
        // 1. 查询Entity
        PatientBasicInfoEntity entity = patientRepository.findById(patientId)
            .orElseThrow(() -> new PatientNotFoundException(patientId));

        // 2. 转换为聚合根
        Patient patient = Patient.fromEntity(entity);

        // 3. 执行业务操作
        patient.updateBasicInfo(updateDTO.getName(), updateDTO.getPhone());

        // 4. 更新Entity
        patient.updateEntity(entity);
        PatientBasicInfoEntity savedEntity = patientRepository.save(entity);

        // 5. 发布领域事件
        eventPublisher.publish(patient.getUncommittedEvents());

        return PatientMapper.toDTO(Patient.fromEntity(savedEntity));
    }
}
```

### 总结

实用主义方案在保持DDD核心思想的同时，兼顾了技术约束和开发效率：

1. **业务逻辑集中**：所有业务逻辑都在聚合根中
2. **数据持久化简化**：利用BaseRepository的强大功能
3. **职责边界清晰**：Entity只负责数据映射，聚合根负责业务逻辑
4. **转换机制明确**：通过工厂方法实现Entity与聚合根的转换

这种方案既满足了DDD的核心要求，又充分利用了项目现有的技术框架，是一个平衡的选择。
