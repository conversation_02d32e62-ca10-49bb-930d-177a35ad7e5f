# 项目总览 - 微服务就绪的模块化单体架构

## 🎯 项目定位

本项目采用**微服务就绪的模块化单体架构**（Microservice-Ready Monolith），当前保持单体部署的简单性，同时为未来的微服务拆分做好准备。

## 🏗️ 架构设计

### 分层架构

```
┌─────────────────────────────────────────┐
│           hm-bootstrap (启动层)          │
├─────────────────────────────────────────┤
│         hm-interfaces (接口层)           │
├─────────────────────────────────────────┤
│        hm-application (应用服务层)        │
├─────────────────────────────────────────┤
│          hm-domain (领域层)             │
├─────────────────────────────────────────┤
│      hm-infrastructure (基础设施层)      │
├─────────────────────────────────────────┤
│          hm-shared (共享内核)           │
└─────────────────────────────────────────┘
```

### 模块化设计

按业务域划分模块，每个模块都有清晰的边界：

- **患者管理模块** (Patient Module)
- **转诊管理模块** (Referral Module) ⭐ **标杆示例**
- **健康管理模块** (Health Module)
- **随访管理模块** (Followup Module)
- **知识库模块** (Knowledge Module)
- **沟通模块** (Communication Module)

## ⭐ 标杆示例 - 转诊模块

转诊模块是按照新架构规范实现的**完整标杆示例**，展示了：

### ✅ 完整的分层实现

- **领域层**: 聚合根、值对象、仓储接口、领域服务
- **应用服务层**: 应用服务、DTO、转换器、事件监听
- **基础设施层**: JPA实体、仓储实现、查询服务
- **接口层**: REST控制器、异常处理、API文档

### ✅ Framework封装使用

- **BaseEntity**: 实体继承获得审计字段和通用功能
- **BaseRepository**: 仓储继承获得完整CRUD和动态查询
- **BaseService**: 应用服务继承获得标准业务操作
- **BaseController**: 控制器继承获得REST API标准实现

### ✅ 事件驱动机制

- 基于Spring Events的内部事件机制
- 转诊创建、状态变更事件发布
- 异步事件处理保证性能
- 为未来微服务间通信做准备

### ✅ 完整的业务功能

```java
// 创建转诊表单
ReferralCreateDTO createDTO = ReferralCreateDTO.builder()
    .basicInfoId("patient-001")
    .patientName("张三")
    .referralReason("心脏病需要专科治疗")
    .outUnitName("北京市第一医院")
    .inUnitName("北京协和医院")
    .build();

ReferralQueryDTO result = referralApplicationService.createReferralForm(createDTO, "operator-001");

// 转诊状态流转
referralApplicationService.confirmReferral(referralId, "operator-001");
referralApplicationService.rejectReferral(referralId, "医疗资源不足", "operator-001");
referralApplicationService.completeReferral(referralId, "operator-001");

// 多维度查询
List<ReferralSummaryDTO> patientReferrals = referralApplicationService.getPatientReferralSummary("patient-001");
List<ReferralSummaryDTO> pendingReferrals = referralApplicationService.getPendingReferrals();
List<ReferralSummaryDTO> urgentReferrals = referralApplicationService.getUrgentReferrals();
```

### ✅ REST API设计

```bash
# 创建转诊表单
POST /api/referrals
{
  "basicInfoId": "patient-001",
  "patientName": "张三",
  "referralReason": "心脏病需要专科治疗",
  "outUnitName": "北京市第一医院",
  "inUnitName": "北京协和医院"
}

# 转诊状态操作
PUT /api/referrals/{id}/confirm     # 确认转诊
PUT /api/referrals/{id}/reject      # 拒绝转诊
PUT /api/referrals/{id}/complete    # 完成转诊

# 查询接口
GET /api/referrals/{id}                    # 获取转诊详情
GET /api/referrals/patient/{patientId}     # 获取患者转诊记录
GET /api/referrals/pending                 # 获取待处理转诊
GET /api/referrals/urgent                  # 获取紧急转诊
GET /api/referrals/statistics              # 获取统计信息
```

### ✅ 完整的测试覆盖

- 创建转诊表单测试
- 状态流转测试
- 查询功能测试
- 批量操作测试
- 工具方法测试

## 🔧 技术特点

### Framework封装

- **统一的基础类**: 所有实体、仓储、服务、控制器都继承对应的Base类
- **标准化的异常处理**: 统一的异常处理机制和响应格式
- **完整的审计功能**: 自动记录创建时间、更新时间、操作人等信息
- **加密字段支持**: 敏感数据自动加密存储，支持模糊查询

### 事件驱动

- **内部事件总线**: 基于Spring Events实现模块间解耦
- **异步事件处理**: 提高系统性能，避免阻塞主流程
- **事件溯源准备**: 为未来的事件溯源架构做准备

### 数据访问

- **动态查询**: BaseRepository提供强大的动态查询能力
- **软删除支持**: 统一的软删除机制
- **乐观锁**: 防止并发更新冲突
- **分页查询**: 标准化的分页查询实现

## 📁 项目结构

```
hm-health-management/
├── hm-bootstrap/                           # 启动模块
├── hm-interfaces/
│   └── hm-interfaces-web/                  # Web控制器
├── hm-application/
│   ├── hm-application-referral/            # 转诊应用服务 ⭐
│   ├── hm-application-patient/             # 患者应用服务
│   └── hm-application-shared/              # 共享应用服务
├── hm-domain/
│   ├── hm-domain-referral/                 # 转诊领域 ⭐
│   ├── hm-domain-patient/                  # 患者领域
│   └── hm-domain-shared/                   # 共享领域
├── hm-infrastructure/
│   ├── hm-infrastructure-persistence-referral/  # 转诊持久化 ⭐
│   ├── hm-infrastructure-persistence-patient/   # 患者持久化
│   └── hm-infrastructure-external/              # 外部系统集成
├── hm-shared/
│   ├── hm-shared-framework/                # 技术框架
│   ├── hm-shared-common/                   # 通用工具
│   ├── hm-shared-types/                    # 通用类型
│   └── hm-shared-events/                   # 事件定义
└── docs/                                   # 文档
```

## 🚀 开发指南

### 新模块开发流程

1. **参考转诊模块结构** - 严格按照标杆示例的分层结构
2. **使用Framework封装** - 继承所有Base类获得通用功能
3. **实现事件机制** - 定义和发布领域事件
4. **编写完整测试** - 覆盖主要业务场景
5. **完善API文档** - 使用Swagger注解

### 代码规范

- **命名约定**: 遵循Java命名规范和项目约定
- **分层原则**: 严格遵循分层架构，不允许跨层调用
- **异常处理**: 使用统一的异常处理机制
- **日志规范**: 记录关键业务操作和异常信息
- **注释规范**: 使用JavaDoc注释公共API

## 🔮 未来规划

### 微服务拆分准备

当前架构已经为微服务拆分做好准备：

- ✅ **清晰的模块边界**: 每个模块职责明确，依赖关系清晰
- ✅ **标准化的接口**: 模块间通过定义良好的接口通信
- ✅ **事件驱动机制**: 通过事件实现模块间的松耦合
- ✅ **数据隔离**: 每个模块管理自己的数据

### 拆分路径

1. **选择拆分模块** - 优先拆分独立性强、变更频繁的模块
2. **独立部署** - 将选定模块独立部署为微服务
3. **服务发现** - 引入服务注册与发现机制
4. **API网关** - 统一对外服务入口
5. **分布式事务** - 将内部事件机制升级为分布式事件总线

## 📚 相关文档

- 📖 [架构重构规划](./architecture-refactoring-plan.md)
- 🏗️ [模块化单体实现指南](./modular-monolith-implementation.md)
- ⚙️ [模块配置和依赖管理](./module-configuration-guide.md)
- ⭐ [转诊模块标杆示例](./referral-module-guide.md)

## 💡 关键要点

1. **转诊模块是标杆** - 所有新模块都应该参考转诊模块的实现方式
2. **严格使用Framework** - 必须继承Base类，不允许重复造轮子
3. **事件驱动解耦** - 模块间通过事件通信，避免直接依赖
4. **完整的测试覆盖** - 每个模块都要有充分的单元测试
5. **为微服务做准备** - 当前的设计已经为未来拆分做好准备

---

⭐ **记住**: 转诊模块是完整的标杆示例，展示了新架构的所有特性和最佳实践！
