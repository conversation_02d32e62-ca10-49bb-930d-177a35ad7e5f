# 基于Hibernate JPA注解的关联查询使用指南

## 📖 概述

本方案使用标准的Hibernate JPA注解（@ManyToOne、@OneToMany、@OneToOne等）来实现模块间的关联查询，避免循环依赖的同时充分利用Hibernate的ORM功能。

## 🎯 核心优势

### ✅ 标准化
- 使用JPA标准注解，符合行业规范
- 充分利用Hibernate的ORM功能
- 支持懒加载、级联操作等高级特性

### ✅ 性能优化
- 使用JOIN FETCH进行关联查询，避免N+1问题
- 支持批量查询和分页查询
- 可配置的懒加载策略

### ✅ 避免循环依赖
- 通过Repository层的关联查询方法实现
- 不在实体间直接建立双向关联
- 保持模块间的松耦合

## 🏗️ 架构设计

### 1. 实体关联设计

```java
// 转诊实体（主表）
@Entity
@Table(name = "dc_referral_form")
public class ReferralFormEntity extends BaseEntity<String> {
    
    @Column(name = "BASIC_INFO_ID")
    private String basicInfoId;  // 外键字段
    
    /**
     * 关联患者基本信息（多对一）
     * 使用懒加载，避免性能问题
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "BASIC_INFO_ID", insertable = false, updatable = false)
    private PatientBasicInfoEntity patientBasicInfo;
}
```

### 2. Repository关联查询

```java
@Repository
public interface ReferralFormJpaRepository extends BaseRepository<ReferralFormEntity, String> {
    
    /**
     * 查询转诊记录并关联患者信息
     * 使用LEFT JOIN FETCH避免N+1查询
     */
    @Query("SELECT r FROM ReferralFormEntity r " +
           "LEFT JOIN FETCH r.patientBasicInfo p " +
           "WHERE r.id = :id")
    Optional<ReferralFormEntity> findByIdWithPatient(@Param("id") String id);
    
    /**
     * 批量查询转诊记录并关联患者信息
     */
    @Query("SELECT r FROM ReferralFormEntity r " +
           "LEFT JOIN FETCH r.patientBasicInfo p " +
           "WHERE r.status = :status")
    List<ReferralFormEntity> findByStatusWithPatient(@Param("status") Integer status);
}
```

## 🔧 使用方式

### 1. 定义实体关联

#### 主表实体（转诊表）
```java
@Entity
@Table(name = "dc_referral_form")
public class ReferralFormEntity extends BaseEntity<String> {
    
    // 外键字段
    @Column(name = "BASIC_INFO_ID", nullable = false)
    private String basicInfoId;
    
    // 关联实体（多对一）
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "BASIC_INFO_ID", insertable = false, updatable = false)
    private PatientBasicInfoEntity patientBasicInfo;
    
    // 其他字段...
}
```

#### 关联表实体（患者表）
```java
@Entity
@Table(name = "dc_patient_basic_info")
public class PatientBasicInfoEntity extends BaseEntity<String> {
    
    @Id
    private String id;
    
    private String name;
    private String phone;
    // 其他字段...
    
    // 注意：不在患者实体中定义反向关联，避免循环依赖
    // 反向查询通过Repository方法实现
}
```

### 2. 创建关联查询方法

```java
@Repository
public interface ReferralFormJpaRepository extends BaseRepository<ReferralFormEntity, String> {
    
    // 单个查询 + 关联
    @Query("SELECT r FROM ReferralFormEntity r " +
           "LEFT JOIN FETCH r.patientBasicInfo p " +
           "WHERE r.id = :id")
    Optional<ReferralFormEntity> findByIdWithPatient(@Param("id") String id);
    
    // 列表查询 + 关联
    @Query("SELECT r FROM ReferralFormEntity r " +
           "LEFT JOIN FETCH r.patientBasicInfo p " +
           "WHERE r.basicInfoId = :basicInfoId")
    List<ReferralFormEntity> findByBasicInfoIdWithPatient(@Param("basicInfoId") String basicInfoId);
    
    // 条件查询 + 关联
    @Query("SELECT r FROM ReferralFormEntity r " +
           "LEFT JOIN FETCH r.patientBasicInfo p " +
           "WHERE r.status = :status")
    List<ReferralFormEntity> findByStatusWithPatient(@Param("status") Integer status);
    
    // 分页查询 + 关联
    @Query("SELECT r FROM ReferralFormEntity r " +
           "LEFT JOIN FETCH r.patientBasicInfo p")
    Page<ReferralFormEntity> findAllWithPatient(Pageable pageable);
}
```

### 3. 在Service中使用

```java
@Service
@Transactional(readOnly = true)
public class ReferralQueryService {
    
    @Autowired
    private ReferralFormRepository referralFormRepository;
    
    /**
     * 获取转诊详情（包含患者信息）
     */
    public Optional<ReferralDTO> getReferralById(String id) {
        // 使用关联查询，一次性获取转诊和患者信息
        return referralFormRepository.findByIdWithPatient(id)
            .map(referralMapper::toDTO);
    }
    
    /**
     * 获取患者的转诊列表（包含患者信息）
     */
    public List<ReferralDTO> getReferralsByPatientId(String basicInfoId) {
        List<ReferralFormEntity> entities = 
            referralFormRepository.findByBasicInfoIdWithPatient(basicInfoId);
        return referralMapper.toDTOList(entities);
    }
}
```

### 4. 在Mapper中处理关联数据

```java
@Component
public class ReferralMapper {
    
    public ReferralDTO toDTO(ReferralFormEntity entity) {
        if (entity == null) {
            return null;
        }
        
        ReferralDTO dto = new ReferralDTO();
        // 设置基本字段
        dto.setId(entity.getId());
        dto.setBasicInfoId(entity.getBasicInfoId());
        // ... 其他字段
        
        // 处理关联的患者信息
        if (entity.getPatientBasicInfo() != null) {
            dto.setPatientInfo(convertPatientInfo(entity.getPatientBasicInfo()));
        }
        
        return dto;
    }
    
    private PatientBasicInfoDTO convertPatientInfo(PatientBasicInfoEntity patientEntity) {
        // 转换患者信息
        PatientBasicInfoDTO patientDTO = new PatientBasicInfoDTO();
        patientDTO.setId(patientEntity.getId());
        patientDTO.setName(patientEntity.getName());
        patientDTO.setPhone(patientEntity.getPhone());
        // ... 其他字段
        return patientDTO;
    }
}
```

## 🚀 高级用法

### 1. 多层关联查询

```java
// 转诊 -> 患者 -> 机构
@Query("SELECT r FROM ReferralFormEntity r " +
       "LEFT JOIN FETCH r.patientBasicInfo p " +
       "LEFT JOIN FETCH p.organization o " +
       "WHERE r.id = :id")
Optional<ReferralFormEntity> findByIdWithPatientAndOrg(@Param("id") String id);
```

### 2. 条件关联查询

```java
// 查询指定机构患者的转诊记录
@Query("SELECT r FROM ReferralFormEntity r " +
       "LEFT JOIN FETCH r.patientBasicInfo p " +
       "WHERE p.orgId = :orgId AND r.status = :status")
List<ReferralFormEntity> findByPatientOrgAndStatus(
    @Param("orgId") String orgId, 
    @Param("status") Integer status);
```

### 3. 聚合查询

```java
// 统计各机构的转诊数量
@Query("SELECT p.orgName, COUNT(r) FROM ReferralFormEntity r " +
       "LEFT JOIN r.patientBasicInfo p " +
       "GROUP BY p.orgId, p.orgName")
List<Object[]> countReferralsByOrg();
```

### 4. 分页关联查询

```java
@Query(value = "SELECT r FROM ReferralFormEntity r " +
               "LEFT JOIN FETCH r.patientBasicInfo p " +
               "WHERE r.status = :status",
       countQuery = "SELECT COUNT(r) FROM ReferralFormEntity r " +
                   "WHERE r.status = :status")
Page<ReferralFormEntity> findByStatusWithPatient(
    @Param("status") Integer status, 
    Pageable pageable);
```

## 🎯 最佳实践

### 1. 关联策略选择

```java
// ✅ 推荐：使用懒加载
@ManyToOne(fetch = FetchType.LAZY)
@JoinColumn(name = "BASIC_INFO_ID", insertable = false, updatable = false)
private PatientBasicInfoEntity patientBasicInfo;

// ❌ 不推荐：急切加载可能导致性能问题
@ManyToOne(fetch = FetchType.EAGER)
private PatientBasicInfoEntity patientBasicInfo;
```

### 2. 查询优化

```java
// ✅ 推荐：使用JOIN FETCH避免N+1查询
@Query("SELECT r FROM ReferralFormEntity r " +
       "LEFT JOIN FETCH r.patientBasicInfo p " +
       "WHERE r.status = :status")
List<ReferralFormEntity> findByStatusWithPatient(@Param("status") Integer status);

// ❌ 不推荐：会产生N+1查询问题
List<ReferralFormEntity> findByStatus(Integer status);
// 然后在代码中访问 entity.getPatientBasicInfo()
```

### 3. 避免循环依赖

```java
// ✅ 推荐：单向关联
@Entity
public class ReferralFormEntity {
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "BASIC_INFO_ID")
    private PatientBasicInfoEntity patientBasicInfo;
}

@Entity
public class PatientBasicInfoEntity {
    // 不定义反向关联，通过Repository查询实现
}

// ❌ 不推荐：双向关联可能导致循环依赖
@Entity
public class PatientBasicInfoEntity {
    @OneToMany(mappedBy = "patientBasicInfo")
    private List<ReferralFormEntity> referralForms; // 可能导致循环依赖
}
```

### 4. 性能监控

```java
// 启用SQL日志，监控查询性能
logging:
  level:
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

// 使用@BatchSize优化集合查询
@OneToMany(mappedBy = "patientBasicInfo")
@BatchSize(size = 20)
private List<ReferralFormEntity> referralForms;
```

## ⚠️ 注意事项

### 1. 懒加载异常
```java
// 确保在事务内访问懒加载属性
@Transactional(readOnly = true)
public ReferralDTO getReferral(String id) {
    ReferralFormEntity entity = repository.findById(id);
    // 在事务内访问关联属性
    entity.getPatientBasicInfo().getName(); // OK
    return mapper.toDTO(entity);
}
```

### 2. 查询性能
```java
// 对于大数据量查询，考虑使用分页
@Query("SELECT r FROM ReferralFormEntity r " +
       "LEFT JOIN FETCH r.patientBasicInfo p")
Page<ReferralFormEntity> findAllWithPatient(Pageable pageable);
```

### 3. 缓存策略
```java
// 对于频繁查询的关联数据，考虑使用二级缓存
@Entity
@Cacheable
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class PatientBasicInfoEntity {
    // ...
}
```

这个基于Hibernate JPA注解的方案既避免了循环依赖，又充分利用了ORM的强大功能，是一个更加标准和高效的解决方案。
