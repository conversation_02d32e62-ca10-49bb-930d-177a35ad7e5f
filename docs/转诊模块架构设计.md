# 转诊模块架构设计

## 概述

转诊模块是健康管理系统的核心业务模块之一，负责处理医院间的患者转诊业务。该模块基于DDD（领域驱动设计）架构，实现了转诊业务的完整生命周期管理。

## 核心特性

### 🏥 业务功能
- **转诊申请**: 支持医生发起患者转诊申请
- **转诊审核**: 转入医院可以接受、拒绝转诊申请
- **状态跟踪**: 完整的转诊状态流转管理
- **紧急转诊**: 支持紧急转诊的优先处理
- **历史查询**: 患者转诊历史记录查询

### 🔧 技术特性
- **避免循环依赖**: 通过接口定义和依赖倒置避免模块间循环依赖
- **事件驱动**: 基于领域事件的异步处理机制
- **数据加密**: 敏感信息自动加密存储
- **软删除**: 支持数据的软删除和恢复
- **分页查询**: 高性能的分页查询支持

## 架构设计

### 模块结构

```
转诊模块
├── hm-domain-referral/              # 领域层
│   ├── entity/                      # 实体类
│   │   └── ReferralFormEntity       # 转诊表单实体
│   ├── repository/                  # 仓储接口
│   │   ├── ReferralFormRepository   # 转诊仓储接口
│   │   ├── ReferralFormJpaRepository # JPA仓储接口
│   │   └── impl/
│   │       └── ReferralFormRepositoryImpl # 仓储实现
│   ├── service/                     # 领域服务
│   │   └── impl/
│   │       └── ReferralDomainServiceImpl # 领域服务实现
│   └── event/                       # 领域事件
│       ├── ReferralCreatedEvent     # 转诊创建事件
│       └── ReferralStatusChangedEvent # 状态变更事件
│
├── hm-application-referral/         # 应用层
│   ├── dto/                         # 数据传输对象
│   │   ├── ReferralCreateDTO        # 创建DTO
│   │   ├── ReferralUpdateDTO        # 更新DTO
│   │   ├── ReferralQueryDTO         # 查询DTO
│   │   └── ReferralDTO              # 转诊DTO
│   ├── mapper/                      # 映射器
│   │   └── ReferralMapper           # 转诊映射器
│   ├── service/                     # 应用服务
│   │   ├── ReferralApplicationService # 转诊应用服务
│   │   ├── ReferralQueryService     # 转诊查询服务
│   │   └── PatientQueryService      # 患者查询服务接口
│   ├── event/                       # 事件监听器
│   │   └── ReferralEventListener    # 转诊事件监听器
│   └── config/                      # 配置类
│       └── ReferralModuleConfig     # 模块配置
│
└── hm-interfaces-web/               # 接口层
    └── controller/
        └── ReferralController       # REST控制器
```

### 关键设计决策

#### 1. 避免循环依赖的设计

**问题**: 转诊模块需要查询患者信息，但不能直接依赖患者模块，否则会造成循环依赖。

**解决方案**: 依赖倒置原则
- 在转诊模块中定义 `PatientQueryService` 接口
- 患者模块实现该接口 (`PatientQueryServiceForReferralImpl`)
- 通过Spring的依赖注入实现解耦

```java
// 转诊模块定义接口
public interface PatientQueryService {
    Optional<PatientBasicInfo> getPatientBasicInfo(String patientId);
    // ... 其他方法
}

// 患者模块实现接口
@Service
public class PatientQueryServiceForReferralImpl implements PatientQueryService {
    // 实现具体逻辑
}
```

#### 2. 实体设计

**ReferralFormEntity** 直接映射数据库表 `dc_referral_form`，包含：
- 患者基本信息关联 (`basic_info_id`)
- 转出/转入医院信息
- 医生信息
- 诊断和病史信息
- 状态和优先级
- 时间戳信息

#### 3. 状态管理

转诊状态流转：
```
待处理(1) → 已接受(2) → 已完成(4)
    ↓           ↓
  已拒绝(3)   已取消(5)
```

#### 4. 事件驱动架构

- **ReferralCreatedEvent**: 转诊创建时触发
- **ReferralStatusChangedEvent**: 状态变更时触发
- 支持同步和异步事件处理
- 事务内和事务后的不同处理阶段

## API接口

### 转诊管理

```http
# 创建转诊
POST /api/referrals
Content-Type: application/json

{
  "basicInfoId": "patient-001",
  "patientName": "张三",
  "fromHospital": "市人民医院",
  "toHospital": "省人民医院",
  "referralReason": "需要进一步检查",
  "impression": "高血压"
}

# 接受转诊
POST /api/referrals/{id}/accept?processOpinion=同意接收

# 拒绝转诊
POST /api/referrals/{id}/reject?processOpinion=患者病情不符合收治条件

# 完成转诊
POST /api/referrals/{id}/complete?processOpinion=患者已成功转入

# 取消转诊
POST /api/referrals/{id}/cancel?reason=患者要求取消
```

### 查询接口

```http
# 获取转诊详情
GET /api/referrals/{id}

# 根据转诊单号查询
GET /api/referrals/by-no/{referralNo}

# 分页查询转诊
GET /api/referrals?page=0&size=20&status=1

# 获取待处理转诊
GET /api/referrals/pending

# 获取紧急转诊
GET /api/referrals/urgent

# 获取今日转诊
GET /api/referrals/today
```

## 数据库设计

### 核心表：dc_referral_form

| 字段名 | 类型 | 说明 | 备注 |
|--------|------|------|------|
| ID | VARCHAR(32) | 主键ID | |
| BASIC_INFO_ID | VARCHAR(32) | 患者基本信息ID | 关联患者表 |
| referral_no | VARCHAR(20) | 转诊单号 | 唯一标识 |
| patient_name | VARCHAR(50) | 患者姓名 | |
| status | INT | 状态 | 1-待处理,2-已接受,3-已拒绝,4-已完成,5-已取消 |
| priority | VARCHAR(20) | 优先级 | NORMAL/URGENT |
| from_hospital | VARCHAR(100) | 转出医院 | |
| to_hospital | VARCHAR(100) | 转入医院 | |
| referral_reason | VARCHAR(500) | 转诊原因 | |
| impression | VARCHAR(500) | 初步诊断 | |
| referral_time | DATETIME | 转诊时间 | |
| process_time | DATETIME | 处理时间 | |
| CREATE_TIME | DATETIME | 创建时间 | |

## 使用示例

### 1. 创建转诊

```java
@Autowired
private ReferralApplicationService referralApplicationService;

// 构建创建DTO
ReferralCreateDTO createDTO = ReferralCreateDTO.builder()
    .basicInfoId("patient-001")
    .patientName("张三")
    .fromHospital("市人民医院")
    .toHospital("省人民医院")
    .referralReason("需要进一步检查")
    .impression("高血压")
    .build();

// 创建转诊
ReferralDTO result = referralApplicationService.createReferral(createDTO);
```

### 2. 查询转诊

```java
@Autowired
private ReferralQueryService referralQueryService;

// 根据ID查询
Optional<ReferralDTO> referral = referralQueryService.getReferralById("referral-001");

// 查询患者的转诊历史
List<ReferralDTO> referrals = referralQueryService.getReferralsByPatientId("patient-001");

// 查询待处理转诊
List<ReferralDTO> pendingReferrals = referralQueryService.getPendingReferrals();
```

### 3. 处理转诊

```java
// 接受转诊
ReferralDTO accepted = referralApplicationService.acceptReferral("referral-001", "同意接收");

// 拒绝转诊
ReferralDTO rejected = referralApplicationService.rejectReferral("referral-001", "患者病情不符合收治条件");

// 完成转诊
ReferralDTO completed = referralApplicationService.completeReferral("referral-001", "患者已成功转入");
```

## 扩展点

### 1. 自定义事件处理

```java
@Component
public class CustomReferralEventListener {
    
    @EventListener
    public void handleReferralCreated(ReferralCreatedEvent event) {
        // 自定义处理逻辑
    }
}
```

### 2. 自定义查询

```java
@Repository
public interface CustomReferralRepository extends ReferralFormJpaRepository {
    
    @Query("SELECT r FROM ReferralFormEntity r WHERE ...")
    List<ReferralFormEntity> findByCustomCondition(...);
}
```

### 3. 业务规则扩展

```java
@Service
public class CustomReferralDomainService extends ReferralDomainServiceImpl {
    
    @Override
    public ReferralFormEntity createReferral(ReferralFormEntity referral) {
        // 添加自定义业务规则
        validateCustomRules(referral);
        return super.createReferral(referral);
    }
}
```

## 注意事项

1. **数据一致性**: 转诊创建时会验证患者是否存在
2. **并发控制**: 使用乐观锁防止并发修改
3. **数据加密**: 敏感字段（身份证、电话）自动加密
4. **事件处理**: 异步事件处理不会影响主业务流程
5. **软删除**: 删除操作默认为软删除，可恢复

## 性能优化

1. **分页查询**: 使用数据库分页，避免内存分页
2. **索引优化**: 在常用查询字段上建立索引
3. **缓存策略**: 对热点数据进行缓存
4. **异步处理**: 非关键业务逻辑异步处理

## 监控和日志

1. **操作日志**: 记录所有转诊操作
2. **性能监控**: 监控接口响应时间
3. **业务监控**: 监控转诊成功率、处理时效等
4. **异常告警**: 异常情况及时告警
