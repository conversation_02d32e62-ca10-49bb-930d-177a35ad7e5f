# 基于注解的关联绑定方案

## 概述

为了解决模块间循环依赖问题，我们设计了一套基于注解的关联绑定方案。该方案通过注解标记关联关系，使用AOP自动处理关联数据加载，实现了模块间的松耦合。

## 🎯 核心优势

### ✅ 避免循环依赖
- 通过注解定义关联关系，避免直接依赖
- 使用依赖倒置原则，实现模块解耦
- 支持动态发现和注册关联数据提供者

### ✅ 自动化处理
- AOP自动拦截查询结果，处理关联关系
- 支持单个对象和批量对象的关联处理
- 延迟加载，按需获取关联数据

### ✅ 高性能
- 支持批量加载，减少数据库查询次数
- 内置缓存机制，提高查询效率
- 可配置的加载策略和优先级

## 📦 包结构规划

```
项目结构
├── hm-shared/
│   ├── hm-shared-annotations/           # 注解定义模块（最底层）
│   │   └── relation/
│   │       ├── EntityRelation.java     # 实体关联注解
│   │       ├── RelationLoader.java     # 关联加载器注解
│   │       └── RelationProvider.java   # 关联提供者注解
│   │
│   └── hm-shared-framework/             # 框架实现模块
│       └── relation/
│           ├── RelationManager.java     # 关联管理器
│           ├── RelationProcessorAspect.java # AOP切面
│           └── RelationAutoConfiguration.java # 自动配置
│
├── hm-domain/
│   ├── hm-domain-patient/               # 患者领域模块
│   └── hm-domain-referral/              # 转诊领域模块
│       └── entity/
│           └── ReferralFormEntity.java  # 使用@EntityRelation注解
│
├── hm-application/
│   ├── hm-application-patient/          # 患者应用模块
│   │   └── service/
│   │       └── PatientQueryServiceForReferralImpl.java # 使用@RelationProvider和@RelationLoader
│   │
│   └── hm-application-referral/         # 转诊应用模块
│       └── dto/
│           └── ReferralDTO.java         # 使用@EntityRelation注解
│
└── hm-interfaces/
    └── hm-interfaces-web/               # Web接口模块
```

## 🔧 依赖关系

```
依赖层次（从上到下）：
┌─────────────────────────────────────┐
│ hm-interfaces-web                   │
├─────────────────────────────────────┤
│ hm-application-*                    │
├─────────────────────────────────────┤
│ hm-domain-*                         │
├─────────────────────────────────────┤
│ hm-shared-framework                 │
├─────────────────────────────────────┤
│ hm-shared-annotations (最底层)       │
└─────────────────────────────────────┘

关键点：
- 所有模块都可以依赖 hm-shared-annotations
- 只有框架模块依赖注解模块来实现功能
- 业务模块之间不直接依赖，通过注解解耦
```

## 📝 使用方式

### 1. 定义关联关系

在实体类或DTO中使用`@EntityRelation`注解：

```java
@Entity
public class ReferralFormEntity extends BaseEntity<String> {
    
    @Column(name = "BASIC_INFO_ID")
    @EntityRelation(
        entityType = "patient",           // 关联实体类型
        relationField = "id",             // 关联字段
        type = RelationType.MANY_TO_ONE,  // 关联类型
        description = "关联患者基本信息"
    )
    private String basicInfoId;
    
    @Transient
    private Object patientInfo;  // 自动填充的关联数据
}
```

### 2. 创建关联数据提供者

在服务类上使用`@RelationProvider`注解：

```java
@Service
@RelationProvider(
    entityTypes = {"patient"},
    name = "患者信息提供者"
)
public class PatientQueryServiceImpl {
    
    @RelationLoader(
        entityTypes = {"patient"},
        priority = 0,
        supportBatch = true,
        cacheSeconds = 300
    )
    public Map<String, PatientInfo> loadPatientsByIds(List<String> patientIds) {
        // 批量加载患者信息
        return batchLoadPatients(patientIds);
    }
    
    @RelationLoader(
        entityTypes = {"patient"},
        priority = 1,
        supportBatch = false
    )
    public PatientInfo loadPatientById(String patientId) {
        // 单个加载患者信息
        return loadPatient(patientId);
    }
}
```

### 3. 自动关联处理

查询服务返回的结果会自动处理关联关系：

```java
@Service
public class ReferralQueryService {
    
    public ReferralDTO getReferralById(String id) {
        ReferralFormEntity entity = repository.findById(id);
        ReferralDTO dto = mapper.toDTO(entity);
        // AOP会自动处理dto中的关联关系
        // dto.getPatientInfo() 将自动填充患者信息
        return dto;
    }
    
    public List<ReferralDTO> getReferralsByStatus(Integer status) {
        List<ReferralFormEntity> entities = repository.findByStatus(status);
        List<ReferralDTO> dtos = mapper.toDTOList(entities);
        // AOP会自动批量处理所有dto的关联关系
        return dtos;
    }
}
```

## 🔄 工作流程

### 1. 初始化阶段
```
应用启动 → 扫描@RelationProvider → 注册提供者 → 扫描@RelationLoader → 注册加载器
```

### 2. 运行时处理
```
查询方法执行 → AOP拦截返回结果 → 扫描@EntityRelation注解 → 
查找对应加载器 → 批量/单个加载关联数据 → 设置到目标对象
```

## 🎨 注解详解

### @EntityRelation
标记字段的关联关系：
```java
@EntityRelation(
    entityType = "patient",              // 关联实体类型（必填）
    relationField = "id",                // 关联字段名（默认"id"）
    type = RelationType.MANY_TO_ONE,     // 关联类型
    lazy = true,                         // 是否延迟加载（默认true）
    cachePrefix = "patient:",            // 缓存键前缀
    description = "关联患者信息"          // 描述信息
)
```

### @RelationProvider
标记关联数据提供者：
```java
@RelationProvider(
    entityTypes = {"patient", "doctor"}, // 提供的实体类型（必填）
    name = "患者信息提供者",              // 提供者名称
    description = "为其他模块提供患者信息" // 描述信息
)
```

### @RelationLoader
标记关联数据加载器方法：
```java
@RelationLoader(
    entityTypes = {"patient"},           // 支持的实体类型（必填）
    priority = 0,                        // 优先级（数字越小优先级越高）
    supportBatch = true,                 // 是否支持批量加载
    cacheSeconds = 300                   // 缓存时间（秒）
)
```

## 🚀 高级特性

### 1. 批量加载优化
```java
// 支持批量加载的方法签名
public Map<String, Object> loadBatch(List<String> ids);

// 单个加载的方法签名  
public Object loadSingle(String id);
```

### 2. 缓存支持
```java
@RelationLoader(
    entityTypes = {"patient"},
    cacheSeconds = 600  // 缓存10分钟
)
public PatientInfo loadPatient(String id) {
    // 结果会自动缓存
}
```

### 3. 多提供者支持
```java
// 可以有多个提供者提供同一类型的数据
// 按priority排序，优先使用priority值小的

@RelationLoader(entityTypes = {"patient"}, priority = 0)  // 优先使用
public PatientInfo loadFromCache(String id) { ... }

@RelationLoader(entityTypes = {"patient"}, priority = 1)  // 备用
public PatientInfo loadFromDatabase(String id) { ... }
```

### 4. 条件加载
```java
@RelationLoader(
    entityTypes = {"patient"},
    priority = 0
)
@ConditionalOnProperty("feature.patient.cache.enabled")
public PatientInfo loadFromCache(String id) {
    // 只有在配置启用时才会注册此加载器
}
```

## ⚙️ 配置选项

在`application.yml`中配置：

```yaml
hm:
  relation:
    enabled: true                    # 是否启用关联处理（默认true）
    cache:
      enabled: true                  # 是否启用缓存（默认true）
      default-expire-seconds: 300    # 默认缓存时间
    batch:
      enabled: true                  # 是否启用批量加载（默认true）
      max-batch-size: 100           # 最大批量大小
    async:
      enabled: false                 # 是否异步处理（默认false）
      thread-pool-size: 10          # 异步线程池大小
```

## 🔍 监控和调试

### 1. 日志配置
```yaml
logging:
  level:
    com.hys.hm.shared.framework.relation: DEBUG
```

### 2. 监控指标
- 关联加载次数
- 批量加载效率
- 缓存命中率
- 处理耗时统计

### 3. 调试工具
```java
@Autowired
private RelationManager relationManager;

// 手动处理关联关系（用于调试）
relationManager.processRelations(dto);

// 查看注册的提供者和加载器
relationManager.getProviderRegistry();
relationManager.getLoaderRegistry();
```

## 🎯 最佳实践

### 1. 命名规范
- entityType使用小写，如："patient", "doctor", "referral"
- 关联字段使用驼峰命名：patientInfo, doctorList
- 提供者名称要有意义：PatientInfoProvider

### 2. 性能优化
- 优先使用批量加载器
- 合理设置缓存时间
- 避免深层嵌套关联

### 3. 错误处理
- 加载器方法要处理异常
- 返回null表示未找到数据
- 记录详细的错误日志

### 4. 测试策略
- 单元测试要mock关联数据
- 集成测试验证关联关系
- 性能测试关注批量加载效率

## 🔧 扩展点

### 1. 自定义加载策略
```java
public interface RelationLoadStrategy {
    Object load(String entityType, Object relationId);
    Map<Object, Object> batchLoad(String entityType, List<Object> relationIds);
}
```

### 2. 自定义缓存实现
```java
public interface RelationCache {
    Object get(String key);
    void put(String key, Object value, int expireSeconds);
    void evict(String key);
}
```

### 3. 关联处理监听器
```java
public interface RelationProcessListener {
    void beforeProcess(Object target);
    void afterProcess(Object target);
    void onError(Object target, Exception e);
}
```

这个基于注解的关联绑定方案完美解决了循环依赖问题，同时提供了强大的关联数据处理能力。通过合理的包结构规划和依赖管理，确保了系统的可维护性和扩展性。
