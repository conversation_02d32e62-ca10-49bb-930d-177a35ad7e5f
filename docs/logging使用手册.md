# HM-Shared-Logging 模块使用手册

## 1. 模块概述

`hm-shared-logging` 是健康管理系统的共享日志模块，提供了统一的日志记录功能。该模块基于Spring AOP实现，通过注解的方式为业务方法提供自动化的日志记录能力。

### 1.1 主要功能
- 🔍 **自动日志记录**: 通过`@Log`注解自动记录方法执行日志
- 🚀 **异步处理**: 支持同步和异步两种日志保存方式
- 📊 **丰富的日志信息**: 记录请求参数、响应结果、执行时间、用户信息等
- 🏷️ **多维度分类**: 支持业务类型、操作类型、平台类型等多维度分类
- 🔧 **灵活配置**: 可配置是否保存请求/响应数据、日志级别等
- 📈 **性能监控**: 自动记录方法执行时间，便于性能分析

### 1.2 技术栈
- Spring Boot 3.x
- Spring AOP
- Jackson (JSON序列化)
- Lombok
- Apache Commons Lang3

## 2. 快速开始

### 2.1 添加依赖

在项目的`pom.xml`中添加依赖：

```xml
<dependency>
    <groupId>com.hys</groupId>
    <artifactId>hm-shared-logging</artifactId>
    <version>3.0.1-SNAPSHOT</version>
</dependency>
```

### 2.2 实现LogService接口

模块提供了`LogService`接口，需要在具体的业务模块中实现该接口：

```java
@Service
public class LogServiceImpl implements LogService {
    
    @Override
    public boolean saveLog(LogRecord logRecord) {
        // 实现同步保存日志逻辑
        return true;
    }
    
    @Override
    public CompletableFuture<Boolean> saveLogAsync(LogRecord logRecord) {
        // 实现异步保存日志逻辑
        return CompletableFuture.completedFuture(true);
    }
    
    // 实现其他接口方法...
}
```

### 2.3 基本使用

在需要记录日志的方法上添加`@Log`注解：

```java
@RestController
public class UserController {
    
    @Log(title = "用户查询", businessType = BusinessType.SELECT)
    @GetMapping("/users/{id}")
    public Result<User> getUser(@PathVariable Long id) {
        // 业务逻辑
        return Result.success(user);
    }
    
    @Log(title = "用户创建", businessType = BusinessType.INSERT)
    @PostMapping("/users")
    public Result<User> createUser(@RequestBody User user) {
        // 业务逻辑
        return Result.success(user);
    }
}
```

## 3. 核心组件详解

### 3.1 @Log注解

`@Log`注解是模块的核心，提供了丰富的配置选项：

```java
@Log(
    title = "模块标题",                    // 日志标题
    businessType = BusinessType.INSERT,   // 业务操作类型
    operatorType = OperatorType.MANAGE,   // 操作人类别
    platformType = PlatformType.HM,       // 平台类型
    isSaveRequestData = true,             // 是否保存请求参数
    isSaveResponseData = true,            // 是否保存响应结果
    projectRecordLog = false,             // 是否为项目记录日志
    async = true,                         // 是否异步保存
    level = Log.LogLevel.INFO             // 日志级别
)
```

#### 注解属性说明

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| title | String | "" | 模块标题，用于标识日志所属的功能模块 |
| businessType | BusinessType | OTHER | 业务操作类型，如增删改查等 |
| operatorType | OperatorType | MANAGE | 操作人类别，如管理员、移动端用户等 |
| platformType | PlatformType | HM | 平台类型，如健康管理平台、管理后台等 |
| isSaveRequestData | boolean | true | 是否保存请求参数 |
| isSaveResponseData | boolean | true | 是否保存响应结果 |
| projectRecordLog | boolean | false | 是否为项目记录日志 |
| async | boolean | true | 是否异步保存日志 |
| level | LogLevel | INFO | 日志级别：DEBUG、INFO、WARN、ERROR |

### 3.2 枚举类型

#### BusinessType (业务操作类型)
```java
public enum BusinessType {
    INSERT,      // 新增
    UPDATE,      // 修改
    SAVE_UPDATE, // 保存或修改
    DELETE,      // 删除
    GRANT,       // 授权
    EXPORT,      // 导出
    IMPORT,      // 导入
    FORCE,       // 强退
    GENCODE,     // 生成代码
    CLEAN,       // 清空数据
    SELECT,      // 查询
    LOGIN,       // 登录
    LOGOUT,      // 登出
    OTHER        // 其它
}
```

#### OperatorType (操作人类别)
```java
public enum OperatorType {
    MANAGE,  // 后台管理用户
    MOBILE,  // 手机端用户
    APP,     // APP用户
    OTHER    // 其他
}
```

#### PlatformType (平台类型)
```java
public enum PlatformType {
    HM,      // 健康管理平台
    ADMIN,   // 管理后台
    MOBILE,  // 移动端
    API,     // API接口
    OTHER    // 其他平台
}
```

#### BusinessStatus (操作状态)
```java
public enum BusinessStatus {
    SUCCESS, // 成功
    FAIL     // 失败
}
```

### 3.3 LogRecord数据模型

`LogRecord`是日志记录的数据载体，包含了完整的日志信息：

```java
@Data
@Builder
public class LogRecord {
    private Long id;                    // 日志ID
    private String title;               // 模块标题
    private String requestMethod;       // 请求方式
    private String methodName;          // 方法名称
    private String requestUrl;          // 请求URL
    private Boolean projectRecordLog;   // 是否项目日志
    private String requestIp;           // 主机地址
    private OperatorType operatorType;  // 操作类别
    private BusinessType businessType;  // 业务类型
    private PlatformType platformType;  // 平台类型
    private String userName;            // 操作人员登录名
    private String realName;            // 用户真实姓名
    private String location;            // 操作地点
    private BusinessStatus status;      // 操作状态
    private String requestParam;        // 请求参数
    private String responseResult;      // 返回参数
    private String errorMessage;        // 错误消息
    private LocalDateTime operateTime;  // 操作时间
    private Long executionTime;         // 执行耗时（毫秒）
    private String userAgent;           // 用户代理
    private String referer;             // 请求来源
    private String sessionId;           // 会话ID
    private String traceId;             // 追踪ID
}
```

## 4. 高级特性

### 4.1 自动配置

模块通过`LogAutoConfiguration`提供自动配置功能：

```java
@Configuration
public class LogAutoConfiguration {
    
    @Bean
    @ConditionalOnBean(LogService.class)
    @ConditionalOnMissingBean(LogAspect.class)
    public LogAspect logAspect(LogService logService) {
        return new LogAspect(logService);
    }
}
```

只有当存在`LogService`实现时，才会自动注册`LogAspect`切面。

### 4.2 异步处理

模块支持异步日志处理，可以通过`async`属性控制：

```java
@Log(title = "用户创建", async = true)  // 异步保存
@Log(title = "重要操作", async = false) // 同步保存
```

### 4.3 TraceId追踪

每个请求都会生成唯一的TraceId，便于日志追踪和问题排查：

```
【请求完成】TraceId: a1b2c3d4e5f6, 用户: admin, IP: *************,
接口: POST /users, 方法: UserController.createUser, 耗时: 150ms
```

## 5. 使用示例

### 5.1 基础示例

```java
@RestController
@RequestMapping("/api/users")
public class UserController {

    @Autowired
    private UserService userService;

    // 查询用户列表
    @Log(title = "用户管理-查询列表", businessType = BusinessType.SELECT)
    @GetMapping
    public Result<List<User>> getUserList(@RequestParam(required = false) String keyword) {
        List<User> users = userService.getUserList(keyword);
        return Result.success(users);
    }

    // 创建用户
    @Log(title = "用户管理-新增用户", businessType = BusinessType.INSERT)
    @PostMapping
    public Result<User> createUser(@RequestBody @Valid User user) {
        User createdUser = userService.createUser(user);
        return Result.success(createdUser);
    }

    // 更新用户
    @Log(title = "用户管理-更新用户", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}")
    public Result<User> updateUser(@PathVariable Long id, @RequestBody @Valid User user) {
        User updatedUser = userService.updateUser(id, user);
        return Result.success(updatedUser);
    }

    // 删除用户
    @Log(title = "用户管理-删除用户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public Result<Void> deleteUser(@PathVariable Long id) {
        userService.deleteUser(id);
        return Result.success();
    }
}
```

### 5.2 移动端API示例

```java
@RestController
@RequestMapping("/mobile/api")
public class MobileController {

    // 移动端用户登录
    @Log(
        title = "移动端-用户登录",
        businessType = BusinessType.LOGIN,
        operatorType = OperatorType.MOBILE,
        platformType = PlatformType.MOBILE
    )
    @PostMapping("/login")
    public Result<LoginResponse> login(@RequestBody LoginRequest request) {
        LoginResponse response = authService.login(request);
        return Result.success(response);
    }

    // 不保存敏感数据的示例
    @Log(
        title = "移动端-修改密码",
        businessType = BusinessType.UPDATE,
        operatorType = OperatorType.MOBILE,
        isSaveRequestData = false,  // 不保存请求参数（密码敏感）
        isSaveResponseData = false  // 不保存响应数据
    )
    @PostMapping("/change-password")
    public Result<Void> changePassword(@RequestBody ChangePasswordRequest request) {
        authService.changePassword(request);
        return Result.success();
    }
}
```

### 5.3 管理后台示例

```java
@RestController
@RequestMapping("/admin/api")
public class AdminController {

    // 管理员操作
    @Log(
        title = "系统管理-用户授权",
        businessType = BusinessType.GRANT,
        operatorType = OperatorType.MANAGE,
        platformType = PlatformType.ADMIN
    )
    @PostMapping("/users/{id}/grant")
    public Result<Void> grantPermissions(@PathVariable Long id, @RequestBody GrantRequest request) {
        adminService.grantPermissions(id, request);
        return Result.success();
    }

    // 数据导出（同步处理）
    @Log(
        title = "系统管理-导出用户数据",
        businessType = BusinessType.EXPORT,
        async = false  // 同步处理，确保导出完成后再返回
    )
    @GetMapping("/users/export")
    public ResponseEntity<byte[]> exportUsers(@RequestParam String format) {
        byte[] data = exportService.exportUsers(format);
        return ResponseEntity.ok()
            .header("Content-Disposition", "attachment; filename=users." + format)
            .body(data);
    }
}
```

### 5.4 服务层使用示例

```java
@Service
public class UserService {

    // 服务层方法也可以使用日志注解
    @Log(title = "用户服务-批量导入", businessType = BusinessType.IMPORT)
    public ImportResult importUsers(MultipartFile file) {
        // 批量导入逻辑
        return importResult;
    }

    // 清理过期数据
    @Log(title = "用户服务-清理过期数据", businessType = BusinessType.CLEAN)
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void cleanExpiredData() {
        // 清理逻辑
    }
}
```

## 6. LogService接口实现

### 6.1 完整实现示例

```java
@Service
@Slf4j
public class LogServiceImpl implements LogService {

    @Autowired
    private LogMapper logMapper;

    @Async("logTaskExecutor")
    @Override
    public CompletableFuture<Boolean> saveLogAsync(LogRecord logRecord) {
        try {
            boolean result = saveLog(logRecord);
            return CompletableFuture.completedFuture(result);
        } catch (Exception e) {
            log.error("异步保存日志失败", e);
            return CompletableFuture.completedFuture(false);
        }
    }

    @Override
    public boolean saveLog(LogRecord logRecord) {
        try {
            // 转换为数据库实体
            SysOperLog operLog = convertToEntity(logRecord);

            // 保存到数据库
            int result = logMapper.insertOperLog(operLog);
            return result > 0;
        } catch (Exception e) {
            log.error("保存日志失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public int batchSaveLog(List<LogRecord> logRecords) {
        if (CollectionUtils.isEmpty(logRecords)) {
            return 0;
        }

        try {
            List<SysOperLog> operLogs = logRecords.stream()
                .map(this::convertToEntity)
                .collect(Collectors.toList());

            return logMapper.batchInsertOperLog(operLogs);
        } catch (Exception e) {
            log.error("批量保存日志失败", e);
            return 0;
        }
    }

    @Async("logTaskExecutor")
    @Override
    public CompletableFuture<Integer> batchSaveLogAsync(List<LogRecord> logRecords) {
        try {
            int result = batchSaveLog(logRecords);
            return CompletableFuture.completedFuture(result);
        } catch (Exception e) {
            log.error("异步批量保存日志失败", e);
            return CompletableFuture.completedFuture(0);
        }
    }

    @Override
    public LogRecord getLogById(Long id) {
        try {
            SysOperLog operLog = logMapper.selectOperLogById(id);
            return convertToLogRecord(operLog);
        } catch (Exception e) {
            log.error("查询日志失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<LogRecord> queryLogs(LogQueryCondition condition) {
        try {
            // 根据条件查询
            List<SysOperLog> operLogs = logMapper.selectOperLogList(condition);
            return operLogs.stream()
                .map(this::convertToLogRecord)
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询日志列表失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public int deleteExpiredLogs(int days) {
        try {
            LocalDateTime expireTime = LocalDateTime.now().minusDays(days);
            return logMapper.deleteExpiredLogs(expireTime);
        } catch (Exception e) {
            log.error("删除过期日志失败", e);
            return 0;
        }
    }

    // 转换方法
    private SysOperLog convertToEntity(LogRecord logRecord) {
        // 实现转换逻辑
    }

    private LogRecord convertToLogRecord(SysOperLog operLog) {
        // 实现转换逻辑
    }
}
```

## 7. 配置说明

### 7.1 异步配置

如果使用异步日志保存，建议配置专用的线程池：

```java
@Configuration
@EnableAsync
public class LoggingConfig {

    @Bean("logTaskExecutor")
    public Executor logTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(5);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("log-async-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        return executor;
    }
}
```

### 7.2 应用配置

在`application.yml`中可以配置相关参数：

```yaml
logging:
  level:
    com.hys.hm.shared.logging: INFO

# 如果需要自定义配置
hm:
  logging:
    async: true
    max-param-length: 2000
    max-result-length: 2000
```

## 8. 最佳实践

### 8.1 注解使用建议

1. **合理设置title**: 使用有意义的标题，便于日志查询和分析
2. **选择合适的businessType**: 根据实际业务操作选择对应的类型
3. **敏感数据处理**: 对于包含敏感信息的接口，设置`isSaveRequestData=false`
4. **性能考虑**: 对于高频接口，考虑使用异步保存
5. **响应数据**: 大数据量响应可以设置`isSaveResponseData=false`

### 8.2 性能优化

1. **异步处理**: 默认使用异步保存，避免影响业务性能
2. **批量保存**: 对于大量日志，使用批量保存接口
3. **数据清理**: 定期清理过期日志数据
4. **索引优化**: 在数据库中为常用查询字段建立索引

### 8.3 监控和告警

1. **日志保存失败监控**: 监控日志保存失败率
2. **性能监控**: 监控日志保存耗时
3. **存储监控**: 监控日志存储空间使用情况

## 9. 故障排查

### 9.1 常见问题

**问题1**: 日志没有记录
- 检查是否实现了`LogService`接口
- 确认`@Log`注解是否正确添加
- 查看控制台是否有错误日志

**问题2**: 异步日志保存失败
- 检查线程池配置
- 查看异步任务执行情况
- 确认数据库连接是否正常

**问题3**: 性能影响
- 考虑使用异步保存
- 减少保存的数据量
- 优化数据库写入性能

### 9.2 调试技巧

1. **开启DEBUG日志**: 设置日志级别为DEBUG查看详细信息
2. **TraceId追踪**: 使用TraceId追踪请求链路
3. **性能分析**: 关注executionTime字段分析性能

## 10. 总结

`hm-shared-logging`模块提供了完整的日志记录解决方案，具有以下优势：

- ✅ **简单易用**: 通过注解即可实现日志记录
- ✅ **功能丰富**: 支持多维度分类和详细信息记录
- ✅ **性能友好**: 支持异步处理，不影响业务性能
- ✅ **扩展性强**: 接口设计灵活，易于扩展
- ✅ **自动配置**: Spring Boot自动配置，开箱即用

通过合理使用该模块，可以大大提升系统的可观测性和问题排查能力。
