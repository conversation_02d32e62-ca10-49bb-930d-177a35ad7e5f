# 健康管理系统架构重构规划

## 概述

本文档规划了健康管理系统的架构重构方案，目标是构建一个**微服务就绪的单体架构**（Microservice-Ready Monolith）。当前保持单体部署，但通过合理的模块划分和依赖管理，为未来的微服务拆分做好准备。

## 当前架构问题分析

### 主要问题

1. **业务强依赖**：患者服务查询转诊信息，转诊表单关联患者档案
2. **Infrastructure层职责过重**：所有业务的持久化实现集中在一个模块
3. **跨域数据访问**：直接跨业务域访问数据
4. **共享数据库**：所有业务共享同一个数据库实例

### 依赖关系图

```mermaid
graph LR
    A[患者服务] --> B[转诊服务]
    B --> A
    C[健康服务] --> A
    D[随访服务] --> A
    D --> B
    E[知识库服务] --> A
```

## 目标架构设计

### 架构原则

1. **模块自治**：每个业务模块边界清晰，低耦合高内聚
2. **接口隔离**：模块间通过明确定义的接口通信
3. **数据隔离**：每个模块管理自己的数据，避免直接跨模块数据访问
4. **依赖倒置**：通过接口和事件解耦模块间依赖

### 业务模块划分

#### 核心业务模块

1. **患者管理模块** (Patient Module)
2. **转诊管理模块** (Referral Module)
3. **健康管理模块** (Health Module)
4. **随访管理模块** (Followup Module)
5. **知识库模块** (Knowledge Module)
6. **沟通模块** (Communication Module)

#### 共享模块

1. **用户认证模块** (Auth Module)
2. **通知模块** (Notification Module)
3. **文件管理模块** (File Module)
4. **系统日志模块** (Logging Module)

#### 技术框架

1. **共享框架** (Shared Framework)
2. **事件总线** (Event Bus)
3. **数据访问层** (Data Access Layer)

## 解决业务强依赖的方案

### 1. 模块间接口设计

#### 定义清晰的模块接口

```java
// 患者模块对外提供的接口
public interface PatientQueryService {
    PatientBasicInfo getPatientBasicInfo(String patientId);
    List<PatientBasicInfo> batchGetPatientBasicInfo(List<String> patientIds);
    boolean existsPatient(String patientId);
}

// 转诊模块对外提供的接口
public interface ReferralQueryService {
    List<ReferralSummary> getPatientReferrals(String patientId);
    ReferralDetail getReferralDetail(String referralId);
    ReferralStatistics getHospitalStatistics(String unitId);
}
```

#### 模块间数据传输对象

```java
// 患者基础信息DTO（跨模块使用）
@Data
@Builder
public class PatientBasicInfo {
    private String patientId;
    private String name;
    private Integer gender;
    private Integer age;
    private String phone;
    private String address;
    // 只包含其他模块需要的基础信息
}

// 转诊摘要信息DTO
@Data
@Builder
public class ReferralSummary {
    private String referralId;
    private String referralNo;
    private String outHospitalName;
    private String inHospitalName;
    private Integer status;
    private LocalDateTime referralDate;
}
```

### 2. 应用服务聚合模式

#### 聚合应用服务

```java
@Service
@Transactional(readOnly = true)
public class PatientOverviewApplicationService {

    @Autowired
    private PatientQueryService patientQueryService;

    @Autowired
    private ReferralQueryService referralQueryService;

    @Autowired
    private HealthQueryService healthQueryService;

    /**
     * 获取患者概览信息（聚合多个模块的数据）
     */
    public PatientOverviewVO getPatientOverview(String patientId) {
        // 获取患者基础信息
        PatientBasicInfo basicInfo = patientQueryService.getPatientBasicInfo(patientId);

        // 获取转诊记录
        List<ReferralSummary> referrals = referralQueryService.getPatientReferrals(patientId);

        // 获取健康摘要
        HealthSummary healthSummary = healthQueryService.getPatientHealthSummary(patientId);

        // 聚合返回
        return PatientOverviewVO.builder()
            .basicInfo(basicInfo)
            .referralHistory(referrals)
            .healthSummary(healthSummary)
            .build();
    }
}
```

### 3. 内部事件机制

#### 模块内事件总线

```java
// 内部事件发布器
@Component
public class InternalEventPublisher {

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    public void publishPatientCreated(Patient patient) {
        PatientCreatedEvent event = PatientCreatedEvent.builder()
            .patientId(patient.getId())
            .basicInfo(patient.getBasicInfo())
            .timestamp(LocalDateTime.now())
            .build();

        // 使用Spring的事件机制
        eventPublisher.publishEvent(event);
    }
}

// 事件监听器
@Component
public class PatientEventListener {

    @Autowired
    private ReferralPatientInfoService referralPatientInfoService;

    @EventListener
    @Async
    public void handlePatientCreated(PatientCreatedEvent event) {
        // 异步更新转诊模块中的患者信息
        referralPatientInfoService.syncPatientBasicInfo(event.getBasicInfo());
    }

    @EventListener
    @Async
    public void handlePatientUpdated(PatientUpdatedEvent event) {
        // 异步更新相关模块的患者信息
        referralPatientInfoService.updatePatientBasicInfo(
            event.getPatientId(),
            event.getNewBasicInfo()
        );
    }
}
```

## 新架构模块设计

### 单体模块结构（微服务就绪）

```
hm-health-management/               # 单体应用根目录
├── hm-bootstrap/                   # 启动模块
│
├── hm-interfaces/                  # 接口层
│   ├── hm-interfaces-web/          # Web控制器
│   └── hm-interfaces-api/          # 对外API定义
│
├── hm-application/                 # 应用服务层
│   ├── hm-application-patient/     # 患者应用服务
│   ├── hm-application-referral/    # 转诊应用服务
│   ├── hm-application-health/      # 健康应用服务
│   ├── hm-application-followup/    # 随访应用服务
│   ├── hm-application-knowledge/   # 知识库应用服务
│   └── hm-application-shared/      # 共享应用服务（聚合服务）
│
├── hm-domain/                      # 领域层
│   ├── hm-domain-patient/          # 患者领域
│   ├── hm-domain-referral/         # 转诊领域
│   ├── hm-domain-health/           # 健康领域
│   ├── hm-domain-followup/         # 随访领域
│   ├── hm-domain-knowledge/        # 知识库领域
│   └── hm-domain-shared/           # 共享领域（事件定义）
│
├── hm-infrastructure/              # 基础设施层
│   ├── hm-infrastructure-persistence-patient/    # 患者持久化
│   ├── hm-infrastructure-persistence-referral/   # 转诊持久化
│   ├── hm-infrastructure-persistence-health/     # 健康持久化
│   ├── hm-infrastructure-persistence-followup/   # 随访持久化
│   ├── hm-infrastructure-persistence-knowledge/  # 知识库持久化
│   ├── hm-infrastructure-external/               # 外部系统集成
│   ├── hm-infrastructure-ai/                     # AI服务集成
│   └── hm-infrastructure-logging/                # 日志服务
│
└── hm-shared/                      # 共享内核
    ├── hm-shared-framework/        # 技术框架
    ├── hm-shared-common/           # 通用工具
    ├── hm-shared-types/            # 通用类型
    └── hm-shared-events/           # 事件定义
```

### 数据库设计策略

#### 逻辑分库（物理上仍是单库）

```yaml
# 当前：单一数据库，但按模块组织表结构
health_management_db:
  # 患者模块表
  patient_tables:
    - patient_basic_info          # 患者基础信息
    - patient_health_record       # 患者健康档案
    - patient_tags               # 患者标签

  # 转诊模块表
  referral_tables:
    - referral_form              # 转诊表单
    - referral_history           # 转诊历史
    - referral_patient_info      # 转诊模块的患者信息副本

  # 健康模块表
  health_tables:
    - health_assessment          # 健康评估
    - bmi_record                # BMI记录
    - bmr_record                # BMR记录
    - health_patient_info       # 健康模块的患者信息副本

  # 共享表
  shared_tables:
    - system_user               # 系统用户
    - system_config             # 系统配置
    - operation_log             # 操作日志
```

#### 数据冗余策略

```java
// 患者基础信息在各模块的冗余表
@Entity
@Table(name = "referral_patient_info")
public class ReferralPatientInfo {
    private String patientId;      // 关联患者主表
    private String name;           // 冗余：患者姓名
    private Integer gender;        // 冗余：性别
    private Integer age;           // 冗余：年龄
    private String phone;          // 冗余：电话
    // 只保存转诊模块需要的字段
}

// 数据同步服务
@Service
public class PatientInfoSyncService {

    @EventListener
    @Transactional
    public void syncToReferralModule(PatientUpdatedEvent event) {
        // 同步到转诊模块的患者信息表
        referralPatientInfoRepository.updateByPatientId(
            event.getPatientId(),
            event.getNewBasicInfo()
        );
    }
}
```

## 重构实施计划

### 第一阶段：模块重组 (3-4周)

#### 1.1 重新组织模块结构
- [ ] 按业务域重新划分模块
- [ ] 创建清晰的模块边界
- [ ] 建立模块间接口定义
- [ ] 重构共享框架模块

#### 1.2 建立内部事件机制
- [ ] 创建 `hm-shared-events` 模块
- [ ] 实现基于Spring Events的内部事件总线
- [ ] 定义标准的事件接口和处理器
- [ ] 建立事件的异步处理机制

### 第二阶段：模块解耦 (4-5周)

#### 2.1 重构患者模块
- [ ] 重新设计患者模块的对外接口
- [ ] 实现患者查询服务接口
- [ ] 建立患者事件发布机制
- [ ] 重构患者相关的应用服务

#### 2.2 重构转诊模块
- [ ] 创建转诊模块的患者信息冗余表
- [ ] 实现患者信息同步服务
- [ ] 重构转诊查询服务接口
- [ ] 建立转诊事件发布机制

#### 2.3 建立模块间通信
- [ ] 定义标准的模块间接口
- [ ] 实现接口的依赖注入配置
- [ ] 建立数据传输对象(DTO)规范
- [ ] 实现模块间的异步通信

### 第三阶段：数据同步机制 (3-4周)

#### 3.1 实现数据冗余和同步
```java
// 患者信息同步服务
@Service
public class PatientInfoSyncService {

    @EventListener
    @Async
    @Transactional
    public void handlePatientUpdated(PatientUpdatedEvent event) {
        // 同步到转诊模块
        syncToReferralModule(event);

        // 同步到健康模块
        syncToHealthModule(event);

        // 同步到随访模块
        syncToFollowupModule(event);
    }

    private void syncToReferralModule(PatientUpdatedEvent event) {
        referralPatientInfoService.updatePatientInfo(
            event.getPatientId(),
            event.getNewBasicInfo()
        );
    }
}
```

#### 3.2 建立数据一致性保障
- [ ] 实现数据同步的补偿机制
- [ ] 建立数据一致性校验
- [ ] 实现同步失败的重试机制
- [ ] 建立数据修复工具

### 第四阶段：完善和优化 (2-3周)

#### 4.1 性能优化
- [ ] 实现模块级缓存机制
- [ ] 优化数据库查询性能
- [ ] 实现异步处理优化
- [ ] 建立性能监控指标

#### 4.2 测试和验证
- [ ] 编写模块间集成测试
- [ ] 验证数据一致性
- [ ] 性能测试和调优
- [ ] 建立回归测试套件

## 技术选型

### 核心框架
- **Spring Boot 3.2.x** (应用框架)
- **Spring Data JPA** (数据访问)
- **Spring Events** (内部事件机制)
- **Spring Cache** (缓存抽象)

### 数据存储
- **MySQL 8.0** (主数据库)
- **Redis** (缓存和会话)
- **H2** (测试数据库)

### 开发工具
- **Maven** (构建工具)
- **Lombok** (代码生成)
- **MapStruct** (对象映射)
- **Swagger** (API文档)

### 测试框架
- **JUnit 5** (单元测试)
- **Testcontainers** (集成测试)
- **Mockito** (Mock框架)

## 风险评估与应对

### 主要风险

1. **模块间数据一致性风险**
   - 应对：实现事件驱动的数据同步机制和补偿策略

2. **重构过程中的业务中断**
   - 应对：采用渐进式重构，保持业务连续性

3. **开发复杂度增加**
   - 应对：建立清晰的模块接口规范和开发指南

4. **性能影响**
   - 应对：通过缓存和异步处理优化性能

### 重构策略

1. **渐进式重构**：逐个模块进行重构，避免大爆炸式改动
2. **向后兼容**：保持现有API的兼容性
3. **充分测试**：每个阶段都进行充分的测试验证
4. **监控告警**：建立完善的监控体系

## 未来微服务拆分准备

### 拆分就绪的特征

重构完成后，系统将具备以下特征，便于未来拆分为微服务：

1. **清晰的模块边界**：每个业务模块职责明确，依赖关系清晰
2. **标准化的接口**：模块间通过定义良好的接口通信
3. **数据隔离**：每个模块管理自己的数据，减少跨模块数据访问
4. **事件驱动**：通过事件机制实现模块间的松耦合

### 微服务拆分路径

当需要拆分为微服务时，可以按以下步骤进行：

1. **选择拆分模块**：优先拆分独立性强、变更频繁的模块
2. **独立部署**：将选定模块独立部署为微服务
3. **服务发现**：引入服务注册与发现机制
4. **API网关**：统一对外服务入口
5. **分布式事务**：将内部事件机制升级为分布式事件总线

## 总结

本重构方案通过以下方式解决业务强依赖问题：

1. **模块化设计**：清晰的业务模块边界和职责划分
2. **接口隔离**：通过标准接口实现模块间通信
3. **数据冗余**：通过事件驱动的数据同步解决跨模块数据访问
4. **异步解耦**：通过内部事件机制实现模块间的松耦合

重构后的架构将具备：
- **模块自治**：每个模块可以独立开发和测试
- **低耦合**：模块间依赖关系清晰且最小化
- **高内聚**：模块内部功能紧密相关
- **易扩展**：为未来的微服务拆分做好准备

这个重构方案既解决了当前的架构问题，又为未来的微服务演进奠定了坚实的基础。
