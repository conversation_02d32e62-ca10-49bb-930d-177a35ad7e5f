# 微服务实现技术指南

## 事件驱动架构实现

### 1. 事件定义规范

#### 基础事件接口

```java
package com.hys.hm.shared.events;

import java.time.LocalDateTime;

/**
 * 领域事件基础接口
 */
public interface DomainEvent {
    String getEventId();
    String getAggregateId();
    LocalDateTime getTimestamp();
    String getEventType();
    Integer getVersion();
}

/**
 * 抽象领域事件
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public abstract class AbstractDomainEvent implements DomainEvent {
    private String eventId = UUID.randomUUID().toString();
    private String aggregateId;
    private LocalDateTime timestamp = LocalDateTime.now();
    private Integer version = 1;
    
    @Override
    public String getEventType() {
        return this.getClass().getSimpleName();
    }
}
```

#### 患者相关事件

```java
/**
 * 患者创建事件
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PatientCreatedEvent extends AbstractDomainEvent {
    private String patientId;
    private PatientBasicInfo basicInfo;
    private String createdBy;
    
    public PatientCreatedEvent(String patientId, PatientBasicInfo basicInfo, String createdBy) {
        super();
        this.setAggregateId(patientId);
        this.patientId = patientId;
        this.basicInfo = basicInfo;
        this.createdBy = createdBy;
    }
}

/**
 * 患者信息更新事件
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PatientUpdatedEvent extends AbstractDomainEvent {
    private String patientId;
    private PatientBasicInfo oldInfo;
    private PatientBasicInfo newInfo;
    private String updatedBy;
    private List<String> changedFields;
}

/**
 * 患者基础信息VO
 */
@Data
@Builder
public class PatientBasicInfo {
    private String patientId;
    private String name;
    private Integer gender;
    private Integer age;
    private String phone;
    private String idCard;
    private String address;
    private LocalDateTime birthDate;
}
```

#### 转诊相关事件

```java
/**
 * 转诊创建事件
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReferralCreatedEvent extends AbstractDomainEvent {
    private String referralId;
    private String patientId;
    private String referralNo;
    private HospitalInfo outHospital;
    private HospitalInfo inHospital;
    private String createdBy;
}

/**
 * 转诊状态变更事件
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReferralStatusChangedEvent extends AbstractDomainEvent {
    private String referralId;
    private String patientId;
    private ReferralStatus oldStatus;
    private ReferralStatus newStatus;
    private String reason;
    private String operatedBy;
}
```

### 2. 事件发布机制

#### 事件发布器

```java
@Component
@Slf4j
public class DomainEventPublisher {
    
    @Autowired
    private RabbitTemplate rabbitTemplate;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 发布事件
     */
    public void publish(DomainEvent event) {
        try {
            // 1. 本地事件存储（确保可靠性）
            storeEventLocally(event);
            
            // 2. 发布到消息队列
            String routingKey = generateRoutingKey(event);
            rabbitTemplate.convertAndSend(
                EventConstants.DOMAIN_EVENT_EXCHANGE,
                routingKey,
                event
            );
            
            // 3. 标记事件已发布
            markEventPublished(event.getEventId());
            
            log.info("事件发布成功: eventId={}, type={}", 
                    event.getEventId(), event.getEventType());
                    
        } catch (Exception e) {
            log.error("事件发布失败: eventId={}, type={}, error={}", 
                    event.getEventId(), event.getEventType(), e.getMessage(), e);
            throw new EventPublishException("事件发布失败", e);
        }
    }
    
    /**
     * 批量发布事件
     */
    @Transactional
    public void publishBatch(List<DomainEvent> events) {
        for (DomainEvent event : events) {
            publish(event);
        }
    }
    
    private void storeEventLocally(DomainEvent event) {
        EventStore eventStore = EventStore.builder()
            .eventId(event.getEventId())
            .aggregateId(event.getAggregateId())
            .eventType(event.getEventType())
            .eventData(JsonUtils.toJson(event))
            .timestamp(event.getTimestamp())
            .status(EventStatus.PENDING)
            .build();
            
        eventStoreRepository.save(eventStore);
    }
    
    private String generateRoutingKey(DomainEvent event) {
        return event.getEventType().toLowerCase().replace("event", "");
    }
}
```

#### 事件监听器

```java
@Component
@Slf4j
public class PatientEventListener {
    
    @Autowired
    private PatientBasicInfoSyncService syncService;
    
    /**
     * 处理患者创建事件
     */
    @RabbitListener(queues = "referral.patient.created")
    @Retryable(value = Exception.class, maxAttempts = 3)
    public void handlePatientCreated(PatientCreatedEvent event) {
        log.info("处理患者创建事件: patientId={}", event.getPatientId());
        
        try {
            // 同步患者基础信息到转诊服务
            syncService.syncPatientBasicInfo(event.getBasicInfo());
            
            // 发送确认消息
            sendAckMessage(event.getEventId());
            
        } catch (Exception e) {
            log.error("处理患者创建事件失败: patientId={}, error={}", 
                    event.getPatientId(), e.getMessage(), e);
            throw e; // 重新抛出异常，触发重试
        }
    }
    
    /**
     * 处理患者更新事件
     */
    @RabbitListener(queues = "referral.patient.updated")
    public void handlePatientUpdated(PatientUpdatedEvent event) {
        log.info("处理患者更新事件: patientId={}, changedFields={}", 
                event.getPatientId(), event.getChangedFields());
        
        // 只同步变更的字段
        syncService.updatePatientBasicInfo(
            event.getPatientId(), 
            event.getNewInfo(), 
            event.getChangedFields()
        );
    }
}
```

### 3. 数据同步服务

#### 患者基础信息同步

```java
@Service
@Transactional
@Slf4j
public class PatientBasicInfoSyncService {
    
    @Autowired
    private PatientBasicInfoRepository patientBasicInfoRepository;
    
    /**
     * 同步患者基础信息
     */
    public void syncPatientBasicInfo(PatientBasicInfo sourceInfo) {
        log.info("同步患者基础信息: patientId={}", sourceInfo.getPatientId());
        
        // 检查是否已存在
        Optional<PatientBasicInfoEntity> existingOpt = 
            patientBasicInfoRepository.findByPatientId(sourceInfo.getPatientId());
            
        if (existingOpt.isPresent()) {
            // 更新现有记录
            PatientBasicInfoEntity existing = existingOpt.get();
            updateBasicInfo(existing, sourceInfo);
            patientBasicInfoRepository.save(existing);
            log.info("更新患者基础信息成功: patientId={}", sourceInfo.getPatientId());
        } else {
            // 创建新记录
            PatientBasicInfoEntity newEntity = createBasicInfoEntity(sourceInfo);
            patientBasicInfoRepository.save(newEntity);
            log.info("创建患者基础信息成功: patientId={}", sourceInfo.getPatientId());
        }
    }
    
    /**
     * 更新患者基础信息（增量更新）
     */
    public void updatePatientBasicInfo(String patientId, PatientBasicInfo newInfo, 
                                     List<String> changedFields) {
        log.info("增量更新患者基础信息: patientId={}, fields={}", patientId, changedFields);
        
        PatientBasicInfoEntity entity = patientBasicInfoRepository
            .findByPatientId(patientId)
            .orElseThrow(() -> new IllegalArgumentException("患者基础信息不存在: " + patientId));
            
        // 只更新变更的字段
        for (String field : changedFields) {
            updateField(entity, newInfo, field);
        }
        
        entity.setUpdateTime(LocalDateTime.now());
        patientBasicInfoRepository.save(entity);
    }
    
    private void updateField(PatientBasicInfoEntity entity, PatientBasicInfo newInfo, String field) {
        switch (field) {
            case "name":
                entity.setName(newInfo.getName());
                break;
            case "gender":
                entity.setGender(newInfo.getGender());
                break;
            case "age":
                entity.setAge(newInfo.getAge());
                break;
            case "phone":
                entity.setPhone(newInfo.getPhone());
                break;
            case "address":
                entity.setAddress(newInfo.getAddress());
                break;
            default:
                log.warn("未知的字段更新: {}", field);
        }
    }
}
```

### 4. 服务间通信

#### Feign客户端配置

```java
/**
 * 患者服务客户端
 */
@FeignClient(
    name = "patient-service",
    path = "/api/patients",
    fallback = PatientServiceFallback.class
)
public interface PatientServiceClient {
    
    @GetMapping("/{patientId}")
    Result<PatientDetailVO> getPatientDetail(@PathVariable("patientId") String patientId);
    
    @GetMapping("/{patientId}/basic-info")
    Result<PatientBasicInfo> getPatientBasicInfo(@PathVariable("patientId") String patientId);
    
    @PostMapping("/batch/basic-info")
    Result<List<PatientBasicInfo>> batchGetBasicInfo(@RequestBody List<String> patientIds);
}

/**
 * 转诊服务客户端
 */
@FeignClient(
    name = "referral-service", 
    path = "/api/referrals",
    fallback = ReferralServiceFallback.class
)
public interface ReferralServiceClient {
    
    @GetMapping("/patient/{patientId}")
    Result<List<ReferralSummaryVO>> getPatientReferrals(@PathVariable("patientId") String patientId);
    
    @GetMapping("/{referralId}")
    Result<ReferralDetailVO> getReferralDetail(@PathVariable("referralId") String referralId);
    
    @GetMapping("/statistics/hospital/{unitId}")
    Result<ReferralStatistics> getHospitalStatistics(@PathVariable("unitId") String unitId);
}
```

#### 服务降级处理

```java
@Component
@Slf4j
public class PatientServiceFallback implements PatientServiceClient {
    
    @Override
    public Result<PatientDetailVO> getPatientDetail(String patientId) {
        log.warn("患者服务调用失败，返回降级数据: patientId={}", patientId);
        
        PatientDetailVO fallbackData = PatientDetailVO.builder()
            .patientId(patientId)
            .name("数据获取失败")
            .status("SERVICE_UNAVAILABLE")
            .build();
            
        return Result.success("服务降级", fallbackData);
    }
    
    @Override
    public Result<PatientBasicInfo> getPatientBasicInfo(String patientId) {
        log.warn("患者基础信息获取失败: patientId={}", patientId);
        return Result.error("患者服务不可用");
    }
}
```

### 5. BFF聚合层实现

#### 患者概览聚合服务

```java
@Service
@Slf4j
public class PatientOverviewBFFService {
    
    @Autowired
    private PatientServiceClient patientClient;
    
    @Autowired
    private ReferralServiceClient referralClient;
    
    @Autowired
    private HealthServiceClient healthClient;
    
    @Autowired
    private AsyncTaskExecutor taskExecutor;
    
    /**
     * 获取患者概览信息
     */
    public PatientOverviewVO getPatientOverview(String patientId) {
        log.info("获取患者概览信息: patientId={}", patientId);
        
        // 并行调用多个服务
        CompletableFuture<PatientDetailVO> patientFuture = CompletableFuture
            .supplyAsync(() -> getPatientDetail(patientId), taskExecutor);
            
        CompletableFuture<List<ReferralSummaryVO>> referralFuture = CompletableFuture
            .supplyAsync(() -> getPatientReferrals(patientId), taskExecutor);
            
        CompletableFuture<HealthSummaryVO> healthFuture = CompletableFuture
            .supplyAsync(() -> getPatientHealthSummary(patientId), taskExecutor);
        
        try {
            // 等待所有调用完成
            CompletableFuture.allOf(patientFuture, referralFuture, healthFuture)
                .get(5, TimeUnit.SECONDS); // 5秒超时
                
            // 聚合结果
            return PatientOverviewVO.builder()
                .patientDetail(patientFuture.get())
                .referralHistory(referralFuture.get())
                .healthSummary(healthFuture.get())
                .build();
                
        } catch (TimeoutException e) {
            log.warn("获取患者概览信息超时: patientId={}", patientId);
            return buildFallbackOverview(patientId);
        } catch (Exception e) {
            log.error("获取患者概览信息失败: patientId={}, error={}", patientId, e.getMessage(), e);
            throw new BFFServiceException("获取患者概览信息失败", e);
        }
    }
    
    private PatientDetailVO getPatientDetail(String patientId) {
        try {
            Result<PatientDetailVO> result = patientClient.getPatientDetail(patientId);
            return result.isSuccess() ? result.getData() : null;
        } catch (Exception e) {
            log.warn("获取患者详情失败: patientId={}, error={}", patientId, e.getMessage());
            return null;
        }
    }
    
    private List<ReferralSummaryVO> getPatientReferrals(String patientId) {
        try {
            Result<List<ReferralSummaryVO>> result = referralClient.getPatientReferrals(patientId);
            return result.isSuccess() ? result.getData() : Collections.emptyList();
        } catch (Exception e) {
            log.warn("获取患者转诊记录失败: patientId={}, error={}", patientId, e.getMessage());
            return Collections.emptyList();
        }
    }
}
```

### 6. 分布式事务处理

#### Saga模式实现

```java
/**
 * 转诊创建Saga
 */
@Component
@Slf4j
public class CreateReferralSaga {
    
    @Autowired
    private PatientServiceClient patientClient;
    
    @Autowired
    private ReferralServiceClient referralClient;
    
    @Autowired
    private NotificationServiceClient notificationClient;
    
    /**
     * 执行转诊创建流程
     */
    @SagaOrchestrationStart
    public void createReferral(CreateReferralCommand command) {
        log.info("开始转诊创建流程: patientId={}", command.getPatientId());
        
        SagaTransaction transaction = SagaTransaction.builder()
            .sagaId(UUID.randomUUID().toString())
            .sagaType("CREATE_REFERRAL")
            .status(SagaStatus.STARTED)
            .build();
            
        try {
            // 步骤1：验证患者信息
            validatePatient(transaction, command.getPatientId());
            
            // 步骤2：创建转诊表单
            createReferralForm(transaction, command);
            
            // 步骤3：发送通知
            sendNotification(transaction, command);
            
            // 完成Saga
            completeSaga(transaction);
            
        } catch (Exception e) {
            log.error("转诊创建流程失败: sagaId={}, error={}", 
                    transaction.getSagaId(), e.getMessage(), e);
            compensateSaga(transaction);
        }
    }
    
    private void validatePatient(SagaTransaction transaction, String patientId) {
        SagaStep step = SagaStep.builder()
            .stepName("VALIDATE_PATIENT")
            .compensationMethod("compensateValidatePatient")
            .build();
            
        transaction.addStep(step);
        
        Result<PatientDetailVO> result = patientClient.getPatientDetail(patientId);
        if (!result.isSuccess()) {
            throw new SagaException("患者信息验证失败: " + result.getMessage());
        }
        
        step.setStatus(StepStatus.COMPLETED);
    }
    
    private void compensateValidatePatient(SagaTransaction transaction) {
        log.info("补偿患者验证步骤: sagaId={}", transaction.getSagaId());
        // 患者验证步骤通常不需要补偿
    }
}
```

这个技术实现指南提供了：

1. **完整的事件驱动架构**：包括事件定义、发布、监听机制
2. **数据同步方案**：解决跨服务数据一致性问题
3. **服务间通信**：Feign客户端和降级处理
4. **BFF聚合层**：解决前端多服务调用问题
5. **分布式事务**：Saga模式处理复杂业务流程

这些技术方案能够有效解决微服务架构中的业务强依赖问题。
