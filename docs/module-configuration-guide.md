# 模块配置和依赖管理指南

## 模块依赖配置

### 1. 依赖注入配置

#### 患者模块配置

```java
package com.hys.hm.application.patient.config;

/**
 * 患者模块配置类
 */
@Configuration
@ComponentScan(basePackages = {
    "com.hys.hm.domain.patient",
    "com.hys.hm.application.patient",
    "com.hys.hm.infrastructure.persistence.patient"
})
@EnableJpaRepositories(
    basePackages = "com.hys.hm.infrastructure.persistence.patient.repository",
    repositoryFactoryBeanClass = BaseRepositoryFactoryBean.class
)
public class PatientModuleConfiguration {
    
    /**
     * 患者查询服务Bean
     */
    @Bean
    @Primary
    public PatientQueryService patientQueryService(
            PatientQueryServiceImpl patientQueryServiceImpl) {
        return patientQueryServiceImpl;
    }
    
    /**
     * 患者应用服务Bean
     */
    @Bean
    public PatientApplicationService patientApplicationService(
            PatientDomainService patientDomainService,
            PatientQueryService patientQueryService,
            InternalEventPublisher eventPublisher) {
        return new PatientApplicationService(
            patientDomainService, 
            patientQueryService, 
            eventPublisher
        );
    }
}
```

#### 转诊模块配置

```java
package com.hys.hm.application.referral.config;

/**
 * 转诊模块配置类
 */
@Configuration
@ComponentScan(basePackages = {
    "com.hys.hm.domain.referral",
    "com.hys.hm.application.referral",
    "com.hys.hm.infrastructure.persistence.referral"
})
@EnableJpaRepositories(
    basePackages = "com.hys.hm.infrastructure.persistence.referral.repository",
    repositoryFactoryBeanClass = BaseRepositoryFactoryBean.class
)
public class ReferralModuleConfiguration {
    
    /**
     * 转诊查询服务Bean
     */
    @Bean
    @Primary
    public ReferralQueryService referralQueryService(
            ReferralQueryServiceImpl referralQueryServiceImpl) {
        return referralQueryServiceImpl;
    }
    
    /**
     * 转诊应用服务Bean
     */
    @Bean
    public ReferralApplicationService referralApplicationService(
            ReferralDomainService referralDomainService,
            ReferralQueryService referralQueryService,
            PatientQueryService patientQueryService,  // 依赖患者模块
            InternalEventPublisher eventPublisher) {
        return new ReferralApplicationService(
            referralDomainService,
            referralQueryService,
            patientQueryService,
            eventPublisher
        );
    }
    
    /**
     * 患者信息同步服务Bean
     */
    @Bean
    public ReferralPatientInfoSyncService referralPatientInfoSyncService(
            ReferralPatientInfoRepository repository) {
        return new ReferralPatientInfoSyncService(repository);
    }
}
```

### 2. 主配置类

```java
package com.hys.hm.bootstrap.config;

/**
 * 应用主配置类
 */
@SpringBootApplication
@Import({
    // 共享模块配置
    SharedFrameworkConfiguration.class,
    SharedEventsConfiguration.class,
    
    // 业务模块配置
    PatientModuleConfiguration.class,
    ReferralModuleConfiguration.class,
    HealthModuleConfiguration.class,
    FollowupModuleConfiguration.class,
    KnowledgeModuleConfiguration.class,
    CommunicationModuleConfiguration.class,
    
    // 基础设施配置
    PersistenceConfiguration.class,
    WebConfiguration.class
})
public class HealthManagementApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(HealthManagementApplication.class, args);
    }
}
```

### 3. 事件配置

```java
package com.hys.hm.shared.events.config;

/**
 * 事件配置类
 */
@Configuration
@EnableAsync
public class EventConfiguration {
    
    /**
     * 异步事件执行器
     */
    @Bean("eventTaskExecutor")
    public TaskExecutor eventTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("Event-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
    
    /**
     * 事件监听器异步配置
     */
    @Bean
    public AsyncConfigurer asyncConfigurer() {
        return new AsyncConfigurer() {
            @Override
            public Executor getAsyncExecutor() {
                return eventTaskExecutor();
            }
            
            @Override
            public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
                return new SimpleAsyncUncaughtExceptionHandler();
            }
        };
    }
    
    /**
     * 内部事件发布器
     */
    @Bean
    public InternalEventPublisher internalEventPublisher(
            ApplicationEventPublisher eventPublisher) {
        return new InternalEventPublisher(eventPublisher);
    }
}
```

## Maven模块依赖管理

### 1. 父POM配置

```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>
    
    <groupId>com.hys.hm</groupId>
    <artifactId>hm-health-management</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    
    <name>Health Management System</name>
    <description>健康管理系统</description>
    
    <modules>
        <!-- 启动模块 -->
        <module>hm-bootstrap</module>
        
        <!-- 接口层 -->
        <module>hm-interfaces/hm-interfaces-web</module>
        <module>hm-interfaces/hm-interfaces-api</module>
        
        <!-- 应用服务层 -->
        <module>hm-application/hm-application-patient</module>
        <module>hm-application/hm-application-referral</module>
        <module>hm-application/hm-application-health</module>
        <module>hm-application/hm-application-followup</module>
        <module>hm-application/hm-application-knowledge</module>
        <module>hm-application/hm-application-shared</module>
        
        <!-- 领域层 -->
        <module>hm-domain/hm-domain-patient</module>
        <module>hm-domain/hm-domain-referral</module>
        <module>hm-domain/hm-domain-health</module>
        <module>hm-domain/hm-domain-followup</module>
        <module>hm-domain/hm-domain-knowledge</module>
        <module>hm-domain/hm-domain-shared</module>
        
        <!-- 基础设施层 -->
        <module>hm-infrastructure/hm-infrastructure-persistence-patient</module>
        <module>hm-infrastructure/hm-infrastructure-persistence-referral</module>
        <module>hm-infrastructure/hm-infrastructure-persistence-health</module>
        <module>hm-infrastructure/hm-infrastructure-persistence-followup</module>
        <module>hm-infrastructure/hm-infrastructure-persistence-knowledge</module>
        <module>hm-infrastructure/hm-infrastructure-external</module>
        <module>hm-infrastructure/hm-infrastructure-ai</module>
        <module>hm-infrastructure/hm-infrastructure-logging</module>
        
        <!-- 共享模块 -->
        <module>hm-shared/hm-shared-framework</module>
        <module>hm-shared/hm-shared-common</module>
        <module>hm-shared/hm-shared-types</module>
        <module>hm-shared/hm-shared-events</module>
    </modules>
    
    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        
        <spring-boot.version>3.2.5</spring-boot.version>
        <spring-cloud.version>2023.0.1</spring-cloud.version>
        <lombok.version>1.18.38</lombok.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
    </properties>
    
    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot BOM -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            
            <!-- 内部模块依赖 -->
            <dependency>
                <groupId>com.hys.hm</groupId>
                <artifactId>hm-shared-framework</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hys.hm</groupId>
                <artifactId>hm-shared-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hys.hm</groupId>
                <artifactId>hm-shared-types</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hys.hm</groupId>
                <artifactId>hm-shared-events</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
```

### 2. 应用服务模块POM示例

```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>
    
    <parent>
        <groupId>com.hys.hm</groupId>
        <artifactId>hm-health-management</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    
    <artifactId>hm-application-referral</artifactId>
    <name>Referral Application Service</name>
    <description>转诊应用服务模块</description>
    
    <dependencies>
        <!-- 依赖领域层 -->
        <dependency>
            <groupId>com.hys.hm</groupId>
            <artifactId>hm-domain-referral</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <!-- 依赖患者领域（用于查询服务） -->
        <dependency>
            <groupId>com.hys.hm</groupId>
            <artifactId>hm-domain-patient</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <!-- 依赖共享模块 -->
        <dependency>
            <groupId>com.hys.hm</groupId>
            <artifactId>hm-shared-framework</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hys.hm</groupId>
            <artifactId>hm-shared-types</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hys.hm</groupId>
            <artifactId>hm-shared-events</artifactId>
        </dependency>
        
        <!-- Spring Boot依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>
        
        <!-- 工具依赖 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        
        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
```

### 3. 启动模块POM

```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>
    
    <parent>
        <groupId>com.hys.hm</groupId>
        <artifactId>hm-health-management</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    
    <artifactId>hm-bootstrap</artifactId>
    <name>Health Management Bootstrap</name>
    <description>健康管理系统启动模块</description>
    
    <dependencies>
        <!-- 接口层依赖 -->
        <dependency>
            <groupId>com.hys.hm</groupId>
            <artifactId>hm-interfaces-web</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <!-- 应用服务层依赖 -->
        <dependency>
            <groupId>com.hys.hm</groupId>
            <artifactId>hm-application-patient</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.hys.hm</groupId>
            <artifactId>hm-application-referral</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.hys.hm</groupId>
            <artifactId>hm-application-health</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.hys.hm</groupId>
            <artifactId>hm-application-shared</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <!-- 基础设施层依赖 -->
        <dependency>
            <groupId>com.hys.hm</groupId>
            <artifactId>hm-infrastructure-persistence-patient</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.hys.hm</groupId>
            <artifactId>hm-infrastructure-persistence-referral</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <!-- Spring Boot Starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        
        <!-- 数据库驱动 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
    
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
```

## 模块边界控制

### 1. ArchUnit测试

```java
package com.hys.hm.architecture;

/**
 * 架构规则测试
 */
public class ArchitectureTest {
    
    private static final JavaClasses classes = ClassFileImporter
        .importPackages("com.hys.hm");
    
    @Test
    public void 应用服务层不应该依赖基础设施层() {
        noClasses()
            .that().resideInAPackage("..application..")
            .should().dependOnClassesThat()
            .resideInAPackage("..infrastructure..")
            .check(classes);
    }
    
    @Test
    public void 领域层不应该依赖应用服务层() {
        noClasses()
            .that().resideInAPackage("..domain..")
            .should().dependOnClassesThat()
            .resideInAPackage("..application..")
            .check(classes);
    }
    
    @Test
    public void 领域层不应该依赖基础设施层() {
        noClasses()
            .that().resideInAPackage("..domain..")
            .should().dependOnClassesThat()
            .resideInAPackage("..infrastructure..")
            .check(classes);
    }
    
    @Test
    public void 模块间不应该直接访问对方的基础设施层() {
        noClasses()
            .that().resideInAPackage("..patient..")
            .should().dependOnClassesThat()
            .resideInAPackage("..referral..infrastructure..")
            .check(classes);
    }
}
```

### 2. 模块接口检查

```java
package com.hys.hm.architecture;

/**
 * 模块接口规则检查
 */
public class ModuleInterfaceTest {
    
    @Test
    public void 模块间只能通过查询服务接口通信() {
        classes()
            .that().resideInAPackage("..application.referral..")
            .and().areNotInterfaces()
            .should().onlyDependOnClassesThat(
                JavaClass.Predicates.resideInAnyPackage(
                    "..domain.referral..",
                    "..domain.patient.service..", // 只能依赖患者查询服务接口
                    "..shared..",
                    "java..",
                    "org.springframework..",
                    "jakarta.."
                )
            )
            .check(classes);
    }
}
```

这个配置指南确保了：

1. **清晰的模块边界**：通过Maven模块和包结构明确边界
2. **正确的依赖关系**：通过依赖注入配置管理模块间依赖
3. **架构规则检查**：通过ArchUnit确保架构规则不被违反
4. **事件机制配置**：支持模块间的异步通信

这样的配置为未来的微服务拆分提供了良好的基础。
