# 生产环境配置
server:
  port: 8080
  servlet:
    context-path: /hm

spring:
  # 数据库配置 - 生产环境
  datasource:
    url: jdbc:mysql://${DB_HOST:prod-db.hm.com}:${DB_PORT:3306}/${DB_NAME:health_management_prod}?useUnicode=true&characterEncoding=utf8&useSSL=true&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=false
    username: ${DB_USERNAME:hm_prod}
    password: ${DB_PASSWORD}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 50
      minimum-idle: 10
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      pool-name: HM-Prod-HikariCP
      auto-commit: true
      connection-test-query: SELECT 1

  # JPA/Hibernate配置 - 生产环境
  jpa:
    hibernate:
      ddl-auto: validate  # 生产环境严格验证，不允许修改表结构
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: false
        use_sql_comments: false
        jdbc:
          batch_size: 50
          fetch_size: 100
        order_inserts: true
        order_updates: true
        batch_versioned_data: true
        cache:
          use_second_level_cache: true   # 生产环境开启二级缓存
          use_query_cache: true
        generate_statistics: false

  # Redis配置 - 生产环境
  data:
    redis:
      host: ${REDIS_HOST:prod-redis.hm.com}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD}
      database: ${REDIS_DATABASE:0}
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 5
          max-wait: -1ms
    
    # MongoDB配置 - 生产环境
    mongodb:
      uri: mongodb://${MONGO_USERNAME}:${MONGO_PASSWORD}@${MONGO_HOST:prod-mongo.hm.com}:${MONGO_PORT:27017}/${MONGO_DATABASE:health_management_prod}?authSource=admin&ssl=true
      auto-index-creation: false  # 生产环境不自动创建索引

  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 3600000  # 1小时缓存
      cache-null-values: false

  # Flyway数据库迁移配置
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: false  # 生产环境不允许基线迁移
    validate-on-migrate: true
    encoding: UTF-8
    clean-disabled: true  # 生产环境严格禁止清理

# 日志配置 - 生产环境
logging:
  level:
    com.hys.hm: INFO
    org.springframework.cache: WARN
    org.hibernate.SQL: WARN
    org.springframework.web: WARN
    org.springframework.security: WARN
    org.springframework.boot: WARN
    org.springframework.transaction: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
  file:
    name: /var/log/hm/hm-prod.log
    max-size: 500MB
    max-history: 365

# 管理端点配置 - 生产环境
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus  # 生产环境只暴露必要端点
  endpoint:
    health:
      show-details: never  # 生产环境不显示详细信息
    shutdown:
      enabled: false  # 生产环境严格禁止远程关闭

# 应用信息
info:
  app:
    name: 健康管理系统
    description: 基于Spring Boot的健康管理系统生产环境
    version: 0.0.1-SNAPSHOT
    environment: prod
    encoding: UTF-8
    java:
      version: 21

# 自定义配置
hm:
  # 日志配置
  logging:
    enabled: true
    async: true
    level: INFO
    retention-days: 365
    
  # 安全配置
  security:
    jwt:
      secret: ${JWT_SECRET}  # 生产环境必须从环境变量获取
      expiration: 1800  # 30分钟
    
  # 文件上传配置
  file:
    upload:
      path: ${FILE_UPLOAD_PATH:/var/hm/uploads}
      max-size: 10MB
      allowed-types: jpg,jpeg,png,pdf,doc,docx
    
  # 外部服务配置
  external:
    ai:
      enabled: true
      api-key: ${AI_API_KEY}
      timeout: 10000
    
    sms:
      enabled: true
      provider: ${SMS_PROVIDER:aliyun}
      config:
        access-key: ${SMS_ACCESS_KEY}
        secret-key: ${SMS_SECRET_KEY}
    
    email:
      enabled: true
      smtp:
        host: ${SMTP_HOST}
        port: ${SMTP_PORT:587}
        username: ${SMTP_USERNAME}
        password: ${SMTP_PASSWORD}
        ssl: true

  # 生产环境特殊配置
  prod:
    # 性能监控
    monitoring:
      enabled: true
      metrics-export: true
      health-check-interval: 30000
    
    # 安全配置
    security:
      rate-limit: 1000  # 每分钟1000次请求
      session-timeout: 1800  # 30分钟
      max-login-attempts: 5
      lockout-duration: 900  # 15分钟锁定
    
    # 备份配置
    backup:
      enabled: true
      schedule: "0 0 2 * * ?"  # 每天凌晨2点备份
      retention-days: 30
      location: ${BACKUP_LOCATION:/var/hm/backups}
    
    # 集群配置
    cluster:
      enabled: ${CLUSTER_ENABLED:false}
      node-id: ${NODE_ID:node-1}
      discovery:
        type: ${DISCOVERY_TYPE:static}
        servers: ${DISCOVERY_SERVERS:}
