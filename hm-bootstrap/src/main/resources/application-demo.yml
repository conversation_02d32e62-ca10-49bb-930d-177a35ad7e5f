# 演示环境配置
server:
  port: 8082
  servlet:
    context-path: /hm-demo

spring:
  # 数据库配置 - 演示环境
  datasource:
    url: jdbc:mysql://${DB_HOST:demo.hm.com}:${DB_PORT:3306}/${DB_NAME:health_management_demo}?useUnicode=true&characterEncoding=utf8&useSSL=true&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
    username: ${DB_USERNAME:hm_demo}
    password: ${DB_PASSWORD:demo_secure_password}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 15
      minimum-idle: 3
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      pool-name: HM-Demo-HikariCP
      auto-commit: true
      connection-test-query: SELECT 1

  # JPA/Hibernate配置 - 演示环境
  jpa:
    hibernate:
      ddl-auto: validate  # 演示环境不允许修改表结构
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: false
        jdbc:
          batch_size: 20
          fetch_size: 50
        order_inserts: true
        order_updates: true
        batch_versioned_data: true
        cache:
          use_second_level_cache: true   # 演示环境开启二级缓存
          use_query_cache: true
        generate_statistics: false

  # Redis配置 - 演示环境
  data:
    redis:
      host: ${REDIS_HOST:demo-redis.hm.com}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:demo_redis_secure_pass}
      database: 2
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 6
          max-idle: 6
          min-idle: 1
          max-wait: -1ms
    
    # MongoDB配置 - 演示环境
    mongodb:
      uri: mongodb://${MONGO_USERNAME:hm_demo}:${MONGO_PASSWORD:demo_mongo_pass}@${MONGO_HOST:demo-mongo.hm.com}:${MONGO_PORT:27017}/${MONGO_DATABASE:health_management_demo}
      auto-index-creation: false  # 演示环境不自动创建索引

  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 1800000  # 30分钟缓存
      cache-null-values: false

  # Flyway数据库迁移配置
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true
    encoding: UTF-8
    clean-disabled: true  # 演示环境禁止清理

# 日志配置 - 演示环境
logging:
  level:
    com.hys.hm: INFO
    org.springframework.cache: WARN
    org.hibernate.SQL: WARN
    org.springframework.web: WARN
    org.springframework.security: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
  file:
    name: logs/hm-demo.log
    max-size: 200MB
    max-history: 60

# 管理端点配置 - 演示环境
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
    shutdown:
      enabled: false  # 演示环境禁止远程关闭

# 应用信息
info:
  app:
    name: 健康管理系统-演示环境
    description: 基于Spring Boot的健康管理系统演示环境
    version: 0.0.1-SNAPSHOT
    environment: demo
    encoding: UTF-8
    java:
      version: 21

# 自定义配置
hm:
  # 日志配置
  logging:
    enabled: true
    async: true
    level: INFO
    retention-days: 90
    
  # 安全配置
  security:
    jwt:
      secret: ${JWT_SECRET:demo_jwt_secret_key_for_health_management_system_demo_env}
      expiration: 3600  # 1小时
    
  # 文件上传配置
  file:
    upload:
      path: ${FILE_UPLOAD_PATH:/var/hm-demo/uploads}
      max-size: 5MB
      allowed-types: jpg,jpeg,png,pdf
    
  # 外部服务配置
  external:
    ai:
      enabled: true
      api-key: ${AI_API_KEY:demo_ai_key}
      timeout: 15000
    
    sms:
      enabled: true
      provider: demo  # 演示环境使用模拟短信
    
    email:
      enabled: true
      smtp:
        host: ${SMTP_HOST:smtp.demo.hm.com}
        port: ${SMTP_PORT:587}
        username: ${SMTP_USERNAME:<EMAIL>}
        password: ${SMTP_PASSWORD:demo_email_secure_pass}

  # 演示环境特殊配置
  demo:
    # 数据重置配置
    data-reset:
      enabled: true
      schedule: "0 0 2 * * ?"  # 每天凌晨2点重置演示数据
      backup-before-reset: true
    
    # 访问限制
    access:
      max-users: 50
      session-timeout: 1800  # 30分钟
      rate-limit: 100  # 每分钟100次请求
    
    # 功能限制
    features:
      data-export: false  # 禁止数据导出
      data-import: false  # 禁止数据导入
      user-registration: true  # 允许用户注册
      admin-operations: false  # 禁止管理员操作
