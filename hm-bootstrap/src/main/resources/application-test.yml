# 测试环境配置
server:
  port: 8081
  servlet:
    context-path: /hm-test

spring:
  # 数据库配置 - 测试环境
  datasource:
    url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:health_management_test}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
    username: ${DB_USERNAME:hm_test}
    password: ${DB_PASSWORD:test_password}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 2
      connection-timeout: 30000
      idle-timeout: 300000
      max-lifetime: 900000
      pool-name: HM-Test-HikariCP
      auto-commit: true
      connection-test-query: SELECT 1

  # JPA/Hibernate配置 - 测试环境
  jpa:
    hibernate:
      ddl-auto: update  # 测试环境允许自动更新表结构
    show-sql: true      # 测试环境显示SQL
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 10
          fetch_size: 25
        order_inserts: true
        order_updates: true
        batch_versioned_data: true
        cache:
          use_second_level_cache: false
          use_query_cache: false
        generate_statistics: true  # 测试环境开启统计

  # Redis配置 - 测试环境
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6380}  # 使用不同端口
      password: ${REDIS_PASSWORD:test_redis_pass}
      database: 1  # 使用不同数据库
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 4
          max-idle: 4
          min-idle: 0
          max-wait: -1ms
    
    # MongoDB配置 - 测试环境
    mongodb:
      uri: mongodb://${MONGO_USERNAME:hm_test}:${MONGO_PASSWORD:test_mongo_pass}@${MONGO_HOST:localhost}:${MONGO_PORT:27018}/${MONGO_DATABASE:health_management_test}
      auto-index-creation: true

  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 300000  # 5分钟缓存
      cache-null-values: false

  # Flyway数据库迁移配置
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true
    encoding: UTF-8
    clean-disabled: false  # 测试环境允许清理

# 日志配置 - 测试环境
logging:
  level:
    com.hys.hm: DEBUG
    org.springframework.cache: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.springframework.web: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
  file:
    name: logs/hm-test.log
    max-size: 100MB
    max-history: 30

# 管理端点配置 - 测试环境
management:
  endpoints:
    web:
      exposure:
        include: "*"  # 测试环境暴露所有端点
  endpoint:
    health:
      show-details: always
    shutdown:
      enabled: true  # 测试环境允许远程关闭

# 应用信息
info:
  app:
    name: 健康管理系统-测试环境
    description: 基于Spring Boot的健康管理系统测试环境
    version: 0.0.1-SNAPSHOT
    environment: test
    encoding: UTF-8
    java:
      version: 21

# 自定义配置
hm:
  # 日志配置
  logging:
    enabled: true
    async: true
    level: DEBUG
    retention-days: 30
    
  # 安全配置
  security:
    jwt:
      secret: ${JWT_SECRET:test_jwt_secret_key_for_health_management_system}
      expiration: 7200  # 2小时
    
  # 文件上传配置
  file:
    upload:
      path: ${FILE_UPLOAD_PATH:/tmp/hm-test/uploads}
      max-size: 10MB
      allowed-types: jpg,jpeg,png,pdf,doc,docx
    
  # 外部服务配置
  external:
    ai:
      enabled: true
      api-key: ${AI_API_KEY:test_ai_key}
      timeout: 30000
    
    sms:
      enabled: false  # 测试环境关闭短信
      provider: mock
    
    email:
      enabled: true
      smtp:
        host: ${SMTP_HOST:smtp.test.com}
        port: ${SMTP_PORT:587}
        username: ${SMTP_USERNAME:<EMAIL>}
        password: ${SMTP_PASSWORD:test_email_pass}
