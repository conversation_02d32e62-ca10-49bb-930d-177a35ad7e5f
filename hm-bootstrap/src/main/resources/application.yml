# 健康管理系统主配置文件
# 此文件包含所有环境的通用配置，具体环境配置在对应的 application-{profile}.yml 中

server:
  # 服务器通用配置
  servlet:
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  # 优雅关闭配置
  shutdown: graceful
  # 压缩配置
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024

spring:
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}  # 默认开发环境，可通过环境变量覆盖

  application:
    name: health-management-system

  # 国际化配置
  messages:
    basename: i18n/messages
    encoding: UTF-8
    cache-duration: 3600

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    default-property-inclusion: non-null
    serialization:
      write-dates-as-timestamps: false
      fail-on-empty-beans: false
    deserialization:
      fail-on-unknown-properties: false

  # 文件上传配置
  servlet:
    multipart:
      enabled: true
      max-file-size: 100MB
      max-request-size: 100MB
      file-size-threshold: 2KB

  # 任务调度配置
  task:
    execution:
      pool:
        core-size: 8
        max-size: 16
        queue-capacity: 100
        keep-alive: 60s
      thread-name-prefix: hm-task-
    scheduling:
      pool:
        size: 4
      thread-name-prefix: hm-scheduling-

# 通用日志配置（具体级别在各环境配置中设置）
logging:
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"

# 通用管理端点配置
management:
  server:
    port: ${MANAGEMENT_PORT:8081}  # 管理端点使用独立端口
  endpoints:
    web:
      base-path: /actuator
  endpoint:
    health:
      probes:
        enabled: true
  health:
    livenessstate:
      enabled: true
    readinessstate:
      enabled: true

# 应用信息
info:
  app:
    name: 健康管理系统
    description: 基于Spring Boot的健康管理系统
    version: '@project.version@'  # 从Maven获取版本号
    encoding: UTF-8
    java:
      version: '@java.version@'  # 从Maven获取Java版本
  build:
    artifact: '@project.artifactId@'
    group: '@project.groupId@'
    time: '@maven.build.timestamp@'

# SpringDoc OpenAPI 配置
springdoc:
  api-docs:
    path: /api-docs
    enabled: true
  swagger-ui:
    enabled: false  # 禁用默认的 Swagger UI
  packages-to-scan: com.hys.hm.interfaces.web
  paths-to-match: /api/**

# 通用自定义配置
hm:
  # 应用基本信息
  app:
    name: 健康管理系统
    version: 3.0.0
    author: hys
    contact: <EMAIL>

  # 通用功能开关
  features:
    logging: true
    caching: true
    monitoring: true
    security: true
