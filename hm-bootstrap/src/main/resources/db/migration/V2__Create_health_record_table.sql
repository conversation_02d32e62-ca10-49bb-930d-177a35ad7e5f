-- 创建健康记录表
CREATE TABLE IF NOT EXISTS `health_record` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '记录ID',
    `patient_id` BIGINT NOT NULL COMMENT '患者ID',
    `record_type` VARCHAR(50) NOT NULL COMMENT '记录类型：CHECKUP-体检，DIAGNOSIS-诊断，TREATMENT-治疗',
    `record_date` DATETIME NOT NULL COMMENT '记录日期',
    `title` VARCHAR(200) NOT NULL COMMENT '记录标题',
    `description` TEXT COMMENT '记录描述',
    `doctor_name` VARCHAR(100) COMMENT '医生姓名',
    `hospital` VARCHAR(200) COMMENT '医院名称',
    `department` VARCHAR(100) COMMENT '科室',
    `symptoms` TEXT COMMENT '症状',
    `diagnosis` TEXT COMMENT '诊断结果',
    `treatment` TEXT COMMENT '治疗方案',
    `medication` TEXT COMMENT '用药信息',
    `follow_up_date` DATE COMMENT '复查日期',
    `attachments` JSON COMMENT '附件信息（JSON格式）',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-正常，2-删除',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `created_by` BIGINT COMMENT '创建人',
    `updated_by` BIGINT COMMENT '更新人',
    PRIMARY KEY (`id`),
    KEY `idx_patient_id` (`patient_id`),
    KEY `idx_record_type` (`record_type`),
    KEY `idx_record_date` (`record_date`),
    KEY `idx_created_time` (`created_time`),
    CONSTRAINT `fk_health_record_patient` FOREIGN KEY (`patient_id`) REFERENCES `patient` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='健康记录表';

-- 创建体征数据表
CREATE TABLE IF NOT EXISTS `vital_signs` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '体征ID',
    `patient_id` BIGINT NOT NULL COMMENT '患者ID',
    `health_record_id` BIGINT COMMENT '健康记录ID',
    `measurement_date` DATETIME NOT NULL COMMENT '测量日期',
    `height` DECIMAL(5,2) COMMENT '身高(cm)',
    `weight` DECIMAL(5,2) COMMENT '体重(kg)',
    `bmi` DECIMAL(4,2) COMMENT 'BMI指数',
    `systolic_pressure` INT COMMENT '收缩压(mmHg)',
    `diastolic_pressure` INT COMMENT '舒张压(mmHg)',
    `heart_rate` INT COMMENT '心率(次/分)',
    `temperature` DECIMAL(4,2) COMMENT '体温(°C)',
    `blood_sugar` DECIMAL(5,2) COMMENT '血糖(mmol/L)',
    `blood_oxygen` DECIMAL(5,2) COMMENT '血氧饱和度(%)',
    `notes` TEXT COMMENT '备注',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-正常，2-删除',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_patient_id` (`patient_id`),
    KEY `idx_health_record_id` (`health_record_id`),
    KEY `idx_measurement_date` (`measurement_date`),
    CONSTRAINT `fk_vital_signs_patient` FOREIGN KEY (`patient_id`) REFERENCES `patient` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_vital_signs_health_record` FOREIGN KEY (`health_record_id`) REFERENCES `health_record` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='体征数据表';

-- 创建检查报告表
CREATE TABLE IF NOT EXISTS `medical_report` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '报告ID',
    `patient_id` BIGINT NOT NULL COMMENT '患者ID',
    `health_record_id` BIGINT COMMENT '健康记录ID',
    `report_type` VARCHAR(50) NOT NULL COMMENT '报告类型：BLOOD-血液检查，URINE-尿液检查，IMAGING-影像检查',
    `report_date` DATETIME NOT NULL COMMENT '报告日期',
    `report_title` VARCHAR(200) NOT NULL COMMENT '报告标题',
    `report_content` TEXT COMMENT '报告内容',
    `report_result` TEXT COMMENT '检查结果',
    `reference_range` TEXT COMMENT '参考范围',
    `abnormal_items` TEXT COMMENT '异常项目',
    `doctor_advice` TEXT COMMENT '医生建议',
    `file_path` VARCHAR(500) COMMENT '报告文件路径',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-正常，2-删除',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_patient_id` (`patient_id`),
    KEY `idx_health_record_id` (`health_record_id`),
    KEY `idx_report_type` (`report_type`),
    KEY `idx_report_date` (`report_date`),
    CONSTRAINT `fk_medical_report_patient` FOREIGN KEY (`patient_id`) REFERENCES `patient` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_medical_report_health_record` FOREIGN KEY (`health_record_id`) REFERENCES `health_record` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='检查报告表';
