-- 创建患者表
CREATE TABLE IF NOT EXISTS `patient` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '患者ID',
    `patient_code` VARCHAR(50) NOT NULL COMMENT '患者编号',
    `name` VARCHAR(100) NOT NULL COMMENT '姓名',
    `gender` TINYINT NOT NULL COMMENT '性别：1-男，2-女',
    `birth_date` DATE NOT NULL COMMENT '出生日期',
    `phone` VARCHAR(20) COMMENT '手机号码',
    `email` VARCHAR(100) COMMENT '邮箱',
    `id_card` VARCHAR(20) COMMENT '身份证号',
    `address` VARCHAR(500) COMMENT '地址',
    `emergency_contact` VARCHAR(100) COMMENT '紧急联系人',
    `emergency_phone` VARCHAR(20) COMMENT '紧急联系人电话',
    `medical_history` TEXT COMMENT '病史',
    `allergies` TEXT COMMENT '过敏史',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-正常，2-禁用',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `created_by` BIGINT COMMENT '创建人',
    `updated_by` BIGINT COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_patient_code` (`patient_code`),
    KEY `idx_name` (`name`),
    KEY `idx_phone` (`phone`),
    KEY `idx_id_card` (`id_card`),
    KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='患者信息表';

-- 创建患者标签表
CREATE TABLE IF NOT EXISTS `patient_tag` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '标签ID',
    `tag_name` VARCHAR(50) NOT NULL COMMENT '标签名称',
    `tag_color` VARCHAR(10) COMMENT '标签颜色',
    `description` VARCHAR(200) COMMENT '标签描述',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-正常，2-禁用',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_tag_name` (`tag_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='患者标签表';

-- 创建患者标签关联表
CREATE TABLE IF NOT EXISTS `patient_tag_relation` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `patient_id` BIGINT NOT NULL COMMENT '患者ID',
    `tag_id` BIGINT NOT NULL COMMENT '标签ID',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_patient_tag` (`patient_id`, `tag_id`),
    KEY `idx_patient_id` (`patient_id`),
    KEY `idx_tag_id` (`tag_id`),
    CONSTRAINT `fk_patient_tag_patient` FOREIGN KEY (`patient_id`) REFERENCES `patient` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_patient_tag_tag` FOREIGN KEY (`tag_id`) REFERENCES `patient_tag` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='患者标签关联表';
