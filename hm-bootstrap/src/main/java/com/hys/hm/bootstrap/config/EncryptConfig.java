package com.hys.hm.bootstrap.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 加密配置类
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "app.security.encrypt")
public class EncryptConfig {
    
    /**
     * AES加密密钥
     */
    private String aesKey = "hm-default-aes-key-32-characters";
    
    /**
     * SM4加密密钥
     */
    private String sm4Key = "hm-default-sm4-key-16-chars";
    
    /**
     * 哈希盐值
     */
    private String salt = "hm-default-salt";
    
    /**
     * 字段加密配置
     */
    private Fields fields = new Fields();
    
    @Data
    public static class Fields {
        /**
         * 是否启用字段加密
         */
        private boolean enabled = true;
        
        /**
         * 是否启用模糊查询索引
         */
        private boolean fuzzySearchEnabled = true;
        
        /**
         * 索引清理配置
         */
        private IndexCleanup indexCleanup = new IndexCleanup();
    }
    
    @Data
    public static class IndexCleanup {
        /**
         * 是否启用定时清理
         */
        private boolean enabled = true;
        
        /**
         * 清理间隔（小时）
         */
        private int intervalHours = 24;
        
        /**
         * 保留天数
         */
        private int retentionDays = 90;
    }
}
