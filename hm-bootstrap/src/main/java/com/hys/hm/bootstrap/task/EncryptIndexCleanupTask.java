package com.hys.hm.bootstrap.task;

import com.hys.hm.bootstrap.config.EncryptConfig;
import com.hys.hm.shared.encrypt.repository.EncryptSearchRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 加密索引清理任务
 * 定期清理过期的加密字段搜索索引
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "app.security.encrypt.fields.index-cleanup", name = "enabled", havingValue = "true")
public class EncryptIndexCleanupTask {

    private final EncryptSearchRepository encryptSearchRepository;
    private final EncryptConfig encryptConfig;

    /**
     * 清理过期的加密索引
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    @Transactional
    public void cleanupExpiredIndexes() {
        try {
            int retentionDays = encryptConfig.getFields().getIndexCleanup().getRetentionDays();
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(retentionDays);

            log.info("开始清理过期的加密索引，保留天数: {}, 截止时间: {}", retentionDays, cutoffTime);

            // 删除过期的索引记录
            long deletedCount = encryptSearchRepository.deleteByCreateTimeBefore(cutoffTime);

            log.info("加密索引清理完成，删除记录数: {}", deletedCount);

        } catch (Exception e) {
            log.error("清理加密索引失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 统计加密索引数量
     * 每小时执行一次
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    public void reportIndexStatistics() {
        try {
            long totalCount = encryptSearchRepository.count();
            log.debug("当前加密索引总数: {}", totalCount);

            // 可以添加更多统计信息，如按实体类型统计等

        } catch (Exception e) {
            log.error("统计加密索引失败: {}", e.getMessage(), e);
        }
    }
}
