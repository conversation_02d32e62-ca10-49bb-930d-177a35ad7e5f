package com.hys.hm.bootstrap.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 应用程序配置类
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Configuration
@EnableTransactionManagement
@EnableAsync
@EnableAspectJAutoProxy
public class ApplicationConfiguration {
    
    // 这里可以添加其他全局配置
    
}
