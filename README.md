# 健康管理系统 (HM)

<div align="center">

![Java](https://img.shields.io/badge/Java-21-orange)
![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.5.3-brightgreen)
![Spring AI](https://img.shields.io/badge/Spring%20AI-1.0.0--M6-blue)
![Maven](https://img.shields.io/badge/Maven-3.8+-red)
![License](https://img.shields.io/badge/License-MIT-yellow)

**基于AI技术的智能健康管理平台**

[快速开始](#-快速开始) • [项目架构](#-项目架构) • [功能特性](#-功能特性) • [开发指南](#-开发指南) • [API文档](#-api文档)

</div>

## 📋 项目概述

健康管理系统是一个基于AI技术的智能健康管理平台，旨在为用户提供个性化的健康监测、评估和建议服务。系统采用现代化的分层架构设计，支持多种AI平台集成，为医疗健康领域提供全面的数字化解决方案。

### 🎯 核心功能

- **👥 患者管理**: 用户信息管理、健康档案、标签分类
- **📊 健康评估**: BMI、BMR计算、健康指标分析
- **🤖 AI服务**: 智能对话、健康建议、风险评估
- **📚 知识库**: 健康教育、患教资料、随访计划
- **🔗 外部集成**: 微信授权、短信服务、第三方API
- **🔒 数据安全**: AES加密存储、模糊查询、密钥轮换

### 🛠️ 技术栈

- **语言**: Java 21
- **框架**: Spring Boot 3.5.3, Spring AI 1.0.0-M6
- **数据库**: MySQL 8.0+, MongoDB, Redis
- **构建工具**: Maven 3.8+
- **其他**: Hibernate, JWT, ReDoc, Lombok

## 🚀 快速开始

### 前置要求

- Java 21+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+
- MongoDB 4.4+

### 1. 克隆项目

```bash
git clone https://hysdevgitlab.haoyisheng.info/zhuweichen/hm.git
cd hm
```

### 2. 配置数据库

确保MySQL、Redis、MongoDB服务正在运行，并根据需要修改配置文件：
- `hm-bootstrap/src/main/resources/application-dev.yml`

### 3. 启动应用

```bash
# 使用开发启动脚本（推荐）
./scripts/dev-start.sh dev

# 或者使用传统方式
mvn clean install -DskipTests
mvn spring-boot:run -pl hm-bootstrap -Dspring-boot.run.profiles=dev
```

### 4. 验证启动

访问健康检查端点：
```bash
curl http://localhost:8080/hm-dev/health
```

## 🏗️ 项目架构

### 整体架构

项目采用分层多模块架构，遵循DDD（领域驱动设计）原则：

```
┌─────────────────────────────────────────────────────────────┐
│                    应用启动层 (Bootstrap)                      │
│                      hm-bootstrap                           │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    接口适配层 (Interface)                     │
│              hm-interfaces-web, hm-interfaces-api             │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    应用服务层 (Application)                   │
│        hm-application-patient, hm-application-health        │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    领域服务层 (Domain)                       │
│          hm-domain-patient, hm-domain-health               │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    基础设施层 (Infrastructure)                │
│      hm-infrastructure-persistence, hm-infrastructure-ai    │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    共享内核层 (Shared Kernel)                │
│            hm-shared-common, hm-shared-types               │
└─────────────────────────────────────────────────────────────┘
```

### 模块结构

```
hm (根模块)
├── hm-bootstrap (应用启动层)
├── hm-interfaces (接口适配层)
│   └── hm-interfaces-web
├── hm-application (应用服务层)
│   ├── hm-application-patient
│   ├── hm-application-health
│   ├── hm-application-communication
│   ├── hm-application-followup
│   ├── hm-application-knowledge
│   └── hm-application-referral
├── hm-domain (领域服务层)
│   ├── hm-domain-patient
│   ├── hm-domain-health
│   ├── hm-domain-communication
│   ├── hm-domain-followup
│   ├── hm-domain-knowledge
│   └── hm-domain-referral
├── hm-infrastructure (基础设施层)
│   ├── hm-infrastructure-persistence
│   └── hm-infrastructure-ai
└── hm-shared (共享内核层)
    ├── hm-shared-common
    ├── hm-shared-types
    └── hm-shared-framework
```

## ✨ 功能特性

### 🏥 患者管理
- **用户档案**: 完整的患者信息管理
- **健康档案**: 历史健康数据记录
- **标签系统**: 灵活的患者分类管理
- **权限控制**: 基于角色的访问控制

### 📈 健康评估
- **指标计算**: BMI、BMR等健康指标自动计算
- **趋势分析**: 健康数据变化趋势图表
- **风险评估**: AI驱动的健康风险预测
- **报告生成**: 个性化健康评估报告

### 🤖 AI智能服务
- **智能对话**: 基于Spring AI的智能问答
- **健康建议**: 个性化健康指导建议
- **症状分析**: AI辅助症状识别分析
- **多平台支持**: 支持多种AI平台集成

### 📚 知识库管理
- **患教资料**: 丰富的健康教育内容
- **随访计划**: 智能随访计划制定
- **模板管理**: 7种专业模板支持
- **内容搜索**: 全文检索功能

### 🔗 外部服务集成
- **微信集成**: 微信授权登录
- **短信服务**: 多平台短信发送
- **邮件服务**: SMTP邮件通知
- **第三方API**: 灵活的外部服务接入

### 🔒 数据安全
- **AES加密**: 敏感数据加密存储
- **模糊查询**: 加密数据的模糊搜索
- **密钥轮换**: 自动化密钥管理
- **访问控制**: 细粒度权限管理

## 🛠️ 开发指南

### 环境配置

系统支持多环境配置：

- **开发环境** (`dev`): 用于日常开发调试
- **测试环境** (`test`): 用于功能测试验证
- **演示环境** (`demo`): 用于产品演示
- **生产环境** (`prod`): 用于正式部署

### 启动脚本

```bash
# 开发环境启动
./scripts/dev-start.sh dev

# 测试环境启动
./scripts/dev-start.sh test

# 演示环境启动
./scripts/dev-start.sh demo
```

### 代码规范

- 遵循阿里巴巴Java开发手册
- 使用Lombok减少样板代码
- 统一异常处理机制
- 完善的单元测试覆盖

## 📖 API文档

### 访问地址

启动应用后，可通过以下地址访问API文档：

- **ReDoc文档**: http://localhost:8080/hm-dev/redoc.html
- **Swagger UI**: http://localhost:8080/hm-dev/swagger-ui.html
- **OpenAPI规范**: http://localhost:8080/hm-dev/api-docs

### API分组

- **01-系统管理**: 用户、角色、权限管理
- **02-健康数据**: 健康指标、评估报告
- **03-随访管理**: 随访计划、记录管理
- **04-转诊管理**: 转诊申请、流程管理
- **05-知识库**: 患教资料、模板管理
- **06-沟通交流**: 在线咨询、群发助手
- **07-管理员**: 系统配置、数据管理
- **99-测试接口**: 开发测试专用接口

## 🧪 测试功能

### 加密功能测试

```bash
# 基本加密测试
curl -X POST "http://localhost:8080/hm-dev/api/test/encrypt/test" \
  -d "plainText=测试数据"

# 模糊查询测试
curl -X POST "http://localhost:8080/hm-dev/api/test/encrypt/fuzzy-search" \
  -d "keyword=测试"
```

### 日志功能测试

```bash
# 基本日志测试
curl -X GET "http://localhost:8080/hm-dev/api/test/log/basic"

# 异步日志测试
curl -X GET "http://localhost:8080/hm-dev/api/test/log/async"
```

### AI服务测试

```bash
# AI对话测试
curl -X POST "http://localhost:8080/hm-dev/api/test/ai/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "你好，我想了解健康管理"}'
```

## 📁 项目结构

```
hm/
├── docs/                           # 项目文档
│   ├── project-introduction.md     # 项目介绍
│   ├── QUICK_START.md             # 快速开始指南
│   ├── architecture.md            # 架构设计文档
│   ├── module-structure-guide.md  # 模块结构指南
│   ├── 加密功能API接口文档.md       # 加密功能文档
│   └── ...                        # 其他技术文档
├── scripts/                       # 构建和部署脚本
│   ├── dev-start.sh              # 开发启动脚本
│   ├── version-management.sh     # 版本管理脚本
│   └── ...                       # 其他工具脚本
├── hm-bootstrap/                  # 应用启动模块
├── hm-interfaces/                 # 接口层模块
├── hm-application/                # 应用服务层模块
├── hm-domain/                     # 领域层模块
├── hm-infrastructure/             # 基础设施层模块
├── hm-shared/                     # 共享模块
├── pom.xml                        # 根POM文件
└── README.md                      # 项目说明文档
```

## 🔧 配置说明

### 数据库配置

系统支持多种数据库：

- **MySQL**: 主要业务数据存储
- **MongoDB**: 非结构化数据存储
- **Redis**: 缓存和会话存储

### 环境变量

```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=hm_dev
DB_USERNAME=root
DB_PASSWORD=your_password

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# MongoDB配置
MONGO_HOST=localhost
MONGO_PORT=27017
MONGO_USERNAME=your_mongo_user
MONGO_PASSWORD=your_mongo_password

# JWT配置
JWT_SECRET=your_jwt_secret_key

# AI服务配置
AI_API_KEY=your_ai_api_key
```

## 🚀 部署指南

### Docker部署

```bash
# 构建镜像
docker build -t hm:latest .

# 运行容器
docker run -d \
  --name hm-app \
  -p 8080:8080 \
  -e SPRING_PROFILES_ACTIVE=prod \
  hm:latest
```

### 传统部署

```bash
# 打包应用
mvn clean package -DskipTests

# 运行应用
java -jar hm-bootstrap/target/hm-bootstrap-3.0.1-SNAPSHOT.jar \
  --spring.profiles.active=prod
```

## 📊 监控和运维

### 健康检查

- **应用健康**: `/actuator/health`
- **系统信息**: `/actuator/info`
- **指标监控**: `/actuator/metrics`

### 日志管理

- **日志级别**: 支持动态调整日志级别
- **日志文件**: 按日期和大小自动轮转
- **异步日志**: 提高系统性能

## 🤝 贡献指南

### 开发流程

1. Fork 项目到个人仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 代码规范

- 遵循阿里巴巴Java开发手册
- 使用统一的代码格式化配置
- 编写完整的单元测试
- 添加必要的文档注释

### 提交规范

```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

## 📝 更新日志

### v3.0.1-SNAPSHOT (当前版本)
- ✅ 完成多模块架构重构
- ✅ 集成Spring AI框架
- ✅ 实现AES加密存储功能
- ✅ 添加ReDoc API文档界面
- ✅ 完善多环境配置支持
- ✅ 优化启动脚本和部署流程

### v2.0.0
- ✅ 升级到Spring Boot 3.x
- ✅ 支持Java 21
- ✅ 重构数据库设计
- ✅ 添加微信集成功能


## 🙏 致谢

感谢所有为本项目做出贡献的开发者和用户！

---

<div align="center">

**[⬆ 回到顶部](#健康管理系统-hm)**

Made with ❤️ by HYS Team

</div>
