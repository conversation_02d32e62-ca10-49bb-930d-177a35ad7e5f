# 领域事件机制使用指南

## 概述

hm-shared-framework 现已集成了完整的领域事件机制，基于 Spring 事件系统实现，支持同步和异步事件处理，为应用提供松耦合的业务逻辑处理能力。

## 功能特性

### 1. 完整的事件类型
- **EntityCreatedEvent**：实体创建事件
- **EntityUpdatedEvent**：实体更新事件
- **EntityDeletedEvent**：实体删除事件（支持软删除和硬删除）

### 2. 灵活的事件处理
- **同步处理**：事务内立即处理，适合关键业务逻辑
- **异步处理**：事务提交后异步处理，适合耗时操作
- **事件统计**：提供事件处理统计和监控

### 3. 自动事件发布
- 继承 `BaseServiceImpl` 自动发布实体事件
- 支持手动事件发布
- 事务安全的事件处理

## 配置说明

### 1. 基础配置

在 `application.yml` 中添加事件配置：

```yaml
hm:
  framework:
    event:
      enabled: true
      enable-stats: true
      async:
        core-pool-size: 2
        max-pool-size: 10
        queue-capacity: 100
```

### 2. 异步处理配置

```yaml
hm:
  framework:
    event:
      async:
        core-pool-size: 2          # 核心线程数
        max-pool-size: 10          # 最大线程数
        queue-capacity: 100        # 队列容量
        keep-alive-seconds: 60     # 线程空闲时间
```

### 3. 重试配置

```yaml
hm:
  framework:
    event:
      retry:
        enabled: true              # 启用重试
        max-attempts: 3            # 最大重试次数
        delay-millis: 1000         # 重试间隔
        multiplier: 2.0            # 重试间隔倍数
```

## 使用方式

### 1. 自动事件发布（推荐）

继承 `BaseServiceImpl` 的服务类会自动发布实体事件：

```java
@Service
public class UserServiceImpl extends BaseServiceImpl<User, Long> implements UserService {
    
    // save() 方法自动发布 EntityCreatedEvent
    // update() 方法自动发布 EntityUpdatedEvent
    // deleteById() 方法自动发布 EntityDeletedEvent
}
```

### 2. 手动事件发布

```java
@Service
public class CustomService {
    
    @Autowired
    private DomainEventPublisher eventPublisher;
    
    public void createUser(User user) {
        // 业务逻辑
        userRepository.save(user);
        
        // 手动发布事件
        eventPublisher.publishEntityCreated(user, user.getId(), User.class);
    }
}
```

### 3. 事件监听处理

#### 方式一：继承 DomainEventListener（推荐）

```java
@Component
public class UserEventListener extends DomainEventListener {
    
    @Override
    protected void onEntityCreated(EntityCreatedEvent<?, ?> event) {
        if ("User".equals(event.getAggregateType())) {
            // 同步处理用户创建事件
            handleUserCreated(event);
        }
    }
    
    @Override
    protected void onEntityCreatedAsync(EntityCreatedEvent<?, ?> event) {
        if ("User".equals(event.getAggregateType())) {
            // 异步处理用户创建事件
            sendWelcomeEmail(event);
        }
    }
    
    private void handleUserCreated(EntityCreatedEvent<?, ?> event) {
        // 同步业务逻辑：数据验证、缓存更新等
    }
    
    private void sendWelcomeEmail(EntityCreatedEvent<?, ?> event) {
        // 异步业务逻辑：发送邮件、通知等
    }
}
```

#### 方式二：直接使用 Spring 事件监听

```java
@Component
public class CustomEventListener {
    
    @EventListener
    public void handleUserCreated(EntityCreatedEvent<User, Long> event) {
        // 同步处理
        log.info("用户创建: {}", event.getAggregateId());
    }
    
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleUserCreatedAsync(EntityCreatedEvent<User, Long> event) {
        // 异步处理
        log.info("异步处理用户创建: {}", event.getAggregateId());
    }
}
```

## 事件类型详解

### 1. EntityCreatedEvent

```java
// 事件属性
String eventId;           // 事件ID
LocalDateTime occurredOn; // 发生时间
T entity;                 // 实体对象
ID entityId;              // 实体ID
Class<T> entityType;      // 实体类型
String operatorId;        // 操作用户ID

// 使用示例
EntityCreatedEvent<User, Long> event = EntityCreatedEvent.of(user, user.getId(), User.class);
```

### 2. EntityUpdatedEvent

```java
// 额外属性
T previousEntity;         // 更新前的实体状态（可选）

// 使用示例
EntityUpdatedEvent<User, Long> event = EntityUpdatedEvent.of(
    updatedUser, previousUser, user.getId(), User.class
);
```

### 3. EntityDeletedEvent

```java
// 额外属性
boolean softDelete;       // 是否为软删除
T deletedEntity;          // 删除的实体（可选）

// 使用示例
EntityDeletedEvent<User, Long> event = EntityDeletedEvent.softDelete(
    deletedUser, user.getId(), User.class
);
```

## 事件处理模式

### 1. 同步处理
- **时机**：事务内立即执行
- **用途**：关键业务逻辑、数据验证、缓存更新
- **特点**：会影响主流程性能，失败会回滚事务

### 2. 异步处理
- **时机**：事务提交后异步执行
- **用途**：发送通知、数据同步、统计分析
- **特点**：不影响主流程性能，失败不会回滚事务

## 监控和统计

### 1. 获取事件统计信息

```java
@Autowired
private EventStatsService eventStatsService;

public void printEventStats() {
    // 打印所有事件统计
    eventStatsService.printStats();
    
    // 获取特定事件统计
    EventStatsService.EventStats stats = eventStatsService.getEventStats("EntityCreatedEvent");
    
    // 获取总体统计
    long totalEvents = eventStatsService.getTotalEventCount();
    double errorRate = eventStatsService.getErrorRate();
}
```

### 2. 事件统计指标

- **事件总数**：已处理的事件总数量
- **错误总数**：处理失败的事件数量
- **错误率**：事件处理失败的比例
- **平均处理时间**：事件处理的平均耗时
- **按类型统计**：不同事件类型的详细统计

## 最佳实践

### 1. 事件设计原则
- **单一职责**：每个事件只表示一个业务含义
- **不可变性**：事件对象创建后不应修改
- **完整信息**：事件应包含处理所需的完整信息

### 2. 事件处理原则
- **幂等性**：事件处理应该是幂等的
- **异常处理**：妥善处理事件处理中的异常
- **性能考虑**：避免在同步处理中执行耗时操作

### 3. 监听器设计
- **类型过滤**：在监听器中过滤关心的事件类型
- **错误隔离**：一个监听器的错误不应影响其他监听器
- **日志记录**：记录关键的事件处理日志

## 注意事项

1. **事务一致性**：同步事件在事务内处理，异步事件在事务提交后处理
2. **性能影响**：同步事件会影响主流程性能，需谨慎使用
3. **异常处理**：异步事件处理异常不会影响主流程
4. **顺序保证**：同一类型事件的处理顺序可能不确定
5. **重复处理**：在分布式环境下可能出现事件重复处理

## 故障排查

### 1. 事件未触发
- 检查事件配置是否启用
- 确认服务类是否继承 BaseServiceImpl
- 验证事务配置是否正确

### 2. 异步事件未执行
- 检查异步配置和线程池设置
- 确认 @EnableAsync 注解是否添加
- 查看线程池是否有异常

### 3. 事件处理失败
- 查看事件统计中的错误信息
- 检查监听器中的异常处理
- 验证事件数据的完整性
