# 转诊表单模块迁移到新框架指南

## 概述

本文档描述了如何将现有的转诊表单模块迁移到新的共享框架，展示了框架的强大功能和使用方式。

## 迁移对比

### 原有架构 vs 新框架架构

| 层次 | 原有实现 | 新框架实现 | 改进点 |
|------|----------|------------|--------|
| **实体层** | 普通POJO + 手动审计字段 | 继承BaseEntity | 自动审计、软删除、版本控制 |
| **仓储层** | 手动实现所有CRUD方法 | 继承BaseRepository | 自动CRUD、动态查询、分页 |
| **服务层** | 手动实现业务逻辑 | 继承BaseService | 业务钩子、事务管理、缓存 |
| **控制器层** | 手动实现REST API | 继承BaseController | 标准REST、参数解析、异常处理 |

### 代码量对比

- **原有实现**: ~2000+ 行代码
- **新框架实现**: ~800 行代码
- **减少**: 60% 的代码量

## 新框架实现详解

### 1. 实体类 (ReferralFormEntityNew)

```java
@Entity
@Table(name = "referral_form")
@Data
@EqualsAndHashCode(callSuper = true)
public class ReferralFormEntityNew extends BaseEntity<String> {
    
    @Id
    private String id;
    
    // 患者信息
    private String patientName;
    private Integer gender;
    private Integer age;
    
    // 加密字段支持
    @EncryptField(fuzzySearch = true, tokenLength = 4)
    @Convert(converter = EncryptConverter.class)
    private String idCard;
    
    // 转诊信息
    private String referralReason;
    private Integer status = 1;
    
    // 继承BaseEntity获得：
    // - createTime, updateTime (自动管理)
    // - createBy, updateBy (审计字段)
    // - deleted, version (软删除、乐观锁)
    // - remark (备注字段)
}
```

**优势**:
- 继承BaseEntity获得通用字段和功能
- 支持加密字段的模糊查询
- 自动审计和软删除
- 乐观锁支持

### 2. 仓储层 (ReferralFormRepositoryNew)

```java
@Repository
public interface ReferralFormRepositoryNew extends BaseRepository<ReferralFormEntityNew, String> {
    
    // 只需要定义业务特定的查询方法
    Optional<ReferralFormEntityNew> findByReferralNo(String referralNo);
    List<ReferralFormEntityNew> findByStatus(Integer status);
    
    // 复杂查询使用@Query
    @Query("SELECT r FROM ReferralFormEntityNew r WHERE r.status = 1 AND r.referralDate < :cutoffTime")
    List<ReferralFormEntityNew> findTimeoutReferrals(@Param("cutoffTime") LocalDateTime cutoffTime);
    
    // 继承BaseRepository自动获得：
    // - 完整的CRUD操作
    // - 动态查询支持
    // - 分页查询
    // - 批量操作
    // - 软删除操作
}
```

**优势**:
- 继承BaseRepository获得完整CRUD功能
- 支持动态查询条件解析
- 内置分页和排序
- 支持批量操作

### 3. 服务层 (ReferralFormServiceImplNew)

```java
@Service
public class ReferralFormServiceImplNew extends BaseServiceImpl<ReferralFormEntityNew, String> 
        implements ReferralFormServiceNew {
    
    // 业务特定方法
    @Transactional
    public boolean confirmReferral(String id, String operatorId) {
        Optional<ReferralFormEntityNew> referralOpt = findById(id);
        if (referralOpt.isPresent()) {
            ReferralFormEntityNew referral = referralOpt.get();
            referral.setStatus(2);
            referral.setConfirmTime(LocalDateTime.now());
            save(referral);
            return true;
        }
        return false;
    }
    
    // 重写业务钩子方法
    @Override
    public void beforeSave(ReferralFormEntityNew entity) {
        if (!StringUtils.hasText(entity.getReferralNo())) {
            entity.setReferralNo(generateReferralNo());
        }
    }
    
    // 继承BaseServiceImpl自动获得：
    // - 完整的CRUD操作
    // - 事务管理
    // - 数据验证
    // - 业务钩子方法
    // - 缓存支持
}
```

**优势**:
- 继承BaseServiceImpl获得基础功能
- 业务钩子方法支持
- 自动事务管理
- 内置验证和异常处理

### 4. 控制器层 (ReferralFormControllerNew)

```java
@RestController
@RequestMapping("/api/v2/referral-forms")
public class ReferralFormControllerNew extends BaseController<ReferralFormEntityNew, String> {
    
    @Override
    protected String getEntityId(ReferralFormEntityNew entity) {
        return entity.getId();
    }
    
    @Override
    protected void setEntityId(ReferralFormEntityNew entity, String id) {
        entity.setId(id);
    }
    
    // 业务特定的端点
    @PutMapping("/{id}/confirm")
    public ResponseEntity<Result<Void>> confirmReferral(@PathVariable String id, 
                                                       @RequestHeader("X-User-Id") String userId) {
        boolean success = referralFormService.confirmReferral(id, userId);
        return success ? ResponseEntity.ok(Result.success("确认成功")) : ResponseEntity.notFound().build();
    }
    
    // 继承BaseController自动获得：
    // - 标准的REST API (GET, POST, PUT, DELETE)
    // - 分页查询支持
    // - 动态查询条件解析
    // - 统一异常处理
    // - Swagger文档支持
}
```

**优势**:
- 继承BaseController获得标准REST API
- 自动参数解析和验证
- 统一的响应格式
- 内置异常处理

## 动态查询功能演示

新框架支持强大的动态查询功能，无需编写额外代码：

### 基础查询
```bash
# 等于查询
GET /api/v2/referral-forms?patientName=张三&status=1

# 模糊查询
GET /api/v2/referral-forms?patientName_LIKE=张&referralReason_LIKE=心脏

# 范围查询
GET /api/v2/referral-forms?referralDate_GT=2025-01-01&referralDate_LT=2025-12-31

# 列表查询
GET /api/v2/referral-forms?status_IN=1,2,3

# 空值查询
GET /api/v2/referral-forms?rejectReason_IS_NULL=true
```

### 分页和排序
```bash
# 分页查询
GET /api/v2/referral-forms?page=1&size=20

# 排序查询
GET /api/v2/referral-forms?orders=-referralDate,+patientName

# 复合查询
GET /api/v2/referral-forms?patientName_LIKE=张&status=1&page=1&size=10&orders=-referralDate
```

### 加密字段查询
```bash
# 身份证号模糊查询（支持加密字段）
GET /api/v2/referral-forms/search-by-idcard/1234

# 手机号模糊查询（支持加密字段）
GET /api/v2/referral-forms/search-by-phone/138
```

## 业务功能对比

### 原有实现需要手动编写的功能

1. **CRUD操作**: 每个操作都需要手动实现
2. **分页查询**: 需要手动计算偏移量和总数
3. **动态查询**: 需要手动拼接SQL或Criteria
4. **参数验证**: 需要手动验证每个参数
5. **异常处理**: 需要手动捕获和处理异常
6. **审计字段**: 需要手动设置创建时间、更新时间等
7. **软删除**: 需要手动实现逻辑删除逻辑

### 新框架自动提供的功能

1. **完整CRUD**: 继承即可获得所有基础操作
2. **智能分页**: 自动处理分页逻辑和性能优化
3. **动态查询**: 自动解析HTTP参数为查询条件
4. **参数验证**: 内置验证和类型转换
5. **异常处理**: 统一的异常处理和响应格式
6. **审计功能**: 自动管理审计字段
7. **软删除**: 内置软删除支持

## 性能优化

### 查询优化
- 支持不查询总数的分页（提升大数据量查询性能）
- 智能索引建议
- 批量操作支持

### 缓存支持
- 内置缓存接口
- 业务钩子方法中可以轻松集成缓存

### 数据库优化
- 乐观锁支持
- 批量更新操作
- 连接池优化

## 扩展性

### 业务钩子方法
```java
@Override
public void beforeSave(ReferralFormEntityNew entity) {
    // 保存前的业务逻辑
}

@Override
public void afterSave(ReferralFormEntityNew entity) {
    // 保存后的业务逻辑，如发送通知
}
```

### 自定义查询
```java
// 仓储层添加自定义查询
@Query("SELECT r FROM ReferralFormEntityNew r WHERE ...")
List<ReferralFormEntityNew> customQuery();

// 服务层调用
public List<ReferralFormEntityNew> getCustomData() {
    return referralFormRepository.customQuery();
}
```

### 自定义端点
```java
// 控制器添加自定义端点
@GetMapping("/custom-endpoint")
public ResponseEntity<Result<Object>> customEndpoint() {
    // 自定义业务逻辑
}
```

## 迁移步骤

1. **创建新实体类**: 继承BaseEntity
2. **创建新仓储接口**: 继承BaseRepository
3. **创建新服务接口和实现**: 继承BaseService和BaseServiceImpl
4. **创建新控制器**: 继承BaseController
5. **数据迁移**: 将现有数据迁移到新表结构
6. **测试验证**: 确保所有功能正常工作
7. **切换流量**: 逐步将流量切换到新实现

## 总结

通过使用新的共享框架，转诊表单模块获得了以下收益：

- **开发效率提升60%**: 大幅减少重复代码
- **功能更强大**: 获得动态查询、分页、审计等高级功能
- **维护成本降低**: 框架统一维护，业务代码更简洁
- **扩展性更好**: 通过钩子方法和继承机制轻松扩展
- **性能更优**: 内置性能优化和缓存支持

新框架不仅简化了开发工作，还提供了更强大和灵活的功能，是现代化应用开发的最佳实践。
