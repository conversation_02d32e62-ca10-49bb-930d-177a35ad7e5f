package com.hys.hm.shared.framework.event;

import lombok.Getter;

/**
 * 实体更新事件
 * 当实体被更新时发布此事件
 *
 * @param <T> 实体类型
 * @param <ID> 实体ID类型
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-30
 */
@Getter
public class EntityUpdatedEvent<T, ID> extends EntityEvent<T, ID> {

    /**
     * 更新前的实体状态（可选）
     */
    private final T previousEntity;

    /**
     * 构造函数
     */
    public EntityUpdatedEvent(T entity, ID entityId, Class<T> entityType) {
        super(entity, entityId, entityType);
        this.previousEntity = null;
    }

    /**
     * 构造函数（带更新前状态）
     */
    public EntityUpdatedEvent(T entity, T previousEntity, ID entityId, Class<T> entityType) {
        super(entity, entityId, entityType);
        this.previousEntity = previousEntity;
    }

    /**
     * 构造函数（带操作用户）
     */
    public EntityUpdatedEvent(T entity, ID entityId, Class<T> entityType, String operatorId) {
        super(entity, entityId, entityType, operatorId);
        this.previousEntity = null;
    }

    /**
     * 构造函数（完整参数）
     */
    public EntityUpdatedEvent(T entity, T previousEntity, ID entityId, Class<T> entityType, String operatorId) {
        super(entity, entityId, entityType, operatorId);
        this.previousEntity = previousEntity;
    }

    /**
     * 静态工厂方法
     */
    public static <T, ID> EntityUpdatedEvent<T, ID> of(T entity, ID entityId, Class<T> entityType) {
        return new EntityUpdatedEvent<>(entity, entityId, entityType);
    }

    /**
     * 静态工厂方法（带更新前状态）
     */
    public static <T, ID> EntityUpdatedEvent<T, ID> of(T entity, T previousEntity, ID entityId, Class<T> entityType) {
        return new EntityUpdatedEvent<>(entity, previousEntity, entityId, entityType);
    }

    /**
     * 静态工厂方法（带操作用户）
     */
    public static <T, ID> EntityUpdatedEvent<T, ID> of(T entity, ID entityId, Class<T> entityType, String operatorId) {
        return new EntityUpdatedEvent<>(entity, entityId, entityType, operatorId);
    }

    /**
     * 静态工厂方法（完整参数）
     */
    public static <T, ID> EntityUpdatedEvent<T, ID> of(T entity, T previousEntity, ID entityId, Class<T> entityType, String operatorId) {
        return new EntityUpdatedEvent<>(entity, previousEntity, entityId, entityType, operatorId);
    }

    /**
     * 是否有更新前的状态
     */
    public boolean hasPreviousEntity() {
        return previousEntity != null;
    }
}
