package com.hys.hm.shared.framework.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 框架配置属性类
 * 用于配置框架的各种参数
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Data
@ConfigurationProperties(prefix = "hm.framework")
public class FrameworkProperties {

    /**
     * 分页配置
     */
    private PageConfig page = new PageConfig();

    /**
     * 查询配置
     */
    private QueryConfig query = new QueryConfig();

    /**
     * 缓存配置
     */
    private CacheConfig cache = new CacheConfig();

    /**
     * 日志配置
     */
    private LogConfig log = new LogConfig();

    /**
     * 分页配置类
     */
    @Data
    public static class PageConfig {
        /**
         * 默认页面大小
         */
        private int defaultSize = 20;

        /**
         * 最大页面大小
         */
        private int maxSize = 1000;

        /**
         * 是否默认需要总数统计
         */
        private boolean defaultNeedTotal = true;
    }

    /**
     * 查询配置类
     */
    @Data
    public static class QueryConfig {
        /**
         * 是否启用动态查询
         */
        private boolean enableDynamicQuery = true;

        /**
         * 是否忽略空值查询条件
         */
        private boolean ignoreEmptyConditions = true;

        /**
         * 最大查询条件数量
         */
        private int maxConditions = 50;

        /**
         * 默认排序字段
         */
        private String defaultSortField = "createTime";

        /**
         * 默认排序方向（ASC/DESC）
         */
        private String defaultSortDirection = "DESC";
    }

    /**
     * 缓存配置类
     */
    @Data
    public static class CacheConfig {
        /**
         * 是否启用缓存
         */
        private boolean enabled = false;

        /**
         * 缓存过期时间（秒）
         */
        private long expireTime = 3600;

        /**
         * 缓存最大数量
         */
        private int maxSize = 1000;
    }

    /**
     * 日志配置类
     */
    @Data
    public static class LogConfig {
        /**
         * 是否启用SQL日志
         */
        private boolean enableSqlLog = false;

        /**
         * 是否启用性能日志
         */
        private boolean enablePerformanceLog = true;

        /**
         * 慢查询阈值（毫秒）
         */
        private long slowQueryThreshold = 1000;

        /**
         * 是否记录查询参数
         */
        private boolean logQueryParams = true;
    }
}
