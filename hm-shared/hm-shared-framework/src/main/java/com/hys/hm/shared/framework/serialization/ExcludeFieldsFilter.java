package com.hys.hm.shared.framework.serialization;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.PropertyWriter;
import com.fasterxml.jackson.databind.ser.impl.SimpleBeanPropertyFilter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Set;

/**
 * 字段排除过滤器
 * 用于在JSON序列化时排除指定字段
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Slf4j
public class ExcludeFieldsFilter extends SimpleBeanPropertyFilter {

    private final Set<String> excludeFields;

    public ExcludeFieldsFilter(Set<String> excludeFields) {
        this.excludeFields = excludeFields;
    }

    public ExcludeFieldsFilter(List<String> excludeFields) {
        this.excludeFields = Set.copyOf(excludeFields);
    }

    @Override
    public void serializeAsField(Object pojo, JsonGenerator jgen, SerializerProvider provider, PropertyWriter writer) throws Exception {
        String fieldName = writer.getName();
        
        if (excludeFields.contains(fieldName)) {
            log.debug("排除字段序列化: {}", fieldName);
            return; // 跳过该字段的序列化
        }
        
        super.serializeAsField(pojo, jgen, provider, writer);
    }
}
