package com.hys.hm.shared.framework.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 事件配置属性
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-30
 */
@Data
@ConfigurationProperties(prefix = "hm.framework.event")
public class EventProperties {

    /**
     * 是否启用事件机制
     */
    private boolean enabled = true;

    /**
     * 是否启用事件统计
     */
    private boolean enableStats = true;

    /**
     * 是否启用事件日志
     */
    private boolean enableLogging = true;

    /**
     * 事件处理超时时间（毫秒）
     */
    private long timeoutMillis = 30000;

    /**
     * 异步事件配置
     */
    private Async async = new Async();

    /**
     * 重试配置
     */
    private Retry retry = new Retry();

    @Data
    public static class Async {
        /**
         * 核心线程数
         */
        private int corePoolSize = 2;

        /**
         * 最大线程数
         */
        private int maxPoolSize = 10;

        /**
         * 队列容量
         */
        private int queueCapacity = 100;

        /**
         * 线程空闲时间（秒）
         */
        private int keepAliveSeconds = 60;

        /**
         * 是否允许核心线程超时
         */
        private boolean allowCoreThreadTimeOut = false;
    }

    @Data
    public static class Retry {
        /**
         * 是否启用重试
         */
        private boolean enabled = false;

        /**
         * 最大重试次数
         */
        private int maxAttempts = 3;

        /**
         * 重试间隔（毫秒）
         */
        private long delayMillis = 1000;

        /**
         * 重试间隔倍数
         */
        private double multiplier = 2.0;

        /**
         * 最大重试间隔（毫秒）
         */
        private long maxDelayMillis = 10000;
    }
}
