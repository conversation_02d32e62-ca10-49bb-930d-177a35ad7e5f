package com.hys.hm.shared.framework.config;

import com.hys.hm.shared.framework.config.properties.EventProperties;
import com.hys.hm.shared.framework.service.EventStatsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ApplicationEventMulticaster;
import org.springframework.context.event.SimpleApplicationEventMulticaster;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 事件配置类
 * 配置领域事件相关的Bean和设置
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-30
 */
@Slf4j
@Configuration("frameworkEventConfiguration")
@EnableAsync
@EnableConfigurationProperties(EventProperties.class)
@ConditionalOnProperty(prefix = "hm.framework.event", name = "enabled", havingValue = "true", matchIfMissing = true)
public class EventConfiguration {

    /**
     * 异步事件处理线程池
     */
    @Bean("eventTaskExecutor")
    @ConditionalOnMissingBean(name = "eventTaskExecutor")
    public TaskExecutor eventTaskExecutor(EventProperties eventProperties) {
        log.info("初始化事件处理线程池");

        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 线程池配置
        executor.setCorePoolSize(eventProperties.getAsync().getCorePoolSize());
        executor.setMaxPoolSize(eventProperties.getAsync().getMaxPoolSize());
        executor.setQueueCapacity(eventProperties.getAsync().getQueueCapacity());
        executor.setKeepAliveSeconds(eventProperties.getAsync().getKeepAliveSeconds());

        // 线程名称前缀
        executor.setThreadNamePrefix("event-async-");

        // 拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        // 等待任务完成后关闭
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);

        executor.initialize();

        log.info("事件处理线程池初始化完成: corePoolSize={}, maxPoolSize={}, queueCapacity={}",
                eventProperties.getAsync().getCorePoolSize(),
                eventProperties.getAsync().getMaxPoolSize(),
                eventProperties.getAsync().getQueueCapacity());

        return executor;
    }

    /**
     * Framework应用事件多播器
     * 支持异步事件处理
     */
    @Bean("frameworkApplicationEventMulticaster")
    @ConditionalOnMissingBean(name = "frameworkApplicationEventMulticaster")
    public ApplicationEventMulticaster frameworkApplicationEventMulticaster(TaskExecutor eventTaskExecutor) {
        log.info("初始化Framework应用事件多播器");

        SimpleApplicationEventMulticaster multicaster = new SimpleApplicationEventMulticaster();
        multicaster.setTaskExecutor(eventTaskExecutor);

        log.info("Framework应用事件多播器初始化完成");
        return multicaster;
    }

    /**
     * 事件统计服务
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "hm.framework.event", name = "enable-stats", havingValue = "true", matchIfMissing = true)
    public EventStatsService eventStatsService() {
        log.info("初始化事件统计服务");
        return new EventStatsService();
    }
}
