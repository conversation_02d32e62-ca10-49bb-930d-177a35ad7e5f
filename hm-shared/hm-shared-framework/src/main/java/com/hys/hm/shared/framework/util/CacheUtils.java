package com.hys.hm.shared.framework.util;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Optional;
import java.util.concurrent.Callable;

/**
 * 缓存工具类
 * 提供便捷的缓存操作方法
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-30
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CacheUtils {

    private final CacheManager cacheManager;

    /**
     * 获取缓存
     */
    public Optional<Cache> getCache(String cacheName) {
        return Optional.ofNullable(cacheManager.getCache(cacheName));
    }

    /**
     * 从缓存中获取值
     */
    @SuppressWarnings("unchecked")
    public <T> Optional<T> get(String cacheName, Object key, Class<T> type) {
        return getCache(cacheName)
                .map(cache -> cache.get(key, type))
                .filter(value -> value != null);
    }

    /**
     * 从缓存中获取值，如果不存在则执行valueLoader并缓存结果
     */
    @SuppressWarnings("unchecked")
    public <T> T get(String cacheName, Object key, Callable<T> valueLoader) {
        return getCache(cacheName)
                .map(cache -> {
                    try {
                        return (T) cache.get(key, valueLoader);
                    } catch (Exception e) {
                        log.error("从缓存获取值时发生异常: cacheName={}, key={}", cacheName, key, e);
                        try {
                            return valueLoader.call();
                        } catch (Exception ex) {
                            log.error("执行valueLoader时发生异常: cacheName={}, key={}", cacheName, key, ex);
                            return null;
                        }
                    }
                })
                .orElseGet(() -> {
                    try {
                        return valueLoader.call();
                    } catch (Exception e) {
                        log.error("缓存不存在，执行valueLoader时发生异常: cacheName={}, key={}", cacheName, key, e);
                        return null;
                    }
                });
    }

    /**
     * 向缓存中放入值
     */
    public void put(String cacheName, Object key, Object value) {
        getCache(cacheName).ifPresent(cache -> {
            cache.put(key, value);
            log.debug("缓存值已更新: cacheName={}, key={}", cacheName, key);
        });
    }

    /**
     * 如果缓存中不存在该键，则放入值
     */
    @SuppressWarnings("unchecked")
    public <T> T putIfAbsent(String cacheName, Object key, T value) {
        return getCache(cacheName)
                .map(cache -> {
                    Cache.ValueWrapper existingValue = cache.putIfAbsent(key, value);
                    if (existingValue != null) {
                        return (T) existingValue.get();
                    } else {
                        log.debug("缓存值已添加: cacheName={}, key={}", cacheName, key);
                        return value;
                    }
                })
                .orElse(value);
    }

    /**
     * 从缓存中移除值
     */
    public void evict(String cacheName, Object key) {
        getCache(cacheName).ifPresent(cache -> {
            cache.evict(key);
            log.debug("缓存值已移除: cacheName={}, key={}", cacheName, key);
        });
    }

    /**
     * 清空指定缓存
     */
    public void clear(String cacheName) {
        getCache(cacheName).ifPresent(cache -> {
            cache.clear();
            log.debug("缓存已清空: cacheName={}", cacheName);
        });
    }

    /**
     * 清空所有缓存
     */
    public void clearAll() {
        Collection<String> cacheNames = cacheManager.getCacheNames();
        cacheNames.forEach(this::clear);
        log.info("所有缓存已清空: {}", cacheNames);
    }

    /**
     * 检查缓存中是否存在指定键
     */
    public boolean exists(String cacheName, Object key) {
        return getCache(cacheName)
                .map(cache -> cache.get(key) != null)
                .orElse(false);
    }

    /**
     * 生成实体缓存键
     */
    public static String entityKey(String entityName, Object id) {
        return String.format("%s::%s", entityName, id);
    }

    /**
     * 生成列表缓存键
     */
    public static String listKey(String entityName, String suffix) {
        return String.format("%s::list::%s", entityName, suffix);
    }

    /**
     * 生成分页缓存键
     */
    public static String pageKey(String entityName, int page, int size, String conditions) {
        return String.format("%s::page::%d::%d::%s", entityName, page, size,
                conditions != null ? conditions.hashCode() : "all");
    }

    /**
     * 生成统计缓存键
     */
    public static String countKey(String entityName, String conditions) {
        return String.format("%s::count::%s", entityName,
                conditions != null ? conditions.hashCode() : "all");
    }

    /**
     * 生成查询缓存键
     */
    public static String queryKey(String entityName, String queryHash) {
        return String.format("%s::query::%s", entityName, queryHash);
    }
}
