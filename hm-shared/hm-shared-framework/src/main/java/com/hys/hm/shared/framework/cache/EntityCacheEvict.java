package com.hys.hm.shared.framework.cache;

import org.springframework.cache.annotation.CacheEvict;
import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.*;

/**
 * 实体缓存清除注解
 * 专门用于实体操作后清除缓存的注解
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-30
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@CacheEvict(cacheNames = "entities")
public @interface EntityCacheEvict {

    /**
     * 缓存名称
     */
    @AliasFor(annotation = CacheEvict.class, attribute = "cacheNames")
    String[] value() default {"entities"};

    /**
     * 缓存键表达式
     */
    @AliasFor(annotation = CacheEvict.class, attribute = "key")
    String key() default "";

    /**
     * 缓存键生成器
     */
    @AliasFor(annotation = CacheEvict.class, attribute = "keyGenerator")
    String keyGenerator() default "";

    /**
     * 缓存条件
     */
    @AliasFor(annotation = CacheEvict.class, attribute = "condition")
    String condition() default "";

    /**
     * 是否清除所有缓存
     */
    @AliasFor(annotation = CacheEvict.class, attribute = "allEntries")
    boolean allEntries() default false;

    /**
     * 是否在方法执行前清除缓存
     */
    @AliasFor(annotation = CacheEvict.class, attribute = "beforeInvocation")
    boolean beforeInvocation() default false;
}
