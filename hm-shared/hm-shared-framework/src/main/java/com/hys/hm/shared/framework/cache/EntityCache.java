package com.hys.hm.shared.framework.cache;

import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.*;

/**
 * 实体缓存注解
 * 专门用于实体查询的缓存注解，提供默认配置
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-30
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Cacheable(cacheNames = "entities")
public @interface EntityCache {

    /**
     * 缓存名称
     */
    @AliasFor(annotation = Cacheable.class, attribute = "cacheNames")
    String[] value() default {"entities"};

    /**
     * 缓存键表达式
     */
    @AliasFor(annotation = Cacheable.class, attribute = "key")
    String key() default "";

    /**
     * 缓存键生成器
     */
    @AliasFor(annotation = Cacheable.class, attribute = "keyGenerator")
    String keyGenerator() default "";

    /**
     * 缓存条件
     */
    @AliasFor(annotation = Cacheable.class, attribute = "condition")
    String condition() default "";

    /**
     * 排除条件
     */
    @AliasFor(annotation = Cacheable.class, attribute = "unless")
    String unless() default "";

    /**
     * 是否同步执行
     */
    @AliasFor(annotation = Cacheable.class, attribute = "sync")
    boolean sync() default false;
}
