package com.hys.hm.shared.framework.service;

import com.hys.hm.shared.framework.event.DomainEvent;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 事件统计服务
 * 统计事件处理的相关指标
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-30
 */
@Slf4j
@Service
public class EventStatsService {

    /**
     * 事件统计数据
     */
    private final Map<String, EventStats> eventStatsMap = new ConcurrentHashMap<>();

    /**
     * 总事件计数器
     */
    private final AtomicLong totalEventCount = new AtomicLong(0);

    /**
     * 总错误计数器
     */
    private final AtomicLong totalErrorCount = new AtomicLong(0);

    /**
     * 服务启动时间
     */
    private final LocalDateTime startTime = LocalDateTime.now();

    /**
     * 监听所有领域事件
     */
    @EventListener
    public void handleDomainEvent(DomainEvent event) {
        recordEvent(event.getEventType());
    }

    /**
     * 记录事件
     */
    public void recordEvent(String eventType) {
        totalEventCount.incrementAndGet();

        eventStatsMap.computeIfAbsent(eventType, k -> new EventStats())
                .incrementCount();

        log.debug("记录事件: {}, 总计: {}", eventType, totalEventCount.get());
    }

    /**
     * 记录事件错误
     */
    public void recordEventError(String eventType, Exception error) {
        totalErrorCount.incrementAndGet();

        eventStatsMap.computeIfAbsent(eventType, k -> new EventStats())
                .incrementErrorCount();

        log.warn("记录事件错误: {}, 错误: {}", eventType, error.getMessage());
    }

    /**
     * 记录事件处理时间
     */
    public void recordEventProcessingTime(String eventType, long processingTimeMillis) {
        eventStatsMap.computeIfAbsent(eventType, k -> new EventStats())
                .recordProcessingTime(processingTimeMillis);

        log.debug("记录事件处理时间: {}, 耗时: {}ms", eventType, processingTimeMillis);
    }

    /**
     * 获取所有事件统计
     */
    public Map<String, EventStats> getAllEventStats() {
        return new ConcurrentHashMap<>(eventStatsMap);
    }

    /**
     * 获取特定事件统计
     */
    public EventStats getEventStats(String eventType) {
        return eventStatsMap.get(eventType);
    }

    /**
     * 获取总事件数量
     */
    public long getTotalEventCount() {
        return totalEventCount.get();
    }

    /**
     * 获取总错误数量
     */
    public long getTotalErrorCount() {
        return totalErrorCount.get();
    }

    /**
     * 获取错误率
     */
    public double getErrorRate() {
        long total = totalEventCount.get();
        if (total == 0) {
            return 0.0;
        }
        return (double) totalErrorCount.get() / total;
    }

    /**
     * 获取服务运行时间
     */
    public LocalDateTime getStartTime() {
        return startTime;
    }

    /**
     * 重置统计数据
     */
    public void resetStats() {
        eventStatsMap.clear();
        totalEventCount.set(0);
        totalErrorCount.set(0);
        log.info("事件统计数据已重置");
    }

    /**
     * 打印统计信息
     */
    public void printStats() {
        log.info("=== 事件统计信息 ===");
        log.info("服务启动时间: {}", startTime);
        log.info("总事件数量: {}", totalEventCount.get());
        log.info("总错误数量: {}", totalErrorCount.get());
        log.info("错误率: {:.2%}", getErrorRate());
        log.info("事件类型统计:");

        eventStatsMap.forEach((eventType, stats) -> {
            log.info("  {}: 数量={}, 错误={}, 平均耗时={}ms",
                    eventType, stats.getCount(), stats.getErrorCount(), stats.getAverageProcessingTime());
        });
    }

    /**
     * 事件统计数据类
     */
    @Data
    public static class EventStats {
        private final AtomicLong count = new AtomicLong(0);
        private final AtomicLong errorCount = new AtomicLong(0);
        private final AtomicLong totalProcessingTime = new AtomicLong(0);
        private final AtomicLong processingCount = new AtomicLong(0);

        public void incrementCount() {
            count.incrementAndGet();
        }

        public void incrementErrorCount() {
            errorCount.incrementAndGet();
        }

        public void recordProcessingTime(long timeMillis) {
            totalProcessingTime.addAndGet(timeMillis);
            processingCount.incrementAndGet();
        }

        public long getCount() {
            return count.get();
        }

        public long getErrorCount() {
            return errorCount.get();
        }

        public double getErrorRate() {
            long total = count.get();
            if (total == 0) {
                return 0.0;
            }
            return (double) errorCount.get() / total;
        }

        public double getAverageProcessingTime() {
            long processCount = processingCount.get();
            if (processCount == 0) {
                return 0.0;
            }
            return (double) totalProcessingTime.get() / processCount;
        }
    }
}
