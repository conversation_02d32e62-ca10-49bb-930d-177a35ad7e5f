package com.hys.hm.shared.framework.exception;

/**
 * 实体未找到异常
 * 当根据ID或条件查找实体时，如果实体不存在则抛出此异常
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
public class EntityNotFoundException extends FrameworkException {

    /**
     * 实体类型
     */
    private final Class<?> entityType;

    /**
     * 查找条件
     */
    private final Object searchCriteria;

    public EntityNotFoundException(String message) {
        super("ENTITY_NOT_FOUND", message);
        this.entityType = null;
        this.searchCriteria = null;
    }

    public EntityNotFoundException(Class<?> entityType, Object searchCriteria) {
        super("ENTITY_NOT_FOUND", 
              String.format("实体不存在: 类型=%s, 查找条件=%s", 
                          entityType.getSimpleName(), searchCriteria));
        this.entityType = entityType;
        this.searchCriteria = searchCriteria;
    }

    public EntityNotFoundException(Class<?> entityType, Object searchCriteria, String message) {
        super("ENTITY_NOT_FOUND", message);
        this.entityType = entityType;
        this.searchCriteria = searchCriteria;
    }

    public EntityNotFoundException(Class<?> entityType, Object searchCriteria, Throwable cause) {
        super("ENTITY_NOT_FOUND", 
              String.format("实体不存在: 类型=%s, 查找条件=%s", 
                          entityType.getSimpleName(), searchCriteria), 
              cause);
        this.entityType = entityType;
        this.searchCriteria = searchCriteria;
    }

    public Class<?> getEntityType() {
        return entityType;
    }

    public Object getSearchCriteria() {
        return searchCriteria;
    }

    /**
     * 创建根据ID查找失败的异常
     */
    public static EntityNotFoundException byId(Class<?> entityType, Object id) {
        return new EntityNotFoundException(entityType, "ID=" + id);
    }

    /**
     * 创建根据字段查找失败的异常
     */
    public static EntityNotFoundException byField(Class<?> entityType, String fieldName, Object value) {
        return new EntityNotFoundException(entityType, fieldName + "=" + value);
    }

    /**
     * 创建根据多个条件查找失败的异常
     */
    public static EntityNotFoundException byConditions(Class<?> entityType, String conditions) {
        return new EntityNotFoundException(entityType, conditions);
    }
}
