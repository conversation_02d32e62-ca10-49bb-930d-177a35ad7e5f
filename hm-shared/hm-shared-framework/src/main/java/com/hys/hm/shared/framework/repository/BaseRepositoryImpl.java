package com.hys.hm.shared.framework.repository;

import com.hys.hm.shared.framework.base.BaseEntity;
import com.hys.hm.shared.common.page.PageRequest;
import com.hys.hm.shared.common.page.PageResult;
import com.hys.hm.shared.common.query.QueryCondition;
import com.hys.hm.shared.common.query.QueryOperator;
import com.hys.hm.shared.encrypt.service.EncryptFieldQueryHelper;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import jakarta.persistence.criteria.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.support.JpaEntityInformation;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 基础仓储实现类
 * 提供通用的CRUD操作和动态查询功能的具体实现
 *
 * @param <T> 实体类型
 * @param <ID> 主键类型
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Slf4j
public class BaseRepositoryImpl<T, ID extends Serializable> extends SimpleJpaRepository<T, ID> implements BaseRepository<T, ID> {

    private final EntityManager entityManager;
    private final JpaEntityInformation<T, ID> entityInformation;

    @Autowired(required = false)
    private EncryptFieldQueryHelper encryptFieldQueryHelper;

    public BaseRepositoryImpl(JpaEntityInformation<T, ID> entityInformation, EntityManager entityManager) {
        super(entityInformation, entityManager);
        this.entityManager = entityManager;
        this.entityInformation = entityInformation;
    }

    @Override
    public List<T> findByConditions(List<QueryCondition> conditions) {
        if (conditions == null || conditions.isEmpty()) {
            return findAll();
        }

        Specification<T> spec = buildSpecification(conditions);
        return findAll(spec);
    }

    @Override
    public Page<T> findByConditions(List<QueryCondition> conditions, Pageable pageable) {
        if (conditions == null || conditions.isEmpty()) {
            return findAll(pageable);
        }

        Specification<T> spec = buildSpecification(conditions);
        return findAll(spec, pageable);
    }

    @Override
    public PageResult<T> findByPageRequest(PageRequest pageRequest) {
        if (pageRequest == null) {
            return PageResult.empty();
        }

        Pageable pageable = pageRequest.toPageable();

        if (pageRequest.isNeedTotal()) {
            // 需要总数的分页查询
            Page<T> page = findByConditions(pageRequest.getQueryConditions(), pageable);
            return PageResult.of(page);
        } else {
            // 不需要总数的分页查询（性能优化）
            List<T> content = findByConditions(pageRequest.getQueryConditions())
                    .stream()
                    .skip(pageRequest.getOffset())
                    .limit(pageRequest.getValidSize())
                    .collect(Collectors.toList());
            return PageResult.ofWithoutTotal(content, pageRequest.getValidPage(), pageRequest.getValidSize());
        }
    }

    @Override
    public long countByConditions(List<QueryCondition> conditions) {
        if (conditions == null || conditions.isEmpty()) {
            return count();
        }

        Specification<T> spec = buildSpecification(conditions);
        return count(spec);
    }

    @Override
    public boolean existsByConditions(List<QueryCondition> conditions) {
        return countByConditions(conditions) > 0;
    }

    @Override
    public Optional<T> findFirstByConditions(List<QueryCondition> conditions) {
        List<T> results = findByConditions(conditions);
        return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
    }

    @Override
    public List<T> findByField(String fieldName, Object value) {
        return findByConditions(List.of(QueryCondition.eq(fieldName, value)));
    }

    @Override
    public Optional<T> findFirstByField(String fieldName, Object value) {
        return findFirstByConditions(List.of(QueryCondition.eq(fieldName, value)));
    }

    @Override
    public List<T> findByFieldLike(String fieldName, Object value) {
        return findByConditions(List.of(QueryCondition.like(fieldName, value)));
    }

    @Override
    public List<T> findByFieldBetween(String fieldName, Object startValue, Object endValue) {
        return findByConditions(List.of(QueryCondition.between(fieldName, startValue, endValue)));
    }

    @Override
    public List<T> findByFieldIn(String fieldName, List<Object> values) {
        return findByConditions(List.of(QueryCondition.in(fieldName, values)));
    }

    @Override
    public List<T> findByFields(Map<String, Object> fieldValues) {
        List<QueryCondition> conditions = fieldValues.entrySet().stream()
                .map(entry -> QueryCondition.eq(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());
        return findByConditions(conditions);
    }

    @Override
    public boolean softDeleteById(ID id) {
        Optional<T> entityOpt = findById(id);
        if (entityOpt.isPresent()) {
            T entity = entityOpt.get();
            if (entity instanceof BaseEntity) {
                ((BaseEntity<?>) entity).markDeleted();
                save(entity);
                return true;
            } else {
                log.warn("实体 {} 不支持软删除", entity.getClass().getSimpleName());
            }
        }
        return false;
    }

    @Override
    public int softDeleteByIds(List<ID> ids) {
        int deletedCount = 0;
        for (ID id : ids) {
            if (softDeleteById(id)) {
                deletedCount++;
            }
        }
        return deletedCount;
    }

    @Override
    public boolean restoreById(ID id) {
        Optional<T> entityOpt = findById(id);
        if (entityOpt.isPresent()) {
            T entity = entityOpt.get();
            if (entity instanceof BaseEntity) {
                ((BaseEntity<?>) entity).markUndeleted();
                save(entity);
                return true;
            } else {
                log.warn("实体 {} 不支持软删除恢复", entity.getClass().getSimpleName());
            }
        }
        return false;
    }

    @Override
    public List<T> findAllNotDeleted() {
        return findByConditions(List.of(QueryCondition.eq("deleted", 0)));
    }

    @Override
    public Page<T> findAllNotDeleted(Pageable pageable) {
        return findByConditions(List.of(QueryCondition.eq("deleted", 0)), pageable);
    }

    @Override
    public Optional<T> findByIdNotDeleted(ID id) {
        return findFirstByConditions(List.of(
                QueryCondition.eq("id", id),
                QueryCondition.eq("deleted", 0)
        ));
    }

    @Override
    public long countNotDeleted() {
        return countByConditions(List.of(QueryCondition.eq("deleted", 0)));
    }

    @Override
    public List<T> updateAll(Iterable<T> entities) {
        List<T> result = new ArrayList<>();
        for (T entity : entities) {
            result.add(save(entity));
        }
        return result;
    }

    @Override
    public int updateFieldByConditions(List<QueryCondition> conditions, String fieldName, Object newValue) {
        return updateFieldsByConditions(conditions, Map.of(fieldName, newValue));
    }

    @Override
    public int updateFieldsByConditions(List<QueryCondition> conditions, Map<String, Object> fieldValues) {
        try {
            CriteriaBuilder cb = entityManager.getCriteriaBuilder();
            CriteriaUpdate<T> update = cb.createCriteriaUpdate(entityInformation.getJavaType());
            Root<T> root = update.from(entityInformation.getJavaType());

            // 设置更新字段
            for (Map.Entry<String, Object> entry : fieldValues.entrySet()) {
                update.set(root.get(entry.getKey()), entry.getValue());
            }

            // 添加查询条件
            if (conditions != null && !conditions.isEmpty()) {
                Predicate predicate = buildPredicate(conditions, cb, root);
                update.where(predicate);
            }

            return entityManager.createQuery(update).executeUpdate();
        } catch (Exception e) {
            log.error("批量更新字段失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public List<Object[]> executeNativeQuery(String sql, Object... parameters) {
        try {
            Query query = entityManager.createNativeQuery(sql);
            for (int i = 0; i < parameters.length; i++) {
                query.setParameter(i + 1, parameters[i]);
            }
            return query.getResultList();
        } catch (Exception e) {
            log.error("执行原生查询失败: SQL={}, 错误={}", sql, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public int executeNativeUpdate(String sql, Object... parameters) {
        try {
            Query query = entityManager.createNativeQuery(sql);
            for (int i = 0; i < parameters.length; i++) {
                query.setParameter(i + 1, parameters[i]);
            }
            return query.executeUpdate();
        } catch (Exception e) {
            log.error("执行原生更新失败: SQL={}, 错误={}", sql, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public void flushAndClear() {
        flush();
        entityManager.clear();
    }

    @Override
    public void clear() {
        entityManager.clear();
    }



    /**
     * 构建JPA Specification
     */
    private Specification<T> buildSpecification(List<QueryCondition> conditions) {
        return (root, query, criteriaBuilder) -> buildPredicate(conditions, criteriaBuilder, root);
    }



    /**
     * 构建JPA Predicate
     */
    private Predicate buildPredicate(List<QueryCondition> conditions, CriteriaBuilder cb, Root<T> root) {
        List<Predicate> predicates = new ArrayList<>();

        for (QueryCondition condition : conditions) {
            if (!condition.isValid()) {
                continue;
            }

            try {
                Predicate predicate = buildSinglePredicate(condition, cb, root);
                if (predicate != null) {
                    predicates.add(predicate);
                }
            } catch (Exception e) {
                log.warn("构建查询条件失败: 字段={}, 操作符={}, 值={}, 错误={}",
                        condition.getFieldName(), condition.getOperator(), condition.getValue(), e.getMessage());
            }
        }

        return predicates.isEmpty() ? cb.conjunction() : cb.and(predicates.toArray(new Predicate[0]));
    }

    /**
     * 构建单个查询条件的Predicate
     */
    @SuppressWarnings("unchecked")
    private Predicate buildSinglePredicate(QueryCondition condition, CriteriaBuilder cb, Root<T> root) {
        String fieldName = condition.getFieldName();
        QueryOperator operator = condition.getOperator();

        // 检查是否为加密字段，如果是则使用加密索引查询
        log.debug("检查加密字段: fieldName={}, encryptFieldQueryHelper={}", fieldName, encryptFieldQueryHelper != null);

        if (encryptFieldQueryHelper != null && encryptFieldQueryHelper.isEncryptServiceAvailable()) {
            @SuppressWarnings("unchecked")
            Class<T> entityClass = (Class<T>) root.getJavaType();

            log.debug("加密服务可用，检查字段是否为加密字段: entityClass={}, fieldName={}", entityClass.getSimpleName(), fieldName);

            if (encryptFieldQueryHelper.isEncryptField(entityClass, fieldName)) {
                log.info("检测到加密字段，使用加密查询: fieldName={}, operator={}, value={}", fieldName, operator, condition.getValue());
                return buildEncryptedFieldPredicate(condition, cb, root, entityClass, fieldName, operator);
            } else {
                log.debug("字段不是加密字段: fieldName={}", fieldName);
            }
        } else {
            log.debug("加密服务不可用或EncryptFieldQueryHelper为null");
        }

        // 获取字段路径（支持嵌套字段，如 user.name）
        Path<Object> fieldPath = getFieldPath(root, fieldName);

        switch (operator) {
            case EQ:
                return cb.equal(fieldPath, condition.getValue());

            case NE:
                return cb.notEqual(fieldPath, condition.getValue());

            case LIKE:
                if (condition.isIgnoreCase()) {
                    return cb.like(cb.lower(fieldPath.as(String.class)),
                            condition.getActualValue().toString().toLowerCase());
                } else {
                    return cb.like(fieldPath.as(String.class), condition.getActualValue().toString());
                }

            case GT:
                return cb.greaterThan(fieldPath.as(Comparable.class), (Comparable) condition.getValue());

            case GTE:
                return cb.greaterThanOrEqualTo(fieldPath.as(Comparable.class), (Comparable) condition.getValue());

            case LT:
                return cb.lessThan(fieldPath.as(Comparable.class), (Comparable) condition.getValue());

            case LTE:
                return cb.lessThanOrEqualTo(fieldPath.as(Comparable.class), (Comparable) condition.getValue());

            case IN:
                return fieldPath.in(condition.getValues());

            case NOT_IN:
                return cb.not(fieldPath.in(condition.getValues()));

            case IS_NULL:
                return cb.isNull(fieldPath);

            case IS_NOT_NULL:
                return cb.isNotNull(fieldPath);

            case BETWEEN:
                if (condition.getValues() != null && !condition.getValues().isEmpty()) {
                    return cb.between(fieldPath.as(Comparable.class),
                            (Comparable) condition.getValue(),
                            (Comparable) condition.getValues().get(0));
                }
                break;

            default:
                log.warn("不支持的查询操作符: {}", operator);
        }

        return null;
    }

    /**
     * 构建加密字段查询条件的Predicate
     */
    private Predicate buildEncryptedFieldPredicate(QueryCondition condition, CriteriaBuilder cb, Root<T> root,
                                                   Class<T> entityClass, String fieldName, QueryOperator operator) {
        try {
            Object value = condition.getValue();
            if (value == null) {
                log.debug("查询值为null，跳过加密查询: fieldName={}", fieldName);
                return null;
            }

            String plainTextValue = value.toString();
            log.info("开始构建加密字段查询: fieldName={}, operator={}, plainTextValue={}", fieldName, operator, plainTextValue);

            List<String> encryptedEntityIds = new ArrayList<>();

            switch (operator) {
                case EQ:
                    // 精确查询：使用明文参数查询加密索引
                    log.debug("执行加密字段精确查询");
                    encryptedEntityIds = encryptFieldQueryHelper.findEntityIdsByEncryptedField(entityClass, fieldName, plainTextValue);
                    break;

                case LIKE:
                    // 模糊查询：使用明文参数查询加密索引
                    log.debug("执行加密字段模糊查询");
                    encryptedEntityIds = encryptFieldQueryHelper.findEntityIdsByEncryptedFieldLike(entityClass, fieldName, plainTextValue);
                    break;

                default:
                    // 其他操作符暂不支持加密字段查询，降级为普通查询
                    log.debug("加密字段不支持操作符 {}, 降级为普通查询: field={}", operator, fieldName);
                    return null;
            }

            log.info("加密索引查询结果: fieldName={}, 找到实体ID数量={}", fieldName, encryptedEntityIds.size());

            // 构建混合查询条件：既查询加密数据又查询历史明文数据
            List<Predicate> predicates = new ArrayList<>();

            // 1. 如果有加密索引匹配的记录，添加ID IN查询
            if (!encryptedEntityIds.isEmpty()) {
                Path<ID> idPath = root.get("id");
                @SuppressWarnings("unchecked")
                List<ID> typedEntityIds = (List<ID>) (List<?>) encryptedEntityIds;
                predicates.add(idPath.in(typedEntityIds));
                log.debug("加密字段查询找到 {} 条匹配记录", encryptedEntityIds.size());
            }

            // 2. 同时添加对历史明文数据的查询
            Path<Object> fieldPath = getFieldPath(root, fieldName);
            Predicate plaintextPredicate = null;

            switch (operator) {
                case EQ:
                    plaintextPredicate = cb.equal(fieldPath, plainTextValue);
                    break;
                case LIKE:
                    String likeValue = plainTextValue;
                    if (!likeValue.contains("%")) {
                        likeValue = "%" + likeValue + "%";
                    }
                    if (condition.isIgnoreCase()) {
                        plaintextPredicate = cb.like(cb.lower(fieldPath.as(String.class)), likeValue.toLowerCase());
                    } else {
                        plaintextPredicate = cb.like(fieldPath.as(String.class), likeValue);
                    }
                    break;
            }

            if (plaintextPredicate != null) {
                predicates.add(plaintextPredicate);
                log.debug("添加历史明文数据查询条件");
            }

            // 3. 使用OR连接加密查询和明文查询
            if (predicates.isEmpty()) {
                return cb.disjunction(); // 没有任何匹配条件
            } else if (predicates.size() == 1) {
                return predicates.get(0);
            } else {
                return cb.or(predicates.toArray(new Predicate[0]));
            }

        } catch (Exception e) {
            log.error("构建加密字段查询条件失败: field={}, operator={}, error={}",
                     fieldName, operator, e.getMessage());
            // 发生异常时降级为普通查询
            return null;
        }
    }

    /**
     * 获取字段路径（支持嵌套字段）
     */
    private Path<Object> getFieldPath(Root<T> root, String fieldName) {
        if (!fieldName.contains(".")) {
            return root.get(fieldName);
        }

        String[] fieldParts = fieldName.split("\\.");
        Path<Object> path = root.get(fieldParts[0]);

        for (int i = 1; i < fieldParts.length; i++) {
            path = path.get(fieldParts[i]);
        }

        return path;
    }

    /**
     * 查找类中的字段（包括父类）
     */
    private java.lang.reflect.Field findField(Class<?> clazz, String fieldName) {
        Class<?> currentClass = clazz;
        while (currentClass != null) {
            try {
                return currentClass.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                currentClass = currentClass.getSuperclass();
            }
        }
        return null;
    }
}
