package com.hys.hm.shared.framework.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * Spring工具类
 * 用于在非Spring管理的类中获取Spring Bean
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Component
@Slf4j
public class SpringUtils implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SpringUtils.applicationContext = applicationContext;
        log.debug("SpringUtils初始化完成，ApplicationContext已设置");
    }

    /**
     * 获取ApplicationContext
     */
    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    /**
     * 通过name获取Bean
     */
    public static Object getBean(String name) {
        try {
            return getApplicationContext().getBean(name);
        } catch (Exception e) {
            log.debug("获取Bean失败: name={}, error={}", name, e.getMessage());
            return null;
        }
    }

    /**
     * 通过class获取Bean
     */
    public static <T> T getBean(Class<T> clazz) {
        try {
            return getApplicationContext().getBean(clazz);
        } catch (Exception e) {
            log.debug("获取Bean失败: class={}, error={}", clazz.getSimpleName(), e.getMessage());
            return null;
        }
    }

    /**
     * 通过name和class获取Bean
     */
    public static <T> T getBean(String name, Class<T> clazz) {
        try {
            return getApplicationContext().getBean(name, clazz);
        } catch (Exception e) {
            log.debug("获取Bean失败: name={}, class={}, error={}", name, clazz.getSimpleName(), e.getMessage());
            return null;
        }
    }

    /**
     * 检查是否包含指定的Bean
     */
    public static boolean containsBean(String name) {
        try {
            return getApplicationContext().containsBean(name);
        } catch (Exception e) {
            log.debug("检查Bean存在性失败: name={}, error={}", name, e.getMessage());
            return false;
        }
    }

    /**
     * 检查ApplicationContext是否已初始化
     */
    public static boolean isInitialized() {
        return applicationContext != null;
    }

    /**
     * 获取指定类型的所有Bean名称
     */
    public static String[] getBeanNamesForType(Class<?> type) {
        try {
            return getApplicationContext().getBeanNamesForType(type);
        } catch (Exception e) {
            log.debug("获取Bean名称列表失败: type={}, error={}", type.getSimpleName(), e.getMessage());
            return new String[0];
        }
    }

    /**
     * 安全获取Bean，如果获取失败返回null而不抛异常
     */
    public static <T> T getBeanSafely(Class<T> clazz) {
        try {
            if (!isInitialized()) {
                log.debug("SpringUtils未初始化，无法获取Bean: {}", clazz.getSimpleName());
                return null;
            }
            return getApplicationContext().getBean(clazz);
        } catch (Exception e) {
            log.debug("安全获取Bean失败: class={}, error={}", clazz.getSimpleName(), e.getMessage());
            return null;
        }
    }

    /**
     * 获取Bean并记录详细日志
     */
    public static <T> T getBeanWithLog(Class<T> clazz) {
        log.debug("尝试获取Bean: class={}", clazz.getSimpleName());
        
        if (!isInitialized()) {
            log.warn("SpringUtils未初始化，无法获取Bean: {}", clazz.getSimpleName());
            return null;
        }

        try {
            T bean = getApplicationContext().getBean(clazz);
            log.debug("成功获取Bean: class={}, bean={}", clazz.getSimpleName(), bean != null);
            return bean;
        } catch (Exception e) {
            log.warn("获取Bean失败: class={}, error={}", clazz.getSimpleName(), e.getMessage());
            return null;
        }
    }
}
