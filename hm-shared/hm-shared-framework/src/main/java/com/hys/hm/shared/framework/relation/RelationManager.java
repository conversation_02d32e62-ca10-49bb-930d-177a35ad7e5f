package com.hys.hm.shared.framework.relation;

import com.hys.hm.shared.common.relation.EntityRelation;
import com.hys.hm.shared.common.relation.RelationLoader;
import com.hys.hm.shared.common.relation.RelationProvider;
import com.hys.hm.shared.framework.util.ReflectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 关联关系管理器
 * 负责管理和处理实体间的关联关系
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Component
@Slf4j
public class RelationManager implements ApplicationContextAware, InitializingBean {

    private ApplicationContext applicationContext;
    
    /**
     * 关联数据提供者注册表
     * Key: entityType, Value: 提供者Bean名称列表
     */
    private final Map<String, List<String>> providerRegistry = new ConcurrentHashMap<>();
    
    /**
     * 关联数据加载器注册表
     * Key: entityType, Value: 加载器方法信息
     */
    private final Map<String, List<LoaderInfo>> loaderRegistry = new ConcurrentHashMap<>();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        scanAndRegisterProviders();
        scanAndRegisterLoaders();
        log.info("关联关系管理器初始化完成，注册提供者: {}, 加载器: {}", 
            providerRegistry.size(), loaderRegistry.size());
    }

    /**
     * 扫描并注册关联数据提供者
     */
    private void scanAndRegisterProviders() {
        Map<String, Object> providers = applicationContext.getBeansWithAnnotation(RelationProvider.class);
        
        for (Map.Entry<String, Object> entry : providers.entrySet()) {
            String beanName = entry.getKey();
            Object bean = entry.getValue();
            RelationProvider annotation = bean.getClass().getAnnotation(RelationProvider.class);
            
            for (String entityType : annotation.entityTypes()) {
                providerRegistry.computeIfAbsent(entityType, k -> new ArrayList<>()).add(beanName);
                log.debug("注册关联数据提供者: entityType={}, provider={}", entityType, beanName);
            }
        }
    }

    /**
     * 扫描并注册关联数据加载器
     */
    private void scanAndRegisterLoaders() {
        Map<String, Object> beans = applicationContext.getBeansOfType(Object.class);
        
        for (Map.Entry<String, Object> entry : beans.entrySet()) {
            String beanName = entry.getKey();
            Object bean = entry.getValue();
            
            Method[] methods = ReflectionUtils.getAllDeclaredMethods(bean.getClass());
            for (Method method : methods) {
                RelationLoader annotation = method.getAnnotation(RelationLoader.class);
                if (annotation != null) {
                    LoaderInfo loaderInfo = new LoaderInfo(beanName, method, annotation);
                    
                    for (String entityType : annotation.entityTypes()) {
                        loaderRegistry.computeIfAbsent(entityType, k -> new ArrayList<>()).add(loaderInfo);
                        log.debug("注册关联数据加载器: entityType={}, loader={}#{}", 
                            entityType, beanName, method.getName());
                    }
                }
            }
        }
        
        // 按优先级排序
        loaderRegistry.values().forEach(loaders -> 
            loaders.sort(Comparator.comparingInt(l -> l.annotation.priority())));
    }

    /**
     * 处理对象的关联关系
     */
    public void processRelations(Object obj) {
        if (obj == null) {
            return;
        }

        Class<?> clazz = obj.getClass();
        Field[] fields = ReflectionUtils.getAllDeclaredFields(clazz);
        
        for (Field field : fields) {
            EntityRelation annotation = field.getAnnotation(EntityRelation.class);
            if (annotation != null) {
                processFieldRelation(obj, field, annotation);
            }
        }
    }

    /**
     * 批量处理对象列表的关联关系
     */
    public void processRelations(List<?> objects) {
        if (objects == null || objects.isEmpty()) {
            return;
        }

        // 按类型分组
        Map<Class<?>, List<Object>> groupedObjects = new HashMap<>();
        for (Object obj : objects) {
            if (obj != null) {
                groupedObjects.computeIfAbsent(obj.getClass(), k -> new ArrayList<>()).add(obj);
            }
        }

        // 批量处理每种类型
        for (Map.Entry<Class<?>, List<Object>> entry : groupedObjects.entrySet()) {
            processBatchRelations(entry.getValue());
        }
    }

    /**
     * 处理单个字段的关联关系
     */
    private void processFieldRelation(Object obj, Field field, EntityRelation annotation) {
        try {
            field.setAccessible(true);
            Object fieldValue = field.get(obj);
            
            if (fieldValue == null) {
                return;
            }

            String entityType = annotation.entityType();
            List<LoaderInfo> loaders = loaderRegistry.get(entityType);
            
            if (loaders == null || loaders.isEmpty()) {
                log.warn("未找到实体类型 {} 的关联数据加载器", entityType);
                return;
            }

            // 使用第一个可用的加载器
            LoaderInfo loader = loaders.get(0);
            Object relationData = invokeLoader(loader, fieldValue);
            
            if (relationData != null) {
                // 设置关联数据到对象中
                setRelationData(obj, field, relationData, annotation);
            }

        } catch (Exception e) {
            log.error("处理字段关联关系失败: field={}, entityType={}", 
                field.getName(), annotation.entityType(), e);
        }
    }

    /**
     * 批量处理关联关系
     */
    private void processBatchRelations(List<Object> objects) {
        if (objects.isEmpty()) {
            return;
        }

        Object firstObj = objects.get(0);
        Class<?> clazz = firstObj.getClass();
        Field[] fields = ReflectionUtils.getAllDeclaredFields(clazz);
        
        for (Field field : fields) {
            EntityRelation annotation = field.getAnnotation(EntityRelation.class);
            if (annotation != null) {
                processBatchFieldRelation(objects, field, annotation);
            }
        }
    }

    /**
     * 批量处理字段关联关系
     */
    private void processBatchFieldRelation(List<Object> objects, Field field, EntityRelation annotation) {
        try {
            field.setAccessible(true);
            
            // 收集所有需要加载的关联ID
            Set<Object> relationIds = new HashSet<>();
            for (Object obj : objects) {
                Object fieldValue = field.get(obj);
                if (fieldValue != null) {
                    relationIds.add(fieldValue);
                }
            }

            if (relationIds.isEmpty()) {
                return;
            }

            String entityType = annotation.entityType();
            List<LoaderInfo> loaders = loaderRegistry.get(entityType);
            
            if (loaders == null || loaders.isEmpty()) {
                return;
            }

            // 查找支持批量加载的加载器
            LoaderInfo batchLoader = loaders.stream()
                .filter(l -> l.annotation.supportBatch())
                .findFirst()
                .orElse(loaders.get(0));

            Map<Object, Object> relationDataMap;
            if (batchLoader.annotation.supportBatch()) {
                relationDataMap = invokeBatchLoader(batchLoader, new ArrayList<>(relationIds));
            } else {
                // 逐个加载
                relationDataMap = new HashMap<>();
                for (Object relationId : relationIds) {
                    Object relationData = invokeLoader(batchLoader, relationId);
                    if (relationData != null) {
                        relationDataMap.put(relationId, relationData);
                    }
                }
            }

            // 设置关联数据
            for (Object obj : objects) {
                Object fieldValue = field.get(obj);
                if (fieldValue != null) {
                    Object relationData = relationDataMap.get(fieldValue);
                    if (relationData != null) {
                        setRelationData(obj, field, relationData, annotation);
                    }
                }
            }

        } catch (Exception e) {
            log.error("批量处理字段关联关系失败: field={}, entityType={}", 
                field.getName(), annotation.entityType(), e);
        }
    }

    /**
     * 调用加载器方法
     */
    private Object invokeLoader(LoaderInfo loader, Object relationId) {
        try {
            Object bean = applicationContext.getBean(loader.beanName);
            return ReflectionUtils.invokeMethod(loader.method, bean, relationId);
        } catch (Exception e) {
            log.error("调用关联数据加载器失败: loader={}#{}, relationId={}", 
                loader.beanName, loader.method.getName(), relationId, e);
            return null;
        }
    }

    /**
     * 调用批量加载器方法
     */
    @SuppressWarnings("unchecked")
    private Map<Object, Object> invokeBatchLoader(LoaderInfo loader, List<Object> relationIds) {
        try {
            Object bean = applicationContext.getBean(loader.beanName);
            Object result = ReflectionUtils.invokeMethod(loader.method, bean, relationIds);
            
            if (result instanceof Map) {
                return (Map<Object, Object>) result;
            } else {
                log.warn("批量加载器返回结果不是Map类型: loader={}#{}", 
                    loader.beanName, loader.method.getName());
                return new HashMap<>();
            }
        } catch (Exception e) {
            log.error("调用批量关联数据加载器失败: loader={}#{}", 
                loader.beanName, loader.method.getName(), e);
            return new HashMap<>();
        }
    }

    /**
     * 设置关联数据到对象中
     */
    private void setRelationData(Object obj, Field field, Object relationData, EntityRelation annotation) {
        try {
            // 这里可以根据需要设置到不同的字段
            // 例如：如果字段名是 patientId，可以设置到 patient 字段
            String fieldName = field.getName();
            String relationFieldName = fieldName.endsWith("Id") ? 
                fieldName.substring(0, fieldName.length() - 2) : fieldName + "Data";
            
            Field relationField = ReflectionUtils.findField(obj.getClass(), relationFieldName);
            if (relationField != null) {
                relationField.setAccessible(true);
                ReflectionUtils.setField(relationField, obj, relationData);
            } else {
                // 如果没有对应的字段，可以考虑使用Map存储
                log.debug("未找到关联数据字段: {}, 跳过设置", relationFieldName);
            }
        } catch (Exception e) {
            log.error("设置关联数据失败: field={}, relationData={}", field.getName(), relationData, e);
        }
    }

    /**
     * 加载器信息
     */
    private static class LoaderInfo {
        final String beanName;
        final Method method;
        final RelationLoader annotation;

        LoaderInfo(String beanName, Method method, RelationLoader annotation) {
            this.beanName = beanName;
            this.method = method;
            this.annotation = annotation;
        }
    }
}
