package com.hys.hm.shared.framework.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * 关联关系自动配置类
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Configuration
@EnableAspectJAutoProxy
@ComponentScan(basePackages = "com.hys.hm.shared.framework.relation")
@ConditionalOnProperty(
    prefix = "hm.relation", 
    name = "enabled", 
    havingValue = "true", 
    matchIfMissing = true
)
public class RelationAutoConfiguration {
    // 自动配置关联关系处理组件
}
