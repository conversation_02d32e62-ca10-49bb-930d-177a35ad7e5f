package com.hys.hm.shared.framework.event.example;

import com.hys.hm.shared.framework.event.DomainEventListener;
import com.hys.hm.shared.framework.event.EntityCreatedEvent;
import com.hys.hm.shared.framework.event.EntityDeletedEvent;
import com.hys.hm.shared.framework.event.EntityUpdatedEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 用户事件监听器示例
 * 演示如何监听和处理实体事件
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-30
 */
@Slf4j
@Component
public class UserEventListener extends DomainEventListener {

    @Override
    protected void onEntityCreated(EntityCreatedEvent<?, ?> event) {
        if ("User".equals(event.getAggregateType())) {
            log.info("用户创建事件 - 同步处理: 用户ID={}, 操作者={}", 
                    event.getAggregateId(), event.getOperatorId());
            
            // 同步处理逻辑，例如：
            // 1. 数据验证
            // 2. 发送欢迎邮件
            // 3. 创建用户配置文件
            handleUserCreatedSync(event);
        }
    }

    @Override
    protected void onEntityUpdated(EntityUpdatedEvent<?, ?> event) {
        if ("User".equals(event.getAggregateType())) {
            log.info("用户更新事件 - 同步处理: 用户ID={}, 操作者={}", 
                    event.getAggregateId(), event.getOperatorId());
            
            // 同步处理逻辑，例如：
            // 1. 更新缓存
            // 2. 验证数据一致性
            // 3. 记录审计日志
            handleUserUpdatedSync(event);
        }
    }

    @Override
    protected void onEntityDeleted(EntityDeletedEvent<?, ?> event) {
        if ("User".equals(event.getAggregateType())) {
            log.info("用户删除事件 - 同步处理: 用户ID={}, 软删除={}, 操作者={}", 
                    event.getAggregateId(), event.isSoftDelete(), event.getOperatorId());
            
            // 同步处理逻辑，例如：
            // 1. 清理缓存
            // 2. 记录删除日志
            // 3. 数据一致性检查
            handleUserDeletedSync(event);
        }
    }

    @Override
    protected void onEntityCreatedAsync(EntityCreatedEvent<?, ?> event) {
        if ("User".equals(event.getAggregateType())) {
            log.info("用户创建事件 - 异步处理: 用户ID={}, 操作者={}", 
                    event.getAggregateId(), event.getOperatorId());
            
            // 异步处理逻辑，例如：
            // 1. 发送通知
            // 2. 数据统计
            // 3. 第三方系统同步
            handleUserCreatedAsync(event);
        }
    }

    @Override
    protected void onEntityUpdatedAsync(EntityUpdatedEvent<?, ?> event) {
        if ("User".equals(event.getAggregateType())) {
            log.info("用户更新事件 - 异步处理: 用户ID={}, 操作者={}", 
                    event.getAggregateId(), event.getOperatorId());
            
            // 异步处理逻辑，例如：
            // 1. 更新搜索索引
            // 2. 同步到数据仓库
            // 3. 发送变更通知
            handleUserUpdatedAsync(event);
        }
    }

    @Override
    protected void onEntityDeletedAsync(EntityDeletedEvent<?, ?> event) {
        if ("User".equals(event.getAggregateType())) {
            log.info("用户删除事件 - 异步处理: 用户ID={}, 软删除={}, 操作者={}", 
                    event.getAggregateId(), event.isSoftDelete(), event.getOperatorId());
            
            // 异步处理逻辑，例如：
            // 1. 清理相关数据
            // 2. 发送删除通知
            // 3. 数据归档
            handleUserDeletedAsync(event);
        }
    }

    // 具体的业务处理方法

    private void handleUserCreatedSync(EntityCreatedEvent<?, ?> event) {
        // 同步创建处理逻辑
        log.debug("执行用户创建同步处理逻辑");
    }

    private void handleUserUpdatedSync(EntityUpdatedEvent<?, ?> event) {
        // 同步更新处理逻辑
        log.debug("执行用户更新同步处理逻辑");
    }

    private void handleUserDeletedSync(EntityDeletedEvent<?, ?> event) {
        // 同步删除处理逻辑
        log.debug("执行用户删除同步处理逻辑");
    }

    private void handleUserCreatedAsync(EntityCreatedEvent<?, ?> event) {
        // 异步创建处理逻辑
        log.debug("执行用户创建异步处理逻辑");
        
        // 模拟耗时操作
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    private void handleUserUpdatedAsync(EntityUpdatedEvent<?, ?> event) {
        // 异步更新处理逻辑
        log.debug("执行用户更新异步处理逻辑");
        
        // 模拟耗时操作
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    private void handleUserDeletedAsync(EntityDeletedEvent<?, ?> event) {
        // 异步删除处理逻辑
        log.debug("执行用户删除异步处理逻辑");
        
        // 模拟耗时操作
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
