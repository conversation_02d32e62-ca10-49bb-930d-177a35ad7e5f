package com.hys.hm.shared.framework.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.hys.hm.shared.framework.config.properties.CacheProperties;
import com.hys.hm.shared.framework.service.CacheStatsService;
import com.hys.hm.shared.framework.util.CacheUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 缓存配置类
 * 支持本地缓存（Caffeine）和分布式缓存（Redis）
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-30
 */
@Slf4j
@Configuration
@EnableCaching
@EnableConfigurationProperties(CacheProperties.class)
public class CacheConfiguration {

    /**
     * Caffeine 本地缓存管理器（默认）
     */
    @Bean
    @Primary
    @ConditionalOnProperty(prefix = "hm.framework.cache", name = "type", havingValue = "caffeine", matchIfMissing = true)
    public CacheManager caffeineCacheManager(CacheProperties cacheProperties) {
        log.info("初始化 Caffeine 缓存管理器");

        CaffeineCacheManager cacheManager = new CaffeineCacheManager();

        // 配置 Caffeine 缓存
        Caffeine<Object, Object> caffeineBuilder = Caffeine.newBuilder()
                .maximumSize(cacheProperties.getCaffeine().getMaximumSize())
                .expireAfterWrite(Duration.ofMinutes(cacheProperties.getCaffeine().getExpireAfterWriteMinutes()))
                .expireAfterAccess(Duration.ofMinutes(cacheProperties.getCaffeine().getExpireAfterAccessMinutes()))
                .recordStats(); // 启用统计信息

        cacheManager.setCaffeine(caffeineBuilder);

        // 设置缓存名称
        cacheManager.setCacheNames(cacheProperties.getCacheNames());

        log.info("Caffeine 缓存管理器初始化完成，缓存名称: {}", cacheProperties.getCacheNames());
        return cacheManager;
    }

    /**
     * Redis 分布式缓存管理器
     */
    @Bean
    @ConditionalOnClass(RedisConnectionFactory.class)
    @ConditionalOnProperty(prefix = "hm.framework.cache", name = "type", havingValue = "redis")
    public CacheManager redisCacheManager(RedisConnectionFactory redisConnectionFactory,
                                        CacheProperties cacheProperties) {
        log.info("初始化 Redis 缓存管理器");


        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        objectMapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);
        objectMapper.registerModule(new JavaTimeModule());
        // 配置 Jackson 序列化器
        Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(objectMapper,Object.class);

        // 配置序列化方式
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(cacheProperties.getRedis().getDefaultTtlMinutes()))
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(jackson2JsonRedisSerializer))
                .disableCachingNullValues();

        // 配置不同缓存的TTL
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        cacheProperties.getRedis().getTtlConfigs().forEach((cacheName, ttlMinutes) -> {
            cacheConfigurations.put(cacheName, config.entryTtl(Duration.ofMinutes(ttlMinutes)));
        });

        RedisCacheManager cacheManager = RedisCacheManager.builder(redisConnectionFactory)
                .cacheDefaults(config)
                .withInitialCacheConfigurations(cacheConfigurations)
                .build();

        log.info("Redis 缓存管理器初始化完成，默认TTL: {} 分钟", cacheProperties.getRedis().getDefaultTtlMinutes());
        return cacheManager;
    }

    /**
     * 缓存统计信息Bean
     */
    @Bean
    @ConditionalOnMissingBean
    public CacheStatsService cacheStatsService(CacheManager cacheManager) {
        return new CacheStatsService(cacheManager);
    }

    /**
     * 缓存工具类Bean
     */
    @Bean
    @ConditionalOnMissingBean
    public CacheUtils cacheUtils(CacheManager cacheManager) {
        return new CacheUtils(cacheManager);
    }
}
