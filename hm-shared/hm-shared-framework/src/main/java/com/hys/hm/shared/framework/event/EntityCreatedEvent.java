package com.hys.hm.shared.framework.event;

import lombok.Getter;

/**
 * 实体创建事件
 * 当实体被创建时发布此事件
 *
 * @param <T> 实体类型
 * @param <ID> 实体ID类型
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-30
 */
@Getter
public class EntityCreatedEvent<T, ID> extends EntityEvent<T, ID> {

    /**
     * 构造函数
     */
    public EntityCreatedEvent(T entity, ID entityId, Class<T> entityType) {
        super(entity, entityId, entityType);
    }

    /**
     * 构造函数（带操作用户）
     */
    public EntityCreatedEvent(T entity, ID entityId, Class<T> entityType, String operatorId) {
        super(entity, entityId, entityType, operatorId);
    }

    /**
     * 静态工厂方法
     */
    public static <T, ID> EntityCreatedEvent<T, ID> of(T entity, ID entityId, Class<T> entityType) {
        return new EntityCreatedEvent<>(entity, entityId, entityType);
    }

    /**
     * 静态工厂方法（带操作用户）
     */
    public static <T, ID> EntityCreatedEvent<T, ID> of(T entity, ID entityId, Class<T> entityType, String operatorId) {
        return new EntityCreatedEvent<>(entity, entityId, entityType, operatorId);
    }
}
