package com.hys.hm.shared.framework.exception;

/**
 * 框架异常基类
 * 所有框架相关的异常都应该继承此类
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
public class FrameworkException extends RuntimeException {

    /**
     * 错误代码
     */
    private final String errorCode;

    /**
     * 错误详细信息
     */
    private final Object errorDetails;

    public FrameworkException(String message) {
        super(message);
        this.errorCode = "FRAMEWORK_ERROR";
        this.errorDetails = null;
    }

    public FrameworkException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "FRAMEWORK_ERROR";
        this.errorDetails = null;
    }

    public FrameworkException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.errorDetails = null;
    }

    public FrameworkException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.errorDetails = null;
    }

    public FrameworkException(String errorCode, String message, Object errorDetails) {
        super(message);
        this.errorCode = errorCode;
        this.errorDetails = errorDetails;
    }

    public FrameworkException(String errorCode, String message, Object errorDetails, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.errorDetails = errorDetails;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public Object getErrorDetails() {
        return errorDetails;
    }

    @Override
    public String toString() {
        return String.format("FrameworkException{errorCode='%s', message='%s', errorDetails=%s}", 
                errorCode, getMessage(), errorDetails);
    }
}
