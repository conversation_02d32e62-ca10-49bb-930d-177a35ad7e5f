package com.hys.hm.shared.framework.event;

import lombok.Getter;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 领域事件基类
 * 所有领域事件都应该继承此类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-30
 */
@Getter
public abstract class DomainEvent {

    /**
     * 事件ID
     */
    private final String eventId;

    /**
     * 事件发生时间
     */
    private final LocalDateTime occurredOn;

    /**
     * 事件版本
     */
    private final int version;

    /**
     * 事件来源
     */
    private final String source;

    /**
     * 构造函数
     */
    protected DomainEvent() {
        this.eventId = UUID.randomUUID().toString();
        this.occurredOn = LocalDateTime.now();
        this.version = 1;
        this.source = this.getClass().getSimpleName();
    }

    /**
     * 构造函数（带来源）
     */
    protected DomainEvent(String source) {
        this.eventId = UUID.randomUUID().toString();
        this.occurredOn = LocalDateTime.now();
        this.version = 1;
        this.source = source;
    }

    /**
     * 获取事件类型
     */
    public String getEventType() {
        return this.getClass().getSimpleName();
    }

    /**
     * 获取聚合根ID（子类需要实现）
     */
    public abstract String getAggregateId();

    /**
     * 获取聚合根类型（子类需要实现）
     */
    public abstract String getAggregateType();

    @Override
    public String toString() {
        return String.format("%s{eventId='%s', aggregateId='%s', aggregateType='%s', occurredOn=%s}",
                getEventType(), eventId, getAggregateId(), getAggregateType(), occurredOn);
    }
}
