package com.hys.hm.shared.framework.event;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * 领域事件发布器
 * 负责发布领域事件到Spring事件系统
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-30
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DomainEventPublisher {

    private final ApplicationEventPublisher applicationEventPublisher;

    /**
     * 发布领域事件
     */
    public void publish(DomainEvent event) {
        if (event == null) {
            log.warn("尝试发布空的领域事件");
            return;
        }

        try {
            log.debug("发布领域事件: {}", event);
            applicationEventPublisher.publishEvent(event);
            log.debug("领域事件发布成功: {}", event.getEventType());
        } catch (Exception e) {
            log.error("发布领域事件失败: {}", event, e);
            throw new RuntimeException("发布领域事件失败", e);
        }
    }

    /**
     * 发布实体创建事件
     */
    public <T, ID> void publishEntityCreated(T entity, ID entityId, Class<T> entityType) {
        EntityCreatedEvent<T, ID> event = EntityCreatedEvent.of(entity, entityId, entityType);
        publish(event);
    }

    /**
     * 发布实体创建事件（带操作用户）
     */
    public <T, ID> void publishEntityCreated(T entity, ID entityId, Class<T> entityType, String operatorId) {
        EntityCreatedEvent<T, ID> event = EntityCreatedEvent.of(entity, entityId, entityType, operatorId);
        publish(event);
    }

    /**
     * 发布实体更新事件
     */
    public <T, ID> void publishEntityUpdated(T entity, ID entityId, Class<T> entityType) {
        EntityUpdatedEvent<T, ID> event = EntityUpdatedEvent.of(entity, entityId, entityType);
        publish(event);
    }

    /**
     * 发布实体更新事件（带更新前状态）
     */
    public <T, ID> void publishEntityUpdated(T entity, T previousEntity, ID entityId, Class<T> entityType) {
        EntityUpdatedEvent<T, ID> event = EntityUpdatedEvent.of(entity, previousEntity, entityId, entityType);
        publish(event);
    }

    /**
     * 发布实体更新事件（带操作用户）
     */
    public <T, ID> void publishEntityUpdated(T entity, ID entityId, Class<T> entityType, String operatorId) {
        EntityUpdatedEvent<T, ID> event = EntityUpdatedEvent.of(entity, entityId, entityType, operatorId);
        publish(event);
    }

    /**
     * 发布实体硬删除事件
     */
    public <T, ID> void publishEntityHardDeleted(ID entityId, Class<T> entityType) {
        EntityDeletedEvent<T, ID> event = EntityDeletedEvent.hardDelete(entityId, entityType);
        publish(event);
    }

    /**
     * 发布实体软删除事件
     */
    public <T, ID> void publishEntitySoftDeleted(ID entityId, Class<T> entityType) {
        EntityDeletedEvent<T, ID> event = EntityDeletedEvent.softDelete(entityId, entityType);
        publish(event);
    }

    /**
     * 发布实体硬删除事件（带删除的实体）
     */
    public <T, ID> void publishEntityHardDeleted(T deletedEntity, ID entityId, Class<T> entityType) {
        EntityDeletedEvent<T, ID> event = EntityDeletedEvent.hardDelete(deletedEntity, entityId, entityType);
        publish(event);
    }

    /**
     * 发布实体软删除事件（带删除的实体）
     */
    public <T, ID> void publishEntitySoftDeleted(T deletedEntity, ID entityId, Class<T> entityType) {
        EntityDeletedEvent<T, ID> event = EntityDeletedEvent.softDelete(deletedEntity, entityId, entityType);
        publish(event);
    }

    /**
     * 发布实体删除事件（带操作用户）
     */
    public <T, ID> void publishEntityDeleted(ID entityId, Class<T> entityType, boolean softDelete, String operatorId) {
        EntityDeletedEvent<T, ID> event = new EntityDeletedEvent<>(entityId, entityType, softDelete, operatorId);
        publish(event);
    }
}
