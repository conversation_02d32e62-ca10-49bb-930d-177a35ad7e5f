package com.hys.hm.shared.framework.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 缓存配置属性
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-30
 */
@Data
@ConfigurationProperties(prefix = "hm.framework.cache")
public class CacheProperties {

    /**
     * 缓存类型：caffeine（本地缓存）、redis（分布式缓存）
     */
    private String type = "caffeine";

    /**
     * 是否启用缓存
     */
    private boolean enabled = true;

    /**
     * 缓存名称列表
     */
    private List<String> cacheNames = Arrays.asList(
            "entities",           // 实体缓存
            "entity-lists",       // 实体列表缓存
            "entity-pages",       // 分页查询缓存
            "entity-counts",      // 统计查询缓存
            "query-results"       // 动态查询结果缓存
    );

    /**
     * Caffeine 缓存配置
     */
    private Caffeine caffeine = new Caffeine();

    /**
     * Redis 缓存配置
     */
    private Redis redis = new Redis();

    @Data
    public static class Caffeine {
        /**
         * 最大缓存条目数
         */
        private long maximumSize = 10000;

        /**
         * 写入后过期时间（分钟）
         */
        private long expireAfterWriteMinutes = 30;

        /**
         * 访问后过期时间（分钟）
         */
        private long expireAfterAccessMinutes = 10;

        /**
         * 初始容量
         */
        private int initialCapacity = 100;

        /**
         * 是否启用统计
         */
        private boolean recordStats = true;
    }

    @Data
    public static class Redis {
        /**
         * 默认TTL（分钟）
         */
        private long defaultTtlMinutes = 30;

        /**
         * 不同缓存的TTL配置（分钟）
         */
        private Map<String, Long> ttlConfigs = new HashMap<String, Long>() {{
            put("entities", 60L);           // 实体缓存1小时
            put("entity-lists", 30L);       // 列表缓存30分钟
            put("entity-pages", 15L);       // 分页缓存15分钟
            put("entity-counts", 60L);      // 统计缓存1小时
            put("query-results", 20L);      // 查询结果缓存20分钟
        }};

        /**
         * 缓存键前缀
         */
        private String keyPrefix = "hm:framework:cache:";

        /**
         * 是否使用键前缀
         */
        private boolean useKeyPrefix = true;
    }
}
