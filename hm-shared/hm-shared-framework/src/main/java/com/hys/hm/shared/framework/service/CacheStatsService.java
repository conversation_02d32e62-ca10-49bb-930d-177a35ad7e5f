package com.hys.hm.shared.framework.service;

import com.github.benmanes.caffeine.cache.stats.CacheStats;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 缓存统计服务
 * 提供缓存使用情况的统计信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CacheStatsService {

    private final CacheManager cacheManager;

    /**
     * 获取所有缓存的统计信息
     */
    public Map<String, CacheStatistics> getAllCacheStats() {
        Map<String, CacheStatistics> statsMap = new HashMap<>();

        cacheManager.getCacheNames().forEach(cacheName -> {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                CacheStatistics stats = getCacheStatistics(cache);
                statsMap.put(cacheName, stats);
            }
        });

        return statsMap;
    }

    /**
     * 获取指定缓存的统计信息
     */
    public CacheStatistics getCacheStats(String cacheName) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache == null) {
            log.warn("缓存不存在: {}", cacheName);
            return null;
        }
        return getCacheStatistics(cache);
    }

    /**
     * 获取缓存统计信息
     */
    private CacheStatistics getCacheStatistics(Cache cache) {
        CacheStatistics statistics = new CacheStatistics();
        statistics.setCacheName(cache.getName());
        statistics.setCacheType(cache.getClass().getSimpleName());

        // 如果是 Caffeine 缓存，获取详细统计信息
        if (cache instanceof CaffeineCache) {
            CaffeineCache caffeineCache = (CaffeineCache) cache;
            com.github.benmanes.caffeine.cache.Cache<Object, Object> nativeCache = caffeineCache.getNativeCache();
            CacheStats stats = nativeCache.stats();

            statistics.setRequestCount(stats.requestCount());
            statistics.setHitCount(stats.hitCount());
            statistics.setMissCount(stats.missCount());
            statistics.setHitRate(stats.hitRate());
            statistics.setMissRate(stats.missRate());
            statistics.setLoadCount(stats.loadCount());
            statistics.setLoadExceptionCount(stats.evictionCount());
            statistics.setTotalLoadTime(stats.totalLoadTime());
            statistics.setAverageLoadPenalty(stats.averageLoadPenalty());
            statistics.setEvictionCount(stats.evictionCount());
            statistics.setEstimatedSize(nativeCache.estimatedSize());
        }

        return statistics;
    }

    /**
     * 打印缓存统计信息
     */
    public void printCacheStats() {
        Map<String, CacheStatistics> allStats = getAllCacheStats();

        log.info("=== 缓存统计信息 ===");
        allStats.forEach((cacheName, stats) -> {
            log.info("缓存名称: {}", cacheName);
            log.info("  缓存类型: {}", stats.getCacheType());
            log.info("  请求总数: {}", stats.getRequestCount());
            log.info("  命中次数: {}", stats.getHitCount());
            log.info("  未命中次数: {}", stats.getMissCount());
            log.info("  命中率: {:.2%}", stats.getHitRate());
            log.info("  未命中率: {:.2%}", stats.getMissRate());
            log.info("  加载次数: {}", stats.getLoadCount());
            log.info("  加载异常次数: {}", stats.getLoadExceptionCount());
            log.info("  平均加载时间: {} ns", stats.getAverageLoadPenalty());
            log.info("  驱逐次数: {}", stats.getEvictionCount());
            log.info("  估计大小: {}", stats.getEstimatedSize());
            log.info("  ---");
        });
    }

    /**
     * 重置缓存统计信息（仅适用于支持的缓存类型）
     */
    public void resetStats(String cacheName) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache instanceof CaffeineCache) {
            // Caffeine 缓存不支持重置统计信息
            log.warn("Caffeine 缓存不支持重置统计信息: {}", cacheName);
        }
    }

    /**
     * 缓存统计信息数据类
     */
    @Data
    public static class CacheStatistics {
        private String cacheName;
        private String cacheType;
        private long requestCount;
        private long hitCount;
        private long missCount;
        private double hitRate;
        private double missRate;
        private long loadCount;
        private long loadExceptionCount;
        private long totalLoadTime;
        private double averageLoadPenalty;
        private long evictionCount;
        private long estimatedSize;

        /**
         * 获取格式化的统计信息
         */
        public String getFormattedStats() {
            return String.format(
                "Cache[%s] - Type: %s, Requests: %d, Hits: %d, Misses: %d, Hit Rate: %.2f%%, Size: %d",
                cacheName, cacheType, requestCount, hitCount, missCount, hitRate * 100, estimatedSize
            );
        }
    }
}
