package com.hys.hm.shared.framework.util;

import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 反射工具类
 * 提供常用的反射操作方法
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Slf4j
public class ReflectionUtils {

    /**
     * 获取类的泛型参数类型
     * 
     * @param clazz 类
     * @param index 泛型参数索引
     * @return 泛型参数类型
     */
    @SuppressWarnings("unchecked")
    public static <T> Class<T> getGenericType(Class<?> clazz, int index) {
        Type superClass = clazz.getGenericSuperclass();
        if (superClass instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) superClass;
            Type[] typeArguments = parameterizedType.getActualTypeArguments();
            if (typeArguments.length > index) {
                return (Class<T>) typeArguments[index];
            }
        }
        return null;
    }

    /**
     * 获取第一个泛型参数类型
     * 
     * @param clazz 类
     * @return 第一个泛型参数类型
     */
    public static <T> Class<T> getFirstGenericType(Class<?> clazz) {
        return getGenericType(clazz, 0);
    }

    /**
     * 获取第二个泛型参数类型
     * 
     * @param clazz 类
     * @return 第二个泛型参数类型
     */
    public static <T> Class<T> getSecondGenericType(Class<?> clazz) {
        return getGenericType(clazz, 1);
    }

    /**
     * 获取字段值
     * 
     * @param obj 对象
     * @param fieldName 字段名
     * @return 字段值
     */
    public static Object getFieldValue(Object obj, String fieldName) {
        if (obj == null || fieldName == null) {
            return null;
        }

        try {
            Field field = findField(obj.getClass(), fieldName);
            if (field != null) {
                field.setAccessible(true);
                return field.get(obj);
            }
        } catch (Exception e) {
            log.warn("获取字段值失败: 对象={}, 字段={}, 错误={}", 
                    obj.getClass().getSimpleName(), fieldName, e.getMessage());
        }
        
        return null;
    }

    /**
     * 设置字段值
     * 
     * @param obj 对象
     * @param fieldName 字段名
     * @param value 字段值
     * @return 是否设置成功
     */
    public static boolean setFieldValue(Object obj, String fieldName, Object value) {
        if (obj == null || fieldName == null) {
            return false;
        }

        try {
            Field field = findField(obj.getClass(), fieldName);
            if (field != null) {
                field.setAccessible(true);
                field.set(obj, value);
                return true;
            }
        } catch (Exception e) {
            log.warn("设置字段值失败: 对象={}, 字段={}, 值={}, 错误={}", 
                    obj.getClass().getSimpleName(), fieldName, value, e.getMessage());
        }
        
        return false;
    }

    /**
     * 查找字段（包括父类）
     * 
     * @param clazz 类
     * @param fieldName 字段名
     * @return 字段对象
     */
    public static Field findField(Class<?> clazz, String fieldName) {
        Class<?> searchType = clazz;
        while (searchType != null && searchType != Object.class) {
            try {
                return searchType.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                // 继续在父类中查找
                searchType = searchType.getSuperclass();
            }
        }
        return null;
    }

    /**
     * 获取所有字段（包括父类）
     * 
     * @param clazz 类
     * @return 字段列表
     */
    public static List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        Class<?> searchType = clazz;
        
        while (searchType != null && searchType != Object.class) {
            fields.addAll(Arrays.asList(searchType.getDeclaredFields()));
            searchType = searchType.getSuperclass();
        }
        
        return fields;
    }

    /**
     * 调用方法
     * 
     * @param obj 对象
     * @param methodName 方法名
     * @param args 参数
     * @return 方法返回值
     */
    public static Object invokeMethod(Object obj, String methodName, Object... args) {
        if (obj == null || methodName == null) {
            return null;
        }

        try {
            Class<?>[] paramTypes = new Class[args.length];
            for (int i = 0; i < args.length; i++) {
                paramTypes[i] = args[i] != null ? args[i].getClass() : Object.class;
            }
            
            Method method = findMethod(obj.getClass(), methodName, paramTypes);
            if (method != null) {
                method.setAccessible(true);
                return method.invoke(obj, args);
            }
        } catch (Exception e) {
            log.warn("调用方法失败: 对象={}, 方法={}, 错误={}", 
                    obj.getClass().getSimpleName(), methodName, e.getMessage());
        }
        
        return null;
    }

    /**
     * 查找方法（包括父类）
     * 
     * @param clazz 类
     * @param methodName 方法名
     * @param paramTypes 参数类型
     * @return 方法对象
     */
    public static Method findMethod(Class<?> clazz, String methodName, Class<?>... paramTypes) {
        Class<?> searchType = clazz;
        while (searchType != null && searchType != Object.class) {
            try {
                return searchType.getDeclaredMethod(methodName, paramTypes);
            } catch (NoSuchMethodException e) {
                // 继续在父类中查找
                searchType = searchType.getSuperclass();
            }
        }
        return null;
    }

    /**
     * 检查类是否有指定字段
     * 
     * @param clazz 类
     * @param fieldName 字段名
     * @return 是否有指定字段
     */
    public static boolean hasField(Class<?> clazz, String fieldName) {
        return findField(clazz, fieldName) != null;
    }

    /**
     * 检查类是否有指定方法
     * 
     * @param clazz 类
     * @param methodName 方法名
     * @param paramTypes 参数类型
     * @return 是否有指定方法
     */
    public static boolean hasMethod(Class<?> clazz, String methodName, Class<?>... paramTypes) {
        return findMethod(clazz, methodName, paramTypes) != null;
    }

    /**
     * 创建实例
     * 
     * @param clazz 类
     * @return 实例对象
     */
    @SuppressWarnings("unchecked")
    public static <T> T newInstance(Class<T> clazz) {
        try {
            return clazz.getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            log.error("创建实例失败: 类={}, 错误={}", clazz.getSimpleName(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 检查对象是否为指定类型的实例
     * 
     * @param obj 对象
     * @param clazz 类型
     * @return 是否为指定类型的实例
     */
    public static boolean isInstanceOf(Object obj, Class<?> clazz) {
        return obj != null && clazz != null && clazz.isAssignableFrom(obj.getClass());
    }

    /**
     * 获取类的简单名称
     * 
     * @param clazz 类
     * @return 简单名称
     */
    public static String getSimpleName(Class<?> clazz) {
        return clazz != null ? clazz.getSimpleName() : "null";
    }

    /**
     * 获取对象的类名
     *
     * @param obj 对象
     * @return 类名
     */
    public static String getClassName(Object obj) {
        return obj != null ? obj.getClass().getSimpleName() : "null";
    }
}
