package com.hys.hm.shared.framework.event;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * 领域事件监听器基类
 * 提供通用的事件监听功能
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-30
 */
@Slf4j
public abstract class DomainEventListener {

    /**
     * 同步处理实体创建事件
     */
    @EventListener
    @Order(100)
    public void handleEntityCreated(EntityCreatedEvent<?, ?> event) {
        try {
            log.debug("处理实体创建事件: {}", event);
            onEntityCreated(event);
        } catch (Exception e) {
            log.error("处理实体创建事件失败: {}", event, e);
            handleEventError(event, e);
        }
    }

    /**
     * 同步处理实体更新事件
     */
    @EventListener
    @Order(100)
    public void handleEntityUpdated(EntityUpdatedEvent<?, ?> event) {
        try {
            log.debug("处理实体更新事件: {}", event);
            onEntityUpdated(event);
        } catch (Exception e) {
            log.error("处理实体更新事件失败: {}", event, e);
            handleEventError(event, e);
        }
    }

    /**
     * 同步处理实体删除事件
     */
    @EventListener
    @Order(100)
    public void handleEntityDeleted(EntityDeletedEvent<?, ?> event) {
        try {
            log.debug("处理实体删除事件: {}", event);
            onEntityDeleted(event);
        } catch (Exception e) {
            log.error("处理实体删除事件失败: {}", event, e);
            handleEventError(event, e);
        }
    }

    /**
     * 异步处理实体创建事件（事务提交后）
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    @Order(200)
    public void handleEntityCreatedAsync(EntityCreatedEvent<?, ?> event) {
        try {
            log.debug("异步处理实体创建事件: {}", event);
            onEntityCreatedAsync(event);
        } catch (Exception e) {
            log.error("异步处理实体创建事件失败: {}", event, e);
            handleAsyncEventError(event, e);
        }
    }

    /**
     * 异步处理实体更新事件（事务提交后）
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    @Order(200)
    public void handleEntityUpdatedAsync(EntityUpdatedEvent<?, ?> event) {
        try {
            log.debug("异步处理实体更新事件: {}", event);
            onEntityUpdatedAsync(event);
        } catch (Exception e) {
            log.error("异步处理实体更新事件失败: {}", event, e);
            handleAsyncEventError(event, e);
        }
    }

    /**
     * 异步处理实体删除事件（事务提交后）
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    @Order(200)
    public void handleEntityDeletedAsync(EntityDeletedEvent<?, ?> event) {
        try {
            log.debug("异步处理实体删除事件: {}", event);
            onEntityDeletedAsync(event);
        } catch (Exception e) {
            log.error("异步处理实体删除事件失败: {}", event, e);
            handleAsyncEventError(event, e);
        }
    }

    // 抽象方法，子类可以选择性实现

    /**
     * 处理实体创建事件（同步）
     */
    protected void onEntityCreated(EntityCreatedEvent<?, ?> event) {
        // 默认不处理，子类可以重写
    }

    /**
     * 处理实体更新事件（同步）
     */
    protected void onEntityUpdated(EntityUpdatedEvent<?, ?> event) {
        // 默认不处理，子类可以重写
    }

    /**
     * 处理实体删除事件（同步）
     */
    protected void onEntityDeleted(EntityDeletedEvent<?, ?> event) {
        // 默认不处理，子类可以重写
    }

    /**
     * 处理实体创建事件（异步）
     */
    protected void onEntityCreatedAsync(EntityCreatedEvent<?, ?> event) {
        // 默认不处理，子类可以重写
    }

    /**
     * 处理实体更新事件（异步）
     */
    protected void onEntityUpdatedAsync(EntityUpdatedEvent<?, ?> event) {
        // 默认不处理，子类可以重写
    }

    /**
     * 处理实体删除事件（异步）
     */
    protected void onEntityDeletedAsync(EntityDeletedEvent<?, ?> event) {
        // 默认不处理，子类可以重写
    }

    /**
     * 处理同步事件错误
     */
    protected void handleEventError(DomainEvent event, Exception error) {
        // 默认只记录日志，子类可以重写进行更复杂的错误处理
        log.error("处理领域事件时发生错误: event={}, error={}", event, error.getMessage());
    }

    /**
     * 处理异步事件错误
     */
    protected void handleAsyncEventError(DomainEvent event, Exception error) {
        // 默认只记录日志，子类可以重写进行更复杂的错误处理
        log.error("异步处理领域事件时发生错误: event={}, error={}", event, error.getMessage());
    }
}
