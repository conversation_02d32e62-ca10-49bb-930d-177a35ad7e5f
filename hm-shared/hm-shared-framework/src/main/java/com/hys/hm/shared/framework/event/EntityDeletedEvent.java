package com.hys.hm.shared.framework.event;

import lombok.Getter;

/**
 * 实体删除事件
 * 当实体被删除时发布此事件
 *
 * @param <T> 实体类型
 * @param <ID> 实体ID类型
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-30
 */
@Getter
public class EntityDeletedEvent<T, ID> extends EntityEvent<T, ID> {

    /**
     * 是否为软删除
     */
    private final boolean softDelete;

    /**
     * 删除前的实体状态（可选）
     */
    private final T deletedEntity;

    /**
     * 构造函数（仅ID）
     */
    public EntityDeletedEvent(ID entityId, Class<T> entityType, boolean softDelete) {
        super(null, entityId, entityType);
        this.softDelete = softDelete;
        this.deletedEntity = null;
    }

    /**
     * 构造函数（带删除的实体）
     */
    public EntityDeletedEvent(T deletedEntity, ID entityId, Class<T> entityType, boolean softDelete) {
        super(deletedEntity, entityId, entityType);
        this.softDelete = softDelete;
        this.deletedEntity = deletedEntity;
    }

    /**
     * 构造函数（带操作用户）
     */
    public EntityDeletedEvent(ID entityId, Class<T> entityType, boolean softDelete, String operatorId) {
        super(null, entityId, entityType, operatorId);
        this.softDelete = softDelete;
        this.deletedEntity = null;
    }

    /**
     * 构造函数（完整参数）
     */
    public EntityDeletedEvent(T deletedEntity, ID entityId, Class<T> entityType, boolean softDelete, String operatorId) {
        super(deletedEntity, entityId, entityType, operatorId);
        this.softDelete = softDelete;
        this.deletedEntity = deletedEntity;
    }

    /**
     * 静态工厂方法（硬删除）
     */
    public static <T, ID> EntityDeletedEvent<T, ID> hardDelete(ID entityId, Class<T> entityType) {
        return new EntityDeletedEvent<>(entityId, entityType, false);
    }

    /**
     * 静态工厂方法（软删除）
     */
    public static <T, ID> EntityDeletedEvent<T, ID> softDelete(ID entityId, Class<T> entityType) {
        return new EntityDeletedEvent<>(entityId, entityType, true);
    }

    /**
     * 静态工厂方法（硬删除，带实体）
     */
    public static <T, ID> EntityDeletedEvent<T, ID> hardDelete(T deletedEntity, ID entityId, Class<T> entityType) {
        return new EntityDeletedEvent<>(deletedEntity, entityId, entityType, false);
    }

    /**
     * 静态工厂方法（软删除，带实体）
     */
    public static <T, ID> EntityDeletedEvent<T, ID> softDelete(T deletedEntity, ID entityId, Class<T> entityType) {
        return new EntityDeletedEvent<>(deletedEntity, entityId, entityType, true);
    }

    /**
     * 静态工厂方法（硬删除，带操作用户）
     */
    public static <T, ID> EntityDeletedEvent<T, ID> hardDelete(ID entityId, Class<T> entityType, String operatorId) {
        return new EntityDeletedEvent<>(entityId, entityType, false, operatorId);
    }

    /**
     * 静态工厂方法（软删除，带操作用户）
     */
    public static <T, ID> EntityDeletedEvent<T, ID> softDelete(ID entityId, Class<T> entityType, String operatorId) {
        return new EntityDeletedEvent<>(entityId, entityType, true, operatorId);
    }

    /**
     * 是否有删除的实体数据
     */
    public boolean hasDeletedEntity() {
        return deletedEntity != null;
    }

    /**
     * 是否为硬删除
     */
    public boolean isHardDelete() {
        return !softDelete;
    }
}
