package com.hys.hm.shared.framework.service;

import com.hys.hm.shared.encrypt.service.EncryptFieldQueryHelper;
import com.hys.hm.shared.framework.base.BaseEntity;
import com.hys.hm.shared.framework.cache.EntityCache;
import com.hys.hm.shared.framework.util.CacheUtils;
import com.hys.hm.shared.framework.event.DomainEventPublisher;
import com.hys.hm.shared.common.page.PageRequest;
import com.hys.hm.shared.common.page.PageResult;
import com.hys.hm.shared.common.query.QueryCondition;
import com.hys.hm.shared.framework.repository.BaseRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 基础服务实现类
 * 提供通用的业务逻辑操作的具体实现
 *
 * @param <T> 实体类型
 * @param <ID> 主键类型
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Slf4j
@Transactional(readOnly = true)
public abstract class BaseServiceImpl<T, ID extends Serializable> implements BaseService<T, ID> {

    protected BaseRepository<T, ID> baseRepository;

    @Autowired(required = false)
    protected CacheUtils cacheUtils;

    @Autowired(required = false)
    protected DomainEventPublisher eventPublisher;

    @Autowired(required = false)
    protected EncryptFieldQueryHelper encryptFieldQueryHelper;

    // 构造函数
    protected BaseServiceImpl() {
        // 默认构造函数，子类需要手动设置 baseRepository
    }

    // 带参数的构造函数
    protected BaseServiceImpl(BaseRepository<T, ID> baseRepository) {
        this.baseRepository = baseRepository;
    }


    @Override
    @Transactional
    @Caching(evict = {
        @CacheEvict(cacheNames = "entities", key = "#result.id", condition = "#result.id != null"),
        @CacheEvict(cacheNames = "entity-lists", allEntries = true),
        @CacheEvict(cacheNames = "entity-pages", allEntries = true),
        @CacheEvict(cacheNames = "entity-counts", allEntries = true)
    })
    public T save(T entity) {
        Assert.notNull(entity, "实体不能为空");

        validate(entity);
        beforeSave(entity);

        T savedEntity = baseRepository.save(entity);

        afterSave(savedEntity);

        // 发布实体创建事件
        if (eventPublisher != null) {
            ID entityId = getEntityId(savedEntity);
            Class<T> entityType = getEntityType();
            eventPublisher.publishEntityCreated(savedEntity, entityId, entityType);
        }

        log.debug("保存实体成功: {}", savedEntity);

        return savedEntity;
    }

    @Override
    @Transactional
    @Caching(evict = {
        @CacheEvict(cacheNames = "entities", allEntries = true),
        @CacheEvict(cacheNames = "entity-lists", allEntries = true),
        @CacheEvict(cacheNames = "entity-pages", allEntries = true),
        @CacheEvict(cacheNames = "entity-counts", allEntries = true)
    })
    public List<T> saveAll(List<T> entities) {
        Assert.notEmpty(entities, "实体列表不能为空");

        for (T entity : entities) {
            validate(entity);
            beforeSave(entity);
        }

        List<T> savedEntities = baseRepository.saveAll(entities);

        for (T entity : savedEntities) {
            afterSave(entity);
        }

        log.debug("批量保存实体成功: {} 条", savedEntities.size());
        return savedEntities;
    }

    @Override
    @Transactional
    public T update(T entity) {
        Assert.notNull(entity, "实体不能为空");

        validate(entity);
        beforeUpdate(entity);

        T updatedEntity = baseRepository.save(entity);

        afterUpdate(updatedEntity);

        // 发布实体更新事件
        if (eventPublisher != null) {
            ID entityId = getEntityId(updatedEntity);
            Class<T> entityType = getEntityType();
            eventPublisher.publishEntityUpdated(updatedEntity, entityId, entityType);
        }

        log.debug("更新实体成功: {}", updatedEntity);

        return updatedEntity;
    }

    @Override
    @Transactional
    public List<T> updateAll(List<T> entities) {
        Assert.notEmpty(entities, "实体列表不能为空");

        for (T entity : entities) {
            validate(entity);
            beforeUpdate(entity);
        }

        List<T> updatedEntities = baseRepository.updateAll(entities);

        for (T entity : updatedEntities) {
            afterUpdate(entity);
        }

        log.debug("批量更新实体成功: {} 条", updatedEntities.size());
        return updatedEntities;
    }

    @Override
    @Transactional
    public T saveOrUpdate(T entity) {
        Assert.notNull(entity, "实体不能为空");

        if (entity instanceof BaseEntity) {
            BaseEntity<?> baseEntity = (BaseEntity<?>) entity;
            if (baseEntity.isNew()) {
                return save(entity);
            } else {
                return update(entity);
            }
        } else {
            // 对于非BaseEntity，直接使用save方法（JPA会自动判断是插入还是更新）
            return save(entity);
        }
    }

    @Override
    @Transactional
    public boolean deleteById(ID id) {
        Assert.notNull(id, "实体ID不能为空");

        if (!baseRepository.existsById(id)) {
            log.warn("要删除的实体不存在: ID={}", id);
            return false;
        }

        beforeDelete(id);
        baseRepository.deleteById(id);
        afterDelete(id);

        // 发布实体删除事件
        if (eventPublisher != null) {
            Class<T> entityType = getEntityType();
            eventPublisher.publishEntityHardDeleted(id, entityType);
        }

        log.debug("删除实体成功: ID={}", id);
        return true;
    }

    @Override
    @Transactional
    public int deleteByIds(List<ID> ids) {
        Assert.notEmpty(ids, "实体ID列表不能为空");

        int deletedCount = 0;
        for (ID id : ids) {
            if (deleteById(id)) {
                deletedCount++;
            }
        }

        log.debug("批量删除实体成功: {} 条", deletedCount);
        return deletedCount;
    }

    @Override
    @Transactional
    public boolean softDeleteById(ID id) {
        Assert.notNull(id, "实体ID不能为空");

        beforeDelete(id);
        boolean result = baseRepository.softDeleteById(id);

        if (result) {
            afterDelete(id);

            // 发布实体软删除事件
            if (eventPublisher != null) {
                Class<T> entityType = getEntityType();
                eventPublisher.publishEntitySoftDeleted(id, entityType);
            }

            log.debug("软删除实体成功: ID={}", id);
        } else {
            log.warn("软删除实体失败: ID={}", id);
        }

        return result;
    }

    @Override
    @Transactional
    public int softDeleteByIds(List<ID> ids) {
        Assert.notEmpty(ids, "实体ID列表不能为空");

        int deletedCount = baseRepository.softDeleteByIds(ids);
        log.debug("批量软删除实体成功: {} 条", deletedCount);

        return deletedCount;
    }

    @Override
    @Transactional
    public boolean restoreById(ID id) {
        Assert.notNull(id, "实体ID不能为空");

        boolean result = baseRepository.restoreById(id);

        if (result) {
            log.debug("恢复软删除实体成功: ID={}", id);
        } else {
            log.warn("恢复软删除实体失败: ID={}", id);
        }

        return result;
    }

    @Override
    @EntityCache(key = "#id")
    public Optional<T> findById(ID id) {
        Assert.notNull(id, "实体ID不能为空");
        return baseRepository.findById(id);
    }

    @Override
    @EntityCache(key = "#id")
    public T getById(ID id) {
        return findById(id).orElseThrow(() ->
                new IllegalArgumentException("实体不存在: ID=" + id));
    }

    @Override
    @Cacheable(cacheNames = "entity-lists", key = "'ids:' + #ids.hashCode()")
    public List<T> findByIds(List<ID> ids) {
        Assert.notEmpty(ids, "实体ID列表不能为空");
        return baseRepository.findAllById(ids);
    }

    @Override
    @Cacheable(cacheNames = "entity-lists", key = "'all'")
    public List<T> findAll() {
        return baseRepository.findAll();
    }

    @Override
    @Cacheable(cacheNames = "entity-pages", key = "'page:' + #pageRequest.hashCode()")
    public PageResult<T> findAll(PageRequest pageRequest) {
        Assert.notNull(pageRequest, "分页请求不能为空");
        return baseRepository.findByPageRequest(pageRequest);
    }

    @Override
    @Cacheable(cacheNames = "query-results", key = "'conditions:' + (#conditions != null ? #conditions.hashCode() : 'null')")
    public List<T> findByConditions(List<QueryCondition> conditions) {
        return baseRepository.findByConditions(conditions);
    }

    @Override
    @Cacheable(cacheNames = "entity-pages", key = "'pageRequest:' + #pageRequest.hashCode()")
    public PageResult<T> findByPageRequest(PageRequest pageRequest) {
        Assert.notNull(pageRequest, "分页请求不能为空");
        return baseRepository.findByPageRequest(pageRequest);
    }



    @Override
    public List<T> findByField(String fieldName, Object value) {
        Assert.hasText(fieldName, "字段名不能为空");
        return baseRepository.findByField(fieldName, value);
    }

    @Override
    public Optional<T> findFirstByField(String fieldName, Object value) {
        Assert.hasText(fieldName, "字段名不能为空");
        return baseRepository.findFirstByField(fieldName, value);
    }

    @Override
    public List<T> findByFieldLike(String fieldName, Object value) {
        Assert.hasText(fieldName, "字段名不能为空");
        return baseRepository.findByFieldLike(fieldName, value);
    }

    @Override
    public List<T> findByFields(Map<String, Object> fieldValues) {
        Assert.notEmpty(fieldValues, "字段值映射不能为空");
        return baseRepository.findByFields(fieldValues);
    }

    @Override
    @Cacheable(cacheNames = "entity-counts", key = "'conditions:' + (#conditions != null ? #conditions.hashCode() : 'null')")
    public long countByConditions(List<QueryCondition> conditions) {
        return baseRepository.countByConditions(conditions);
    }

    @Override
    @Cacheable(cacheNames = "entity-counts", key = "'total'")
    public long count() {
        return baseRepository.count();
    }

    @Override
    public long countNotDeleted() {
        return baseRepository.countNotDeleted();
    }

    @Override
    public boolean existsByConditions(List<QueryCondition> conditions) {
        return baseRepository.existsByConditions(conditions);
    }

    @Override
    public boolean existsById(ID id) {
        Assert.notNull(id, "实体ID不能为空");
        return baseRepository.existsById(id);
    }

    @Override
    public boolean existsByField(String fieldName, Object value) {
        Assert.hasText(fieldName, "字段名不能为空");
        return !baseRepository.findByField(fieldName, value).isEmpty();
    }

    @Override
    public List<T> findAllNotDeleted() {
        return baseRepository.findAllNotDeleted();
    }

    @Override
    public PageResult<T> findAllNotDeleted(PageRequest pageRequest) {
        Assert.notNull(pageRequest, "分页请求不能为空");

        // 添加未删除条件
        pageRequest.addEq("deleted", 0);
        return baseRepository.findByPageRequest(pageRequest);
    }

    @Override
    public Optional<T> findByIdNotDeleted(ID id) {
        Assert.notNull(id, "实体ID不能为空");
        return baseRepository.findByIdNotDeleted(id);
    }

    @Override
    @Transactional
    public int updateFieldByConditions(List<QueryCondition> conditions, String fieldName, Object newValue) {
        Assert.hasText(fieldName, "字段名不能为空");
        return baseRepository.updateFieldByConditions(conditions, fieldName, newValue);
    }

    @Override
    @Transactional
    public int updateFieldsByConditions(List<QueryCondition> conditions, Map<String, Object> fieldValues) {
        Assert.notEmpty(fieldValues, "字段值映射不能为空");
        return baseRepository.updateFieldsByConditions(conditions, fieldValues);
    }

    // 业务逻辑钩子方法的默认实现（子类可以重写）

    @Override
    public void validate(T entity) {
        // 默认不做验证，子类可以重写
    }

    @Override
    public void beforeSave(T entity) {
        // 默认不做处理，子类可以重写
    }

    @Override
    public void afterSave(T entity) {
        // 默认不做处理，子类可以重写
    }

    @Override
    public void beforeUpdate(T entity) {
        // 默认不做处理，子类可以重写
    }

    @Override
    public void afterUpdate(T entity) {
        // 默认不做处理，子类可以重写
    }

    @Override
    public void beforeDelete(ID id) {
        // 默认不做处理，子类可以重写
    }

    @Override
    public void afterDelete(ID id) {
        // 默认不做处理，子类可以重写
    }

    @Override
    @CacheEvict(cacheNames = {"entities", "entity-lists", "entity-pages", "entity-counts", "query-results"}, allEntries = true)
    public void refreshCache() {
        if (cacheUtils != null) {
            String entityName = getEntityName();
            log.info("刷新实体缓存: {}", entityName);

            // 清空所有相关缓存
            cacheUtils.clear("entities");
            cacheUtils.clear("entity-lists");
            cacheUtils.clear("entity-pages");
            cacheUtils.clear("entity-counts");
            cacheUtils.clear("query-results");
        }
    }

    @Override
    @CacheEvict(cacheNames = {"entities", "entity-lists", "entity-pages", "entity-counts", "query-results"}, allEntries = true)
    public void clearCache() {
        if (cacheUtils != null) {
            String entityName = getEntityName();
            log.info("清空实体缓存: {}", entityName);

            // 清空所有相关缓存
            cacheUtils.clear("entities");
            cacheUtils.clear("entity-lists");
            cacheUtils.clear("entity-pages");
            cacheUtils.clear("entity-counts");
            cacheUtils.clear("query-results");
        }
    }

    /**
     * 获取实体名称（用于缓存键生成）
     */
    protected String getEntityName() {
        Type genericSuperclass = getClass().getGenericSuperclass();
        if (genericSuperclass instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) genericSuperclass;
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
            if (actualTypeArguments.length > 0) {
                return actualTypeArguments[0].getTypeName();
            }
        }
        return "Unknown";
    }

    /**
     * 获取实体类型（用于事件发布）
     */
    @SuppressWarnings("unchecked")
    protected Class<T> getEntityType() {
        Type genericSuperclass = getClass().getGenericSuperclass();
        if (genericSuperclass instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) genericSuperclass;
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
            if (actualTypeArguments.length > 0) {
                return (Class<T>) actualTypeArguments[0];
            }
        }
        throw new IllegalStateException("无法获取实体类型");
    }

    /**
     * 获取实体ID（用于事件发布）
     */
    protected ID getEntityId(T entity) {
        if (entity instanceof BaseEntity) {
            return ((BaseEntity<ID>) entity).getId();
        }

        // 使用反射获取ID字段
        try {
            Field idField = entity.getClass().getDeclaredField("id");
            idField.setAccessible(true);
            return (ID) idField.get(entity);
        } catch (Exception e) {
            log.warn("无法获取实体ID: {}", e.getMessage());
            return null;
        }
    }

    @Override
    public List<T> findByEncryptedField(String fieldName, Object value) {
        log.debug("根据加密字段查询: field={}, value=[MASKED]", fieldName);

        if (encryptFieldQueryHelper == null || !encryptFieldQueryHelper.isEncryptServiceAvailable()) {
            log.debug("加密查询服务不可用，使用普通查询");
            return findByField(fieldName, value);
        }

        try {
            Class<T> entityClass = getEntityType();

            // 检查是否为加密字段
            if (!encryptFieldQueryHelper.isEncryptField(entityClass, fieldName)) {
                log.debug("字段不是加密字段，使用普通查询: field={}", fieldName);
                return findByField(fieldName, value);
            }

            // 使用加密索引查询
            List<String> entityIds = encryptFieldQueryHelper.findEntityIdsByEncryptedField(entityClass, fieldName, value);

            if (entityIds.isEmpty()) {
                return List.of();
            }

            // 根据ID列表查询实体
            return findByIds((List<ID>) entityIds);

        } catch (Exception e) {
            log.error("加密字段查询失败，降级为普通查询: field={}, error={}", fieldName, e.getMessage());
            return findByField(fieldName, value);
        }
    }

    @Override
    public Optional<T> findFirstByEncryptedField(String fieldName, Object value) {
        log.debug("根据加密字段查询第一个: field={}, value=[MASKED]", fieldName);

        List<T> results = findByEncryptedField(fieldName, value);
        return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
    }

    @Override
    public List<T> findByEncryptedFieldLike(String fieldName, Object value) {
        log.debug("根据加密字段模糊查询: field={}, value=[MASKED]", fieldName);

        if (encryptFieldQueryHelper == null || !encryptFieldQueryHelper.isEncryptServiceAvailable()) {
            log.debug("加密查询服务不可用，使用普通模糊查询");
            return findByFieldLike(fieldName, value);
        }

        try {
            Class<T> entityClass = getEntityType();

            // 检查是否为加密字段
            if (!encryptFieldQueryHelper.isEncryptField(entityClass, fieldName)) {
                log.debug("字段不是加密字段，使用普通模糊查询: field={}", fieldName);
                return findByFieldLike(fieldName, value);
            }

            // 使用加密分词索引查询
            List<String> entityIds = encryptFieldQueryHelper.findEntityIdsByEncryptedFieldLike(entityClass, fieldName, value);

            if (entityIds.isEmpty()) {
                return List.of();
            }

            // 根据ID列表查询实体
            return findByIds((List<ID>) entityIds);

        } catch (Exception e) {
            log.error("加密字段模糊查询失败，降级为普通查询: field={}, error={}", fieldName, e.getMessage());
            return findByFieldLike(fieldName, value);
        }
    }
}
