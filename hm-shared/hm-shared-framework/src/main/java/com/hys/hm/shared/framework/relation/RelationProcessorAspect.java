package com.hys.hm.shared.framework.relation;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 关联关系处理切面
 * 自动处理查询结果的关联关系
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class RelationProcessorAspect {

    private final RelationManager relationManager;

    /**
     * 处理查询服务返回的单个对象
     */
    @AfterReturning(
        pointcut = "execution(* com.hys.hm.application.*.service.*QueryService.*(..)) && " +
                  "!execution(* com.hys.hm.application.*.service.*QueryService.count*(..))",
        returning = "result"
    )
    public void processQueryResult(Object result) {
        if (result == null) {
            return;
        }

        try {
            if (result instanceof List) {
                @SuppressWarnings("unchecked")
                List<Object> list = (List<Object>) result;
                relationManager.processRelations(list);
            } else {
                relationManager.processRelations(result);
            }
        } catch (Exception e) {
            log.error("处理查询结果关联关系失败", e);
        }
    }

    /**
     * 处理应用服务返回的结果
     */
    @AfterReturning(
        pointcut = "execution(* com.hys.hm.application.*.service.*ApplicationService.get*(..)) || " +
                  "execution(* com.hys.hm.application.*.service.*ApplicationService.find*(..)) || " +
                  "execution(* com.hys.hm.application.*.service.*ApplicationService.query*(..))",
        returning = "result"
    )
    public void processApplicationServiceResult(Object result) {
        processQueryResult(result);
    }
}
