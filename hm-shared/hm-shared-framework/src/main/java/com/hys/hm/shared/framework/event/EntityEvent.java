package com.hys.hm.shared.framework.event;

import lombok.Getter;

/**
 * 实体事件基类
 * 所有实体相关的事件都应该继承此类
 *
 * @param <T> 实体类型
 * @param <ID> 实体ID类型
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-30
 */
@Getter
public abstract class EntityEvent<T, ID> extends DomainEvent {

    /**
     * 实体对象
     */
    private final T entity;

    /**
     * 实体ID
     */
    private final ID entityId;

    /**
     * 实体类型
     */
    private final Class<T> entityType;

    /**
     * 操作用户ID
     */
    private final String operatorId;

    /**
     * 构造函数
     */
    protected EntityEvent(T entity, ID entityId, Class<T> entityType) {
        super();
        this.entity = entity;
        this.entityId = entityId;
        this.entityType = entityType;
        this.operatorId = getCurrentUserId(); // 可以从安全上下文获取
    }

    /**
     * 构造函数（带操作用户）
     */
    protected EntityEvent(T entity, ID entityId, Class<T> entityType, String operatorId) {
        super();
        this.entity = entity;
        this.entityId = entityId;
        this.entityType = entityType;
        this.operatorId = operatorId;
    }

    @Override
    public String getAggregateId() {
        return entityId != null ? entityId.toString() : null;
    }

    @Override
    public String getAggregateType() {
        return entityType.getSimpleName();
    }

    /**
     * 获取当前用户ID（可以从安全上下文获取）
     * 子类可以重写此方法
     */
    protected String getCurrentUserId() {
        // TODO: 从Spring Security上下文获取当前用户ID
        return "system";
    }

    /**
     * 获取实体名称
     */
    public String getEntityName() {
        return entityType.getSimpleName();
    }

    @Override
    public String toString() {
        return String.format("%s{entityId='%s', entityType='%s', operatorId='%s', occurredOn=%s}",
                getEventType(), entityId, getEntityName(), operatorId, getOccurredOn());
    }
}
