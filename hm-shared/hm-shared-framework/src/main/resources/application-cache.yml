# 缓存配置示例
hm:
  framework:
    cache:
      # 缓存类型：caffeine（本地缓存）、redis（分布式缓存）
      type: caffeine
      # 是否启用缓存
      enabled: true
      # 缓存名称列表
      cache-names:
        - entities
        - entity-lists
        - entity-pages
        - entity-counts
        - query-results

      # Caffeine 本地缓存配置
      caffeine:
        # 最大缓存条目数
        maximum-size: 10000
        # 写入后过期时间（分钟）
        expire-after-write-minutes: 30
        # 访问后过期时间（分钟）
        expire-after-access-minutes: 10
        # 初始容量
        initial-capacity: 100
        # 是否启用统计
        record-stats: true

      # Redis 分布式缓存配置
      redis:
        # 默认TTL（分钟）
        default-ttl-minutes: 30
        # 不同缓存的TTL配置（分钟）
        ttl-configs:
          entities: 60           # 实体缓存1小时
          entity-lists: 30       # 列表缓存30分钟
          entity-pages: 15       # 分页缓存15分钟
          entity-counts: 60      # 统计缓存1小时
          query-results: 20      # 查询结果缓存20分钟
        # 缓存键前缀
        key-prefix: "hm:framework:cache:"
        # 是否使用键前缀
        use-key-prefix: true

# Spring Cache 配置
spring:
  cache:
    # 缓存类型
    type: caffeine
    # Caffeine 配置
    caffeine:
      spec: maximumSize=10000,expireAfterWrite=30m,expireAfterAccess=10m,recordStats
    # Redis 配置（当使用 Redis 缓存时）
    redis:
      time-to-live: 30m
      cache-null-values: false
      use-key-prefix: true
      key-prefix: "hm:framework:cache:"

# 日志配置
logging:
  level:
    com.hys.hm.shared.framework.config.CacheConfiguration: DEBUG
    com.hys.hm.shared.framework.config.com.hys.hm.shared.framework.service.CacheStatsService: DEBUG
    org.springframework.cache: DEBUG
