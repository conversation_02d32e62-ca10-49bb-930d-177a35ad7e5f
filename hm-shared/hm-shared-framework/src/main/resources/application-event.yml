# 事件配置示例
hm:
  framework:
    event:
      # 是否启用事件机制
      enabled: true
      # 是否启用事件统计
      enable-stats: true
      # 是否启用事件日志
      enable-logging: true
      # 事件处理超时时间（毫秒）
      timeout-millis: 30000

      # 异步事件配置
      async:
        # 核心线程数
        core-pool-size: 2
        # 最大线程数
        max-pool-size: 10
        # 队列容量
        queue-capacity: 100
        # 线程空闲时间（秒）
        keep-alive-seconds: 60
        # 是否允许核心线程超时
        allow-core-thread-time-out: false

      # 重试配置
      retry:
        # 是否启用重试
        enabled: false
        # 最大重试次数
        max-attempts: 3
        # 重试间隔（毫秒）
        delay-millis: 1000
        # 重试间隔倍数
        multiplier: 2.0
        # 最大重试间隔（毫秒）
        max-delay-millis: 10000

# Spring 异步配置
spring:
  task:
    execution:
      pool:
        # 核心线程数
        core-size: 2
        # 最大线程数
        max-size: 10
        # 队列容量
        queue-capacity: 100
        # 线程空闲时间
        keep-alive: 60s
      # 线程名称前缀
      thread-name-prefix: "async-event-"
      shutdown:
        # 等待任务完成后关闭
        await-termination: true
        # 等待时间
        await-termination-period: 60s

# 日志配置
logging:
  level:
    com.hys.hm.shared.framework.event: DEBUG
    com.hys.hm.shared.framework.config.EventConfiguration: DEBUG
    com.hys.hm.shared.framework.config.com.hys.hm.shared.framework.service.EventStatsService: DEBUG
    org.springframework.context.event: DEBUG
