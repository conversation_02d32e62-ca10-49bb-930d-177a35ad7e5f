package com.hys.hm.shared.framework.util;

import lombok.Data;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ReflectionUtils测试类
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
class ReflectionUtilsTest {

    @Test
    void testGetAllDeclaredFields() {
        Field[] fields = ReflectionUtils.getAllDeclaredFields(TestEntity.class);
        
        assertNotNull(fields);
        assertTrue(fields.length >= 3); // id, name, parentField
        
        // 验证包含父类字段
        boolean hasParentField = false;
        boolean hasIdField = false;
        boolean hasNameField = false;
        
        for (Field field : fields) {
            if ("parentField".equals(field.getName())) {
                hasParentField = true;
            }
            if ("id".equals(field.getName())) {
                hasIdField = true;
            }
            if ("name".equals(field.getName())) {
                hasNameField = true;
            }
        }
        
        assertTrue(hasParentField, "应该包含父类字段");
        assertTrue(hasIdField, "应该包含id字段");
        assertTrue(hasNameField, "应该包含name字段");
    }

    @Test
    void testGetAllDeclaredMethods() {
        Method[] methods = ReflectionUtils.getAllDeclaredMethods(TestEntity.class);
        
        assertNotNull(methods);
        assertTrue(methods.length > 0);
        
        // 验证包含父类方法
        boolean hasParentMethod = false;
        for (Method method : methods) {
            if ("parentMethod".equals(method.getName())) {
                hasParentMethod = true;
                break;
            }
        }
        
        assertTrue(hasParentMethod, "应该包含父类方法");
    }

    @Test
    void testFindField() {
        Field field = ReflectionUtils.findField(TestEntity.class, "name");
        assertNotNull(field);
        assertEquals("name", field.getName());
        
        // 测试查找父类字段
        Field parentField = ReflectionUtils.findField(TestEntity.class, "parentField");
        assertNotNull(parentField);
        assertEquals("parentField", parentField.getName());
        
        // 测试不存在的字段
        Field notExistField = ReflectionUtils.findField(TestEntity.class, "notExist");
        assertNull(notExistField);
    }

    @Test
    void testGetAndSetField() {
        TestEntity entity = new TestEntity();
        entity.setId("test-id");
        entity.setName("test-name");
        
        // 测试获取字段值
        Field idField = ReflectionUtils.findField(TestEntity.class, "id");
        Object idValue = ReflectionUtils.getField(idField, entity);
        assertEquals("test-id", idValue);
        
        // 测试设置字段值
        Field nameField = ReflectionUtils.findField(TestEntity.class, "name");
        ReflectionUtils.setField(nameField, entity, "new-name");
        assertEquals("new-name", entity.getName());
    }

    @Test
    void testInvokeMethod() {
        TestEntity entity = new TestEntity();
        entity.setName("test");
        
        // 测试调用方法
        Method getNameMethod = ReflectionUtils.findMethod(TestEntity.class, "getName");
        Object result = ReflectionUtils.invokeMethod(getNameMethod, entity);
        assertEquals("test", result);
        
        // 测试调用带参数的方法
        Method setNameMethod = ReflectionUtils.findMethod(TestEntity.class, "setName", String.class);
        ReflectionUtils.invokeMethod(setNameMethod, entity, "new-test");
        assertEquals("new-test", entity.getName());
    }

    @Test
    void testGetFieldValue() {
        TestEntity entity = new TestEntity();
        entity.setName("test-value");
        
        Object value = ReflectionUtils.getFieldValue(entity, "name");
        assertEquals("test-value", value);
    }

    @Test
    void testSetFieldValue() {
        TestEntity entity = new TestEntity();
        
        boolean success = ReflectionUtils.setFieldValue(entity, "name", "new-value");
        assertTrue(success);
        assertEquals("new-value", entity.getName());
    }

    @Test
    void testInvokeMethodByName() {
        TestEntity entity = new TestEntity();
        entity.setName("original");
        
        // 测试无参方法调用
        Object result = ReflectionUtils.invokeMethod(entity, "getName");
        assertEquals("original", result);
        
        // 测试有参方法调用
        ReflectionUtils.invokeMethod(entity, "setName", "modified");
        assertEquals("modified", entity.getName());
    }

    @Test
    void testNewInstance() {
        TestEntity instance = ReflectionUtils.newInstance(TestEntity.class);
        assertNotNull(instance);
        assertTrue(instance instanceof TestEntity);
    }

    // 测试用的实体类
    @Data
    static class TestParent {
        private String parentField;
        
        public void parentMethod() {
            // 父类方法
        }
    }

    @Data
    static class TestEntity extends TestParent {
        private String id;
        private String name;
        
        public void testMethod() {
            // 测试方法
        }
    }
}
