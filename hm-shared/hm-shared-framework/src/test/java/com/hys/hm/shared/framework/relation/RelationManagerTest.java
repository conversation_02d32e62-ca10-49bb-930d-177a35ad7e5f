package com.hys.hm.shared.framework.relation;

import com.hys.hm.shared.annotations.relation.EntityRelation;
import com.hys.hm.shared.annotations.relation.RelationLoader;
import com.hys.hm.shared.annotations.relation.RelationProvider;
import lombok.Data;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 关联管理器测试类
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@ExtendWith(MockitoExtension.class)
class RelationManagerTest {

    private RelationManager relationManager;
    private ApplicationContext applicationContext;

    @BeforeEach
    void setUp() {
        applicationContext = mock(ApplicationContext.class);
        relationManager = new RelationManager();
        relationManager.setApplicationContext(applicationContext);
    }

    @Test
    void testProcessSingleObjectRelation() throws Exception {
        // Given
        TestReferralDTO referral = new TestReferralDTO();
        referral.setId("ref-001");
        referral.setPatientId("patient-001");

        TestPatientProvider provider = new TestPatientProvider();
        
        // Mock ApplicationContext
        Map<String, Object> providers = new HashMap<>();
        providers.put("testPatientProvider", provider);
        when(applicationContext.getBeansWithAnnotation(RelationProvider.class)).thenReturn(providers);
        when(applicationContext.getBean("testPatientProvider")).thenReturn(provider);
        
        Map<String, Object> beans = new HashMap<>();
        beans.put("testPatientProvider", provider);
        when(applicationContext.getBeansOfType(Object.class)).thenReturn(beans);

        // Initialize
        relationManager.afterPropertiesSet();

        // When
        relationManager.processRelations(referral);

        // Then
        assertNotNull(referral.getPatientInfo());
        assertEquals("张三", ((TestPatientInfo) referral.getPatientInfo()).getName());
    }

    @Test
    void testProcessBatchObjectsRelation() throws Exception {
        // Given
        List<TestReferralDTO> referrals = Arrays.asList(
            createTestReferral("ref-001", "patient-001"),
            createTestReferral("ref-002", "patient-002"),
            createTestReferral("ref-003", "patient-001") // 重复患者ID
        );

        TestPatientProvider provider = new TestPatientProvider();
        
        // Mock ApplicationContext
        Map<String, Object> providers = new HashMap<>();
        providers.put("testPatientProvider", provider);
        when(applicationContext.getBeansWithAnnotation(RelationProvider.class)).thenReturn(providers);
        when(applicationContext.getBean("testPatientProvider")).thenReturn(provider);
        
        Map<String, Object> beans = new HashMap<>();
        beans.put("testPatientProvider", provider);
        when(applicationContext.getBeansOfType(Object.class)).thenReturn(beans);

        // Initialize
        relationManager.afterPropertiesSet();

        // When
        relationManager.processRelations(referrals);

        // Then
        for (TestReferralDTO referral : referrals) {
            assertNotNull(referral.getPatientInfo());
            assertTrue(referral.getPatientInfo() instanceof TestPatientInfo);
        }
        
        // 验证批量加载被调用（应该只调用一次，因为支持批量）
        assertEquals(1, provider.getBatchLoadCallCount());
        assertEquals(2, provider.getLastBatchLoadIds().size()); // 去重后只有2个不同的ID
    }

    private TestReferralDTO createTestReferral(String id, String patientId) {
        TestReferralDTO referral = new TestReferralDTO();
        referral.setId(id);
        referral.setPatientId(patientId);
        return referral;
    }

    // ==================== 测试用的内部类 ====================

    @Data
    static class TestReferralDTO {
        private String id;
        
        @EntityRelation(
            entityType = "patient",
            type = EntityRelation.RelationType.MANY_TO_ONE
        )
        private String patientId;
        
        private Object patientInfo;
    }

    @Data
    static class TestPatientInfo {
        private String id;
        private String name;
        private String phone;
        
        public TestPatientInfo(String id, String name) {
            this.id = id;
            this.name = name;
        }
    }

    @RelationProvider(entityTypes = {"patient"})
    static class TestPatientProvider {
        private int batchLoadCallCount = 0;
        private List<String> lastBatchLoadIds = new ArrayList<>();

        @RelationLoader(
            entityTypes = {"patient"},
            priority = 0,
            supportBatch = true
        )
        public Map<String, TestPatientInfo> batchLoadPatients(List<String> patientIds) {
            batchLoadCallCount++;
            lastBatchLoadIds = new ArrayList<>(patientIds);
            
            Map<String, TestPatientInfo> result = new HashMap<>();
            for (String id : patientIds) {
                result.put(id, new TestPatientInfo(id, "患者" + id.substring(id.length() - 1)));
            }
            return result;
        }

        @RelationLoader(
            entityTypes = {"patient"},
            priority = 1,
            supportBatch = false
        )
        public TestPatientInfo loadPatient(String patientId) {
            return new TestPatientInfo(patientId, "张三");
        }

        public int getBatchLoadCallCount() {
            return batchLoadCallCount;
        }

        public List<String> getLastBatchLoadIds() {
            return lastBatchLoadIds;
        }
    }
}
