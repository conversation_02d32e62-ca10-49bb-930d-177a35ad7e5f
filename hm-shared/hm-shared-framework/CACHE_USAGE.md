# 缓存机制使用指南

## 概述

hm-shared-framework 现已集成了完整的缓存机制，支持本地缓存（Caffeine）和分布式缓存（Redis），为应用提供高性能的数据访问能力。

## 功能特性

### 1. 多种缓存类型支持
- **Caffeine**：高性能本地缓存，适合单机应用
- **Redis**：分布式缓存，适合集群环境

### 2. 自动缓存管理
- 实体查询自动缓存
- 列表查询自动缓存
- 分页查询自动缓存
- 统计查询自动缓存
- 增删改操作自动清除相关缓存

### 3. 缓存统计监控
- 缓存命中率统计
- 缓存大小监控
- 缓存性能分析

## 配置说明

### 1. 基础配置

在 `application.yml` 中添加缓存配置：

```yaml
hm:
  framework:
    cache:
      type: caffeine  # 或 redis
      enabled: true
      caffeine:
        maximum-size: 10000
        expire-after-write-minutes: 30
        expire-after-access-minutes: 10
```

### 2. Caffeine 本地缓存配置

```yaml
hm:
  framework:
    cache:
      type: caffeine
      caffeine:
        maximum-size: 10000              # 最大缓存条目数
        expire-after-write-minutes: 30   # 写入后过期时间
        expire-after-access-minutes: 10  # 访问后过期时间
        initial-capacity: 100            # 初始容量
        record-stats: true               # 启用统计
```

### 3. Redis 分布式缓存配置

```yaml
hm:
  framework:
    cache:
      type: redis
      redis:
        default-ttl-minutes: 30
        ttl-configs:
          entities: 60
          entity-lists: 30
          entity-pages: 15
        key-prefix: "hm:framework:cache:"

spring:
  redis:
    host: localhost
    port: 6379
    database: 0
```

## 使用方式

### 1. 自动缓存（推荐）

继承 `BaseServiceImpl` 的服务类会自动获得缓存功能：

```java
@Service
public class UserServiceImpl extends BaseServiceImpl<User, Long> implements UserService {
    
    // 查询方法自动缓存
    // 增删改方法自动清除缓存
}
```

### 2. 手动缓存控制

```java
@Service
public class CustomService {
    
    @Autowired
    private CacheUtils cacheUtils;
    
    public void manualCacheOperation() {
        // 获取缓存值
        Optional<User> user = cacheUtils.get("entities", "user:1", User.class);
        
        // 设置缓存值
        cacheUtils.put("entities", "user:1", userObject);
        
        // 清除缓存
        cacheUtils.evict("entities", "user:1");
        
        // 清空所有缓存
        cacheUtils.clearAll();
    }
}
```

### 3. 自定义缓存注解

```java
@Service
public class CustomService {
    
    @EntityCache(key = "#id")
    public User findUserById(Long id) {
        // 查询逻辑
    }
    
    @EntityCacheEvict(key = "#user.id")
    public User updateUser(User user) {
        // 更新逻辑
    }
}
```

## 缓存策略

### 1. 缓存分类

| 缓存名称 | 用途 | 默认TTL | 说明 |
|---------|------|---------|------|
| entities | 单个实体缓存 | 60分钟 | 根据ID缓存实体对象 |
| entity-lists | 实体列表缓存 | 30分钟 | 缓存查询结果列表 |
| entity-pages | 分页查询缓存 | 15分钟 | 缓存分页查询结果 |
| entity-counts | 统计查询缓存 | 60分钟 | 缓存统计查询结果 |
| query-results | 动态查询缓存 | 20分钟 | 缓存动态条件查询结果 |

### 2. 缓存键策略

- **实体缓存**：`实体名::ID`
- **列表缓存**：`实体名::list::条件哈希`
- **分页缓存**：`实体名::page::页码::大小::条件哈希`
- **统计缓存**：`实体名::count::条件哈希`

### 3. 缓存清除策略

- **新增操作**：清除列表、分页、统计缓存
- **更新操作**：清除实体、列表、分页、统计缓存
- **删除操作**：清除所有相关缓存

## 监控和统计

### 1. 获取缓存统计信息

```java
@Autowired
private CacheStatsService cacheStatsService;

public void printStats() {
    // 打印所有缓存统计信息
    cacheStatsService.printCacheStats();
    
    // 获取特定缓存统计
    CacheStatistics stats = cacheStatsService.getCacheStats("entities");
    System.out.println("命中率: " + stats.getHitRate());
}
```

### 2. 缓存管理

```java
@Autowired
private BaseService<User, Long> userService;

public void manageCaches() {
    // 刷新缓存
    userService.refreshCache();
    
    // 清空缓存
    userService.clearCache();
}
```

## 性能优化建议

### 1. 缓存大小调优
- 根据应用内存情况调整 `maximum-size`
- 监控缓存命中率，调整过期时间

### 2. 缓存策略选择
- 单机应用推荐使用 Caffeine
- 集群环境推荐使用 Redis
- 读多写少的场景适合长时间缓存

### 3. 避免缓存穿透
- 对于可能不存在的数据，考虑缓存空值
- 使用布隆过滤器预判数据存在性

## 注意事项

1. **事务一致性**：缓存清除在事务提交后执行
2. **序列化问题**：确保缓存对象可序列化
3. **内存管理**：合理设置缓存大小，避免内存溢出
4. **缓存预热**：应用启动时可预加载热点数据
5. **监控告警**：建议监控缓存命中率和性能指标

## 故障排查

### 1. 缓存不生效
- 检查 `@EnableCaching` 注解
- 确认缓存配置正确
- 验证方法是否被代理调用

### 2. 缓存命中率低
- 检查缓存键是否正确
- 调整过期时间配置
- 分析查询模式

### 3. 内存占用过高
- 减少缓存大小限制
- 缩短过期时间
- 检查是否有内存泄漏
