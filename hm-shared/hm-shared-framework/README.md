# HM 共享框架模块

## 概述

HM 共享框架模块提供了一套完整的基础架构，用于快速构建具有 CRUD 操作、动态查询和分页功能的业务模块。该框架基于 Spring Boot 3.5.3 和 JPA，采用领域驱动设计（DDD）架构。

## 核心特性

### 🚀 基础功能
- **抽象基类**: 提供 DAO、Service、Controller 三层的抽象基类
- **自动 CRUD**: 继承基类即可获得完整的 CRUD 操作
- **分页支持**: 内置分页查询功能，支持性能优化
- **软删除**: 支持逻辑删除和恢复功能

### 🔍 动态查询
- **灵活条件**: 支持多种查询操作符（EQ、LIKE、GT、LT、IN 等）
- **参数解析**: 自动解析 HTTP 请求参数为查询条件
- **嵌套字段**: 支持关联对象的字段查询
- **类型转换**: 自动进行数据类型转换

### 📊 排序功能
- **多字段排序**: 支持多个字段的组合排序
- **灵活语法**: 使用 `+field1,-field2` 语法指定排序
- **默认排序**: 可配置默认排序规则

### ⚡ 性能优化
- **懒加载**: 支持不查询总数的分页（提升性能）
- **批量操作**: 提供批量增删改操作
- **智能缓存**: 内置多级缓存机制，支持本地缓存和分布式缓存

### 🚀 缓存机制
- **多种缓存类型**: 支持 Caffeine（本地）和 Redis（分布式）
- **自动缓存管理**: 查询自动缓存，增删改自动清除
- **缓存统计监控**: 提供缓存命中率和性能统计
- **灵活配置**: 支持不同缓存策略和过期时间配置

### 📡 领域事件机制
- **完整事件类型**: 支持实体创建、更新、删除事件
- **同步异步处理**: 支持事务内同步和事务后异步处理
- **自动事件发布**: 继承基类自动发布实体事件
- **事件统计监控**: 提供事件处理统计和性能监控

## 快速开始

### 1. 创建实体类

```java
@Entity
@Table(name = "my_entity")
@Data
@EqualsAndHashCode(callSuper = true)
public class MyEntity extends BaseEntity<String> {
    
    @Id
    @Column(length = 32)
    private String id;
    
    @Column(length = 100, nullable = false)
    private String name;
    
    @Column(length = 500)
    private String description;
    
    @Override
    public String getId() {
        return id;
    }
    
    @Override
    public void setId(String id) {
        this.id = id;
    }
}
```

### 2. 创建仓储接口

```java
@Repository
public interface MyEntityRepository extends BaseRepository<MyEntity, String> {
    // 可以添加自定义查询方法
    List<MyEntity> findByName(String name);
}
```

### 3. 创建服务接口和实现

```java
public interface MyEntityService extends BaseService<MyEntity, String> {
    // 可以添加自定义业务方法
}

@Service
public class MyEntityServiceImpl extends BaseServiceImpl<MyEntity, String> 
        implements MyEntityService {
    
    @Override
    public void validate(MyEntity entity) {
        // 自定义验证逻辑
        Assert.hasText(entity.getName(), "名称不能为空");
    }
}
```

### 4. 创建控制器

```java
@RestController
@RequestMapping("/api/my-entities")
public class MyEntityController extends BaseController<MyEntity, String> {
    
    @Override
    protected String getEntityId(MyEntity entity) {
        return entity.getId();
    }
    
    @Override
    protected void setEntityId(MyEntity entity, String id) {
        entity.setId(id);
    }
}
```

## 动态查询使用指南

### 支持的查询操作符

| 操作符 | 说明 | 示例 |
|--------|------|------|
| `fieldName` 或 `fieldName_EQ` | 等于 | `name=张三` |
| `fieldName_LIKE` | 模糊查询 | `name_LIKE=张` |
| `fieldName_GT` | 大于 | `age_GT=18` |
| `fieldName_LT` | 小于 | `age_LT=60` |
| `fieldName_GTE` | 大于等于 | `score_GTE=80` |
| `fieldName_LTE` | 小于等于 | `score_LTE=100` |
| `fieldName_IN` | 在列表中 | `status_IN=1,2,3` |
| `fieldName_NOT_NULL` | 不为空 | `email_NOT_NULL=true` |
| `fieldName_IS_NULL` | 为空 | `deletedAt_IS_NULL=true` |

### 查询示例

```bash
# 基本查询
GET /api/my-entities?name=张三&status=1

# 模糊查询
GET /api/my-entities?name_LIKE=张&description_LIKE=测试

# 范围查询
GET /api/my-entities?createTime_GT=2025-01-01&createTime_LT=2025-12-31

# 列表查询
GET /api/my-entities?status_IN=1,2,3

# 分页和排序
GET /api/my-entities?page=1&size=20&orders=-createTime,+name

# 复合查询
GET /api/my-entities?name_LIKE=张&status=1&createTime_GT=2025-01-01&page=1&size=10&orders=-createTime
```

## 排序语法

### 排序格式
- `+fieldName`: 升序排序
- `-fieldName`: 降序排序
- `fieldName`: 默认升序排序

### 多字段排序
使用逗号分隔多个排序字段：
```
orders=+age,-name,createTime
```
表示：按年龄升序，然后按姓名降序，最后按创建时间升序

## 分页功能

### 分页参数
- `page`: 页码（从 1 开始）
- `size`: 每页大小（默认 20，最大 1000）
- `orders`: 排序字符串

### 分页响应格式
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "content": [...],
    "page": 1,
    "size": 20,
    "total": 100,
    "totalPages": 5,
    "first": true,
    "last": false,
    "hasNext": true,
    "hasPrevious": false,
    "numberOfElements": 20,
    "empty": false
  }
}
```

## 业务逻辑钩子

框架提供了多个业务逻辑钩子方法，可以在服务实现类中重写：

```java
@Override
public void validate(MyEntity entity) {
    // 数据验证逻辑
}

@Override
public void beforeSave(MyEntity entity) {
    // 保存前处理
}

@Override
public void afterSave(MyEntity entity) {
    // 保存后处理
}

@Override
public void beforeUpdate(MyEntity entity) {
    // 更新前处理
}

@Override
public void afterUpdate(MyEntity entity) {
    // 更新后处理
}

@Override
public void beforeDelete(String id) {
    // 删除前处理
}

@Override
public void afterDelete(String id) {
    // 删除后处理
}
```

## 配置选项

在 `application.yml` 中可以配置框架参数：

```yaml
hm:
  framework:
    # 缓存配置
    cache:
      type: caffeine              # 缓存类型：caffeine 或 redis
      enabled: true               # 是否启用缓存
      caffeine:
        maximum-size: 10000       # 最大缓存条目数
        expire-after-write-minutes: 30   # 写入后过期时间
        expire-after-access-minutes: 10  # 访问后过期时间
      redis:
        default-ttl-minutes: 30   # 默认TTL
        key-prefix: "hm:framework:cache:"  # 缓存键前缀

    # 事件配置
    event:
      enabled: true               # 是否启用事件机制
      enable-stats: true          # 是否启用事件统计
      async:
        core-pool-size: 2         # 异步处理核心线程数
        max-pool-size: 10         # 异步处理最大线程数
        queue-capacity: 100       # 异步处理队列容量

    # 分页配置
    page:
      default-size: 20          # 默认页面大小
      max-size: 1000           # 最大页面大小
      default-need-total: true  # 是否默认需要总数统计

    # 查询配置
    query:
      enable-dynamic-query: true      # 是否启用动态查询
      ignore-empty-conditions: true   # 是否忽略空值查询条件
      max-conditions: 50              # 最大查询条件数量
      default-sort-field: "createTime" # 默认排序字段
      default-sort-direction: "DESC"   # 默认排序方向

    # 日志配置
    log:
      enable-sql-log: false           # 是否启用SQL日志
      enable-performance-log: true    # 是否启用性能日志
      slow-query-threshold: 1000      # 慢查询阈值（毫秒）
```

## 异常处理

框架提供了统一的异常处理：

- `FrameworkException`: 框架基础异常
- `EntityNotFoundException`: 实体未找到异常

## 示例项目

查看 `example` 包中的完整示例：
- `ExampleEntity`: 示例实体类
- `ExampleRepository`: 示例仓储接口
- `ExampleService`: 示例服务接口
- `ExampleServiceImpl`: 示例服务实现
- `ExampleController`: 示例控制器

## 最佳实践

### 1. 实体设计
- 继承 `BaseEntity` 获得审计字段
- 使用 JPA 注解进行映射
- 重写 `getId()` 和 `setId()` 方法

### 2. 仓储设计
- 继承 `BaseRepository` 获得基础功能
- 添加业务特定的查询方法
- 使用 `@Query` 注解编写复杂查询

### 3. 服务设计
- 继承 `BaseServiceImpl` 获得基础实现
- 重写业务逻辑钩子方法
- 添加业务特定的方法

### 4. 控制器设计
- 继承 `BaseController` 获得标准 REST API
- 实现抽象方法 `getEntityId()` 和 `setEntityId()`
- 添加业务特定的端点

### 5. 性能优化
- 对于大数据量查询，使用 `needTotal=false` 跳过总数统计
- 合理使用索引和查询条件
- 避免 N+1 查询问题

## 技术栈

- **Spring Boot**: 3.5.3
- **Spring Data JPA**: 3.5.3
- **Hibernate**: 6.x
- **Java**: 21
- **Lombok**: 1.18.38
- **Swagger**: 3.x

## 缓存使用指南

详细的缓存使用说明请参考：[缓存机制使用指南](CACHE_USAGE.md)

### 快速启用缓存

1. **添加缓存配置**：
```yaml
hm:
  framework:
    cache:
      enabled: true
      type: caffeine
```

2. **自动缓存**：继承 `BaseServiceImpl` 即可自动获得缓存功能

3. **手动缓存控制**：
```java
@Autowired
private CacheUtils cacheUtils;

// 获取缓存
Optional<User> user = cacheUtils.get("entities", "user:1", User.class);

// 清除缓存
cacheUtils.evict("entities", "user:1");
```

## 领域事件使用指南

详细的事件使用说明请参考：[领域事件机制使用指南](DOMAIN_EVENTS_USAGE.md)

### 快速启用事件

1. **添加事件配置**：
```yaml
hm:
  framework:
    event:
      enabled: true
      enable-stats: true
```

2. **自动事件发布**：继承 `BaseServiceImpl` 即可自动发布实体事件

3. **事件监听处理**：
```java
@Component
public class UserEventListener extends DomainEventListener {

    @Override
    protected void onEntityCreated(EntityCreatedEvent<?, ?> event) {
        if ("User".equals(event.getAggregateType())) {
            // 处理用户创建事件
        }
    }
}
```

## 更新日志

### v1.2.0 (2025-07-30)
- 📡 **新增领域事件机制**：集成完整的领域事件系统
- 🔄 **同步异步处理**：支持事务内同步和事务后异步事件处理
- 📊 **事件统计监控**：提供事件处理统计和性能监控
- 🎯 **自动事件发布**：继承基类自动发布实体相关事件
- 📝 **完善文档**：新增领域事件使用指南和最佳实践

### v1.1.0 (2025-07-30)
- 🚀 **新增缓存机制**：集成 Caffeine 和 Redis 缓存支持
- 📊 **缓存监控**：提供缓存统计和性能监控功能
- 🎯 **自动缓存管理**：查询自动缓存，增删改自动清除相关缓存
- 📝 **完善文档**：新增缓存使用指南和配置说明

### v1.0.0 (2025-07-29)
- 初始版本发布
- 提供基础的 DAO、Service、Controller 抽象类
- 支持动态查询和分页功能
- 包含完整的示例代码和文档
