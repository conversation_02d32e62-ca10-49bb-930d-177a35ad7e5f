package com.hys.hm.shared.encrypt.service.impl;

import com.hys.hm.shared.encrypt.annotation.EncryptField;
import com.hys.hm.shared.encrypt.service.EncryptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

/**
 * 加密服务实现
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
@Slf4j
@Service
public class EncryptServiceImpl implements EncryptService {

    @Value("${app.security.encrypt.aes-key:hm-default-aes-key-32-characters}")
    private String aesKey;

    @Value("${app.security.encrypt.sm4-key:hm-default-sm4-key-16-chars}")
    private String sm4Key;

    @Value("${app.security.encrypt.salt:hm-default-salt}")
    private String salt;

    private static final String AES_ALGORITHM = "AES/ECB/PKCS5Padding";
    private static final String HASH_ALGORITHM = "SHA-256";

    @Override
    public String encrypt(String plainText, EncryptField.EncryptType type) {
        if (plainText == null || plainText.isEmpty()) {
            return plainText;
        }

        try {
            switch (type) {
                case AES:
                    return encryptAES(plainText);
                case SM4:
                    return encryptSM4(plainText);
                default:
                    throw new IllegalArgumentException("不支持的加密类型: " + type);
            }
        } catch (Exception e) {
            log.error("加密失败: {}", e.getMessage(), e);
            throw new RuntimeException("加密失败", e);
        }
    }

    @Override
    public String decrypt(String cipherText, EncryptField.EncryptType type) {
        if (cipherText == null || cipherText.isEmpty()) {
            return cipherText;
        }

        try {
            switch (type) {
                case AES:
                    return decryptAES(cipherText);
                case SM4:
                    return decryptSM4(cipherText);
                default:
                    throw new IllegalArgumentException("不支持的加密类型: " + type);
            }
        } catch (Exception e) {
            log.error("解密失败: {}", e.getMessage(), e);
            throw new RuntimeException("解密失败", e);
        }
    }

    @Override
    public List<String> generateFuzzyTokens(String plainText, int tokenLength) {
        List<String> tokens = new ArrayList<>();

        if (plainText == null || plainText.length() < tokenLength) {
            return tokens;
        }

        // 生成所有可能的分词
        for (int i = 0; i <= plainText.length() - tokenLength; i++) {
            String token = plainText.substring(i, i + tokenLength);
            String hashedToken = hashString(token);
            tokens.add(hashedToken);
        }

        return tokens;
    }

    @Override
    public String generateExactHash(String plainText) {
        return hashString(plainText);
    }

    @Override
    public boolean verifyHash(String plainText, String hash) {
        return hashString(plainText).equals(hash);
    }

    /**
     * AES加密
     */
    private String encryptAES(String plainText) throws Exception {
        SecretKeySpec keySpec = new SecretKeySpec(getAESKey(), "AES");
        Cipher cipher = Cipher.getInstance(AES_ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, keySpec);

        byte[] encrypted = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encrypted);
    }

    /**
     * AES解密
     */
    private String decryptAES(String cipherText) throws Exception {
        SecretKeySpec keySpec = new SecretKeySpec(getAESKey(), "AES");
        Cipher cipher = Cipher.getInstance(AES_ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, keySpec);
        byte[] decrypted = cipher.doFinal(Base64.getDecoder().decode(cipherText));
        return new String(decrypted, StandardCharsets.UTF_8);
    }

    /**
     * SM4加密（简化实现，实际项目中应使用专业的国密库）
     */
    private String encryptSM4(String plainText) throws Exception {
        // 这里简化为AES实现，实际项目中应使用国密SM4算法
        log.warn("SM4加密暂时使用AES实现，生产环境请使用专业的国密库");
        return encryptAES(plainText);
    }

    /**
     * SM4解密（简化实现）
     */
    private String decryptSM4(String cipherText) throws Exception {
        // 这里简化为AES实现，实际项目中应使用国密SM4算法
        log.warn("SM4解密暂时使用AES实现，生产环境请使用专业的国密库");
        return decryptAES(cipherText);
    }

    /**
     * 生成哈希值
     */
    private String hashString(String input) {
        try {
            MessageDigest digest = MessageDigest.getInstance(HASH_ALGORITHM);
            digest.update(salt.getBytes(StandardCharsets.UTF_8));
            byte[] hash = digest.digest(input.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hash);
        } catch (Exception e) {
            log.error("生成哈希失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成哈希失败", e);
        }
    }

    /**
     * 获取AES密钥
     */
    private byte[] getAESKey() {
        // 确保密钥长度为32字节（256位）
        String key = aesKey;
        if (key.length() > 32) {
            key = key.substring(0, 32);
        } else if (key.length() < 32) {
            key = String.format("%-32s", key).replace(' ', '0');
        }
        return key.getBytes(StandardCharsets.UTF_8);
    }
}
