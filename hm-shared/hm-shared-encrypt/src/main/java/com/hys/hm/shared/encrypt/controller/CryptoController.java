package com.hys.hm.shared.encrypt.controller;

import com.hys.hm.shared.encrypt.util.CryptoKeyGenerator;
import com.hys.hm.shared.encrypt.util.FrontendCryptoUtil;
import com.hys.hm.shared.common.Result;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 加密工具控制器
 * 提供加密相关的API接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Slf4j
@RestController
@RequestMapping("/api/crypto")
@ConditionalOnProperty(name = "app.security.frontend.enabled", havingValue = "true")
public class CryptoController {

    @Resource
    private FrontendCryptoUtil frontendCryptoUtil;

    /**
     * 获取加密能力信息
     */
    @GetMapping("/capabilities")
    public Result<Map<String, Object>> getCryptoCapabilities() {
        try {
            Map<String, Object> capabilities = new HashMap<>();
            capabilities.put("supported", frontendCryptoUtil.checkCryptoCapabilities());
            capabilities.put("statistics", frontendCryptoUtil.getCryptoStatistics());

            // 获取所有算法的强度信息
            Map<String, String> algorithms = new HashMap<>();
            for (FrontendCryptoUtil.CryptoAlgorithm algorithm : FrontendCryptoUtil.CryptoAlgorithm.values()) {
                algorithms.put(algorithm.getCode(), frontendCryptoUtil.getAlgorithmStrength(algorithm));
            }
            capabilities.put("algorithms", algorithms);

            return Result.success(capabilities);
        } catch (Exception e) {
            log.error("获取加密能力信息失败: {}", e.getMessage(), e);
            return Result.error("获取加密能力信息失败");
        }
    }

    /**
     * 生成新的AES密钥
     */
    @PostMapping("/keys/aes")
    public Result<Map<String, String>> generateAESKey(@RequestParam String keyId) {
        try {
            String aesKey = frontendCryptoUtil.generateNewAESKey(keyId);
            Map<String, String> result = new HashMap<>();
            result.put("keyId", keyId);
            result.put("aesKey", aesKey);
            result.put("algorithm", "AES-256");

            return Result.success(result);
        } catch (Exception e) {
            log.error("生成AES密钥失败: {}", e.getMessage(), e);
            return Result.error("生成AES密钥失败");
        }
    }

    /**
     * 生成RSA密钥对
     */
    @PostMapping("/keys/rsa")
    public Result<Map<String, String>> generateRSAKeyPair() {
        try {
            String[] keyPair = frontendCryptoUtil.generateRSAKeyPair();
            Map<String, String> result = new HashMap<>();
            result.put("publicKey", keyPair[0]);
            result.put("privateKey", keyPair[1]);
            result.put("algorithm", "RSA-2048");

            return Result.success(result);
        } catch (Exception e) {
            log.error("生成RSA密钥对失败: {}", e.getMessage(), e);
            return Result.error("生成RSA密钥对失败");
        }
    }

    /**
     * AES加密数据
     */
    @PostMapping("/encrypt/aes")
    public Result<FrontendCryptoUtil.EncryptedData> encryptWithAES(
            @RequestParam String data,
            @RequestParam String keyId) {
        try {
            FrontendCryptoUtil.EncryptedData encryptedData =
                frontendCryptoUtil.encryptWithAES(data, keyId);
            return Result.success(encryptedData);
        } catch (Exception e) {
            log.error("AES加密失败: {}", e.getMessage(), e);
            return Result.error("AES加密失败");
        }
    }

    /**
     * AES解密数据
     */
    @PostMapping("/decrypt/aes")
    public Result<String> decryptWithAES(
            @RequestBody FrontendCryptoUtil.EncryptedData encryptedData,
            @RequestParam String keyId) {
        try {
            String decryptedData = frontendCryptoUtil.decryptWithAES(encryptedData, keyId);
            return Result.success(decryptedData);
        } catch (Exception e) {
            log.error("AES解密失败: {}", e.getMessage(), e);
            return Result.error("AES解密失败");
        }
    }

    /**
     * RSA加密数据
     */
    @PostMapping("/encrypt/rsa")
    public Result<String> encryptWithRSA(@RequestParam String data) {
        try {
            String encryptedData = frontendCryptoUtil.encryptWithRSA(data);
            return Result.success(encryptedData);
        } catch (Exception e) {
            log.error("RSA加密失败: {}", e.getMessage(), e);
            return Result.error("RSA加密失败");
        }
    }

    /**
     * RSA解密数据
     */
    @PostMapping("/decrypt/rsa")
    public Result<String> decryptWithRSA(@RequestParam String encryptedData) {
        try {
            String decryptedData = frontendCryptoUtil.decryptWithRSA(encryptedData);
            return Result.success(decryptedData);
        } catch (Exception e) {
            log.error("RSA解密失败: {}", e.getMessage(), e);
            return Result.error("RSA解密失败");
        }
    }

    /**
     * 混合加密数据
     */
    @PostMapping("/encrypt/hybrid")
    public Result<FrontendCryptoUtil.HybridEncryptedData> hybridEncrypt(@RequestParam String data) {
        try {
            FrontendCryptoUtil.HybridEncryptedData hybridData =
                frontendCryptoUtil.hybridEncrypt(data);
            return Result.success(hybridData);
        } catch (Exception e) {
            log.error("混合加密失败: {}", e.getMessage(), e);
            return Result.error("混合加密失败");
        }
    }

    /**
     * 混合解密数据
     */
    @PostMapping("/decrypt/hybrid")
    public Result<String> hybridDecrypt(@RequestBody FrontendCryptoUtil.HybridEncryptedData hybridData) {
        try {
            String decryptedData = frontendCryptoUtil.hybridDecrypt(hybridData);
            return Result.success(decryptedData);
        } catch (Exception e) {
            log.error("混合解密失败: {}", e.getMessage(), e);
            return Result.error("混合解密失败");
        }
    }

    /**
     * 验证数据完整性
     */
    @PostMapping("/verify")
    public Result<Boolean> verifyDataIntegrity(
            @RequestParam String data,
            @RequestParam String signature) {
        try {
            boolean isValid = frontendCryptoUtil.verifyDataIntegrity(data, signature);
            return Result.success(isValid);
        } catch (Exception e) {
            log.error("数据完整性验证失败: {}", e.getMessage(), e);
            return Result.error("数据完整性验证失败");
        }
    }

    /**
     * 密钥轮换
     */
    @PostMapping("/keys/rotate")
    public Result<String> rotateKey(@RequestParam String keyId) {
        try {
            frontendCryptoUtil.rotateKey(keyId);
            return Result.success("密钥轮换成功");
        } catch (Exception e) {
            log.error("密钥轮换失败: {}", e.getMessage(), e);
            return Result.error("密钥轮换失败");
        }
    }

    /**
     * 清理过期密钥
     */
    @PostMapping("/keys/cleanup")
    public Result<String> cleanupExpiredKeys() {
        try {
            frontendCryptoUtil.cleanupExpiredKeys();
            return Result.success("清理过期密钥成功");
        } catch (Exception e) {
            log.error("清理过期密钥失败: {}", e.getMessage(), e);
            return Result.error("清理过期密钥失败");
        }
    }

    /**
     * 生成完整的加密配置（仅开发环境使用）
     */
    @PostMapping("/config/generate")
    @ConditionalOnProperty(name = "spring.profiles.active", havingValue = "dev")
    public Result<Map<String, Object>> generateCryptoConfig() {
        try {
            CryptoKeyGenerator.CryptoConfig config = CryptoKeyGenerator.generateFullCryptoConfig();

            Map<String, Object> result = new HashMap<>();
            result.put("config", config);
            result.put("yamlConfig", config.toYamlConfig());
            result.put("envVars", config.toEnvironmentVariables());
            result.put("warning", "此配置包含敏感信息，请妥善保管！仅在开发环境使用！");

            return Result.success(result);
        } catch (Exception e) {
            log.error("生成加密配置失败: {}", e.getMessage(), e);
            return Result.error("生成加密配置失败");
        }
    }
}
