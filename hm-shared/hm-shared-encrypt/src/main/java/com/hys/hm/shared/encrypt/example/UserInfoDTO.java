package com.hys.hm.shared.encrypt.example;

import com.hys.hm.shared.encrypt.annotation.EncryptField;
import lombok.Data;

/**
 * 用户信息DTO示例
 * 演示脱敏和加密功能
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Data
public class UserInfoDTO {

    /**
     * 用户ID（不需要加密）
     */
    private Long userId;

    /**
     * 用户名（不需要加密）
     */
    private String username;

    /**
     * 手机号（需要脱敏和加密）
     */
    @EncryptField(
        type = EncryptField.EncryptType.AES,
        maskLevel = EncryptField.MaskLevel.PARTIAL,
        description = "用户手机号"
    )
    private String mobile;

    /**
     * 身份证号（需要脱敏和加密）
     */
    @EncryptField(
        type = EncryptField.EncryptType.AES,
        maskLevel = EncryptField.MaskLevel.PARTIAL,
        description = "用户身份证号"
    )
    private String idCard;

    /**
     * 邮箱（需要脱敏和加密）
     */
    @EncryptField(
        type = EncryptField.EncryptType.AES,
        maskLevel = EncryptField.MaskLevel.PARTIAL,
        description = "用户邮箱"
    )
    private String email;

    /**
     * 银行卡号（需要脱敏和加密，使用国密算法）
     */
    @EncryptField(
        type = EncryptField.EncryptType.SM4,
        maskLevel = EncryptField.MaskLevel.PARTIAL,
        description = "用户银行卡号"
    )
    private String bankCard;

    /**
     * 真实姓名（需要脱敏和加密）
     */
    @EncryptField(
        type = EncryptField.EncryptType.AES,
        maskLevel = EncryptField.MaskLevel.FIRST_LAST,
        description = "用户真实姓名"
    )
    private String realName;

    /**
     * 地址（需要脱敏和加密）
     */
    @EncryptField(
        type = EncryptField.EncryptType.AES,
        maskLevel = EncryptField.MaskLevel.PARTIAL,
        description = "用户地址"
    )
    private String address;

    /**
     * 敏感备注（完全隐藏）
     */
    @EncryptField(
        type = EncryptField.EncryptType.AES,
        maskLevel = EncryptField.MaskLevel.FULL,
        hideInResult = true,
        description = "敏感备注信息"
    )
    private String sensitiveNote;

    /**
     * 创建时间（不需要加密）
     */
    private String createTime;

    /**
     * 更新时间（不需要加密）
     */
    private String updateTime;

    /**
     * 构造函数
     */
    public UserInfoDTO() {}

    /**
     * 构造函数
     */
    public UserInfoDTO(Long userId, String username, String mobile, String idCard, 
                      String email, String bankCard, String realName, String address, 
                      String sensitiveNote, String createTime, String updateTime) {
        this.userId = userId;
        this.username = username;
        this.mobile = mobile;
        this.idCard = idCard;
        this.email = email;
        this.bankCard = bankCard;
        this.realName = realName;
        this.address = address;
        this.sensitiveNote = sensitiveNote;
        this.createTime = createTime;
        this.updateTime = updateTime;
    }

    /**
     * 创建示例数据
     */
    public static UserInfoDTO createSampleData() {
        return new UserInfoDTO(
            1001L,
            "zhangsan",
            "***********",
            "110101199001011234",
            "<EMAIL>",
            "6222021234567890123",
            "张三",
            "北京市朝阳区某某街道123号",
            "这是敏感的备注信息，包含重要隐私",
            "2025-07-31 10:00:00",
            "2025-07-31 15:30:00"
        );
    }

    @Override
    public String toString() {
        return String.format(
            "UserInfoDTO{userId=%d, username='%s', mobile='%s', idCard='%s', " +
            "email='%s', bankCard='%s', realName='%s', address='%s', " +
            "sensitiveNote='%s', createTime='%s', updateTime='%s'}",
            userId, username, mobile, idCard, email, bankCard, realName, 
            address, sensitiveNote, createTime, updateTime
        );
    }
}
