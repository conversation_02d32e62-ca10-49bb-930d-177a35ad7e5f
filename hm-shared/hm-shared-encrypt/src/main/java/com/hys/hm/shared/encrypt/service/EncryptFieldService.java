package com.hys.hm.shared.encrypt.service;

import com.hys.hm.shared.encrypt.annotation.EncryptField;
import jakarta.transaction.Transactional;

import java.util.List;

public interface EncryptFieldService {
    @Transactional
    void createSearchIndex(Object entity, String entityId);

    @Transactional
    void deleteSearchIndex(String entityType, String entityId);

    List<String> findByExactMatch(String entityType, String fieldName, String plainText);

    List<String> findByFuzzyMatch(String entityType, String fieldName, String plainText, int tokenLength);

    void createFieldIndex(String entityType, String entityId, String fieldName,
                          String plainText, EncryptField encryptAnnotation);
}
