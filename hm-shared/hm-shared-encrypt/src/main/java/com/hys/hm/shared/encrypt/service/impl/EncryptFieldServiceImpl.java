package com.hys.hm.shared.encrypt.service.impl;

import com.hys.hm.shared.encrypt.annotation.EncryptField;
import com.hys.hm.shared.encrypt.entity.EncryptSearchEntity;
import com.hys.hm.shared.encrypt.repository.EncryptSearchRepository;
import com.hys.hm.shared.encrypt.service.EncryptFieldService;
import com.hys.hm.shared.encrypt.service.EncryptService;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * 加密字段处理服务
 * 负责处理实体的加密字段索引
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class EncryptFieldServiceImpl implements EncryptFieldService {

    private final EncryptService encryptService;
    private final EncryptSearchRepository encryptSearchRepository;

    /**
     * 为实体创建搜索索引
     */
    @Transactional
    @Override
    public void createSearchIndex(Object entity, String entityId) {
        if (entity == null || entityId == null) {
            return;
        }

        String entityType = entity.getClass().getSimpleName();

        // 先删除旧的索引
        encryptSearchRepository.deleteByEntity(entityType, entityId);

        // 遍历所有字段，处理加密字段
        Field[] fields = entity.getClass().getDeclaredFields();
        for (Field field : fields) {
            EncryptField encryptAnnotation = field.getAnnotation(EncryptField.class);
            if (encryptAnnotation != null) {
                try {
                    field.setAccessible(true);
                    Object value = field.get(entity);

                    if (value instanceof String && !((String) value).isEmpty()) {
                        String plainText = (String) value;
                        createFieldIndex(entityType, entityId, field.getName(), plainText, encryptAnnotation);
                    }
                } catch (Exception e) {
                    log.error("创建字段索引失败: entity={}, field={}, error={}",
                             entityType, field.getName(), e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 删除实体的搜索索引
     */
    @Transactional
    @Override
    public void deleteSearchIndex(String entityType, String entityId) {
        encryptSearchRepository.deleteByEntity(entityType, entityId);
    }

    /**
     * 根据加密字段进行精确查询
     */
    @Override
    public List<String> findByExactMatch(String entityType, String fieldName, String plainText) {
        if (plainText == null || plainText.isEmpty()) {
            return new ArrayList<>();
        }

        String exactHash = encryptService.generateExactHash(plainText);
        return encryptSearchRepository.findEntityIdsByExactHash(entityType, fieldName, exactHash);
    }

    /**
     * 根据加密字段进行模糊查询
     */
    @Override
    public List<String> findByFuzzyMatch(String entityType, String fieldName, String plainText, int tokenLength) {
        if (plainText == null || plainText.isEmpty()) {
            return new ArrayList<>();
        }

        List<String> tokenHashes = encryptService.generateFuzzyTokens(plainText, tokenLength);
        if (tokenHashes.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取匹配的实体ID，按匹配度排序
        List<Object[]> results = encryptSearchRepository.findEntityIdsByTokenHashes(entityType, fieldName, tokenHashes);

        List<String> entityIds = new ArrayList<>();
        for (Object[] result : results) {
            entityIds.add((String) result[0]);
        }

        return entityIds;
    }

    /**
     * 为单个字段创建索引
     */
    @Override
    public void createFieldIndex(String entityType, String entityId, String fieldName,
                                 String plainText, EncryptField encryptAnnotation) {

        // 创建精确查询索引
        String exactHash = encryptService.generateExactHash(plainText);
        EncryptSearchEntity exactIndex = EncryptSearchEntity.create(
            entityType, entityId, fieldName, null, exactHash
        );
        encryptSearchRepository.save(exactIndex);

        // 如果支持模糊查询，创建分词索引
        if (encryptAnnotation.fuzzySearch()) {
            List<String> tokenHashes = encryptService.generateFuzzyTokens(plainText, encryptAnnotation.tokenLength());

            for (String tokenHash : tokenHashes) {
                EncryptSearchEntity tokenIndex = EncryptSearchEntity.create(
                    entityType, entityId, fieldName, tokenHash, null
                );
                encryptSearchRepository.save(tokenIndex);
            }
        }

        log.debug("创建字段索引成功: entity={}, field={}, fuzzySearch={}",
                 entityType, fieldName, encryptAnnotation.fuzzySearch());
    }
}
