package com.hys.hm.shared.encrypt.service;

import com.hys.hm.shared.encrypt.annotation.EncryptField;
import com.hys.hm.shared.encrypt.service.impl.EncryptFieldServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * 加密字段查询助手
 * 为BaseService提供加密字段查询支持
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Component
@Slf4j
public class EncryptFieldQueryHelper {

    @Autowired(required = false)
    private EncryptService encryptService;

    @Autowired(required = false)
    private EncryptFieldServiceImpl encryptFieldService;

    /**
     * 检查字段是否为加密字段
     */
    public boolean isEncryptField(Class<?> entityClass, String fieldName) {
        try {
            Field field = entityClass.getDeclaredField(fieldName);
            return field.isAnnotationPresent(EncryptField.class);
        } catch (Exception e) {
            log.debug("检查加密字段失败: class={}, field={}", entityClass.getSimpleName(), fieldName);
            return false;
        }
    }

    /**
     * 根据加密字段精确查询实体ID列表
     */
    public List<String> findEntityIdsByEncryptedField(Class<?> entityClass, String fieldName, Object value) {
        if (!isEncryptField(entityClass, fieldName) || value == null || !StringUtils.hasText(value.toString())) {
            return new ArrayList<>();
        }

        try {
            if (encryptService == null || encryptFieldService == null) {
                log.debug("加密服务不可用，无法执行加密字段查询");
                return new ArrayList<>();
            }

            // 生成查询值的哈希
            String valueHash = encryptService.generateExactHash(value.toString());

            // 通过反射调用infrastructure层的查询方法
            Object result = invokeMethod(encryptFieldService, "findEntityIdsByFieldHash",
                                       entityClass.getSimpleName(), fieldName, valueHash);

            if (result instanceof List) {
                @SuppressWarnings("unchecked")
                List<String> entityIds = (List<String>) result;
                return entityIds;
            }

            return new ArrayList<>();

        } catch (Exception e) {
            log.error("加密字段精确查询失败: class={}, field={}, error={}",
                     entityClass.getSimpleName(), fieldName, e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 根据加密字段模糊查询实体ID列表
     */
    public List<String> findEntityIdsByEncryptedFieldLike(Class<?> entityClass, String fieldName, Object value) {
        if (!isEncryptField(entityClass, fieldName) || value == null || !StringUtils.hasText(value.toString())) {
            return new ArrayList<>();
        }

        try {
            if (encryptService == null || encryptFieldService == null) {
                log.debug("加密服务不可用，无法执行加密字段模糊查询");
                return new ArrayList<>();
            }

            // 检查字段是否支持模糊查询
            Field field = entityClass.getDeclaredField(fieldName);
            EncryptField encryptField = field.getAnnotation(EncryptField.class);

            if (!encryptField.fuzzySearch()) {
                log.debug("字段不支持模糊查询: class={}, field={}", entityClass.getSimpleName(), fieldName);
                return new ArrayList<>();
            }

            // 生成模糊查询分词
            List<String> fuzzyTokens = encryptService.generateFuzzyTokens(
                value.toString(),
                encryptField.tokenLength()
            );

            if (fuzzyTokens.isEmpty()) {
                return new ArrayList<>();
            }

            // 通过反射调用infrastructure层的模糊查询方法
            Object result = invokeMethod(encryptFieldService, "findEntityIdsByFuzzyTokens",
                                       entityClass.getSimpleName(), fieldName, fuzzyTokens);

            if (result instanceof List) {
                @SuppressWarnings("unchecked")
                List<String> entityIds = (List<String>) result;
                return entityIds;
            }

            return new ArrayList<>();

        } catch (Exception e) {
            log.error("加密字段模糊查询失败: class={}, field={}, error={}",
                     entityClass.getSimpleName(), fieldName, e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 检查加密服务是否可用
     */
    public boolean isEncryptServiceAvailable() {
        return encryptService != null && encryptFieldService != null;
    }

    /**
     * 获取实体类中的加密字段列表
     */
    public List<String> getEncryptFields(Class<?> entityClass) {
        List<String> encryptFields = new ArrayList<>();

        try {
            Field[] fields = entityClass.getDeclaredFields();
            for (Field field : fields) {
                if (field.isAnnotationPresent(EncryptField.class)) {
                    encryptFields.add(field.getName());
                }
            }
        } catch (Exception e) {
            log.debug("获取加密字段列表失败: class={}", entityClass.getSimpleName());
        }

        return encryptFields;
    }

    /**
     * 使用反射调用方法
     */
    private Object invokeMethod(Object target, String methodName, Object... args) throws Exception {
        Class<?> targetClass = target.getClass();
        Class<?>[] paramTypes = new Class[args.length];

        for (int i = 0; i < args.length; i++) {
            if (args[i] instanceof List) {
                paramTypes[i] = List.class;
            } else {
                paramTypes[i] = args[i] != null ? args[i].getClass() : String.class;
            }
        }

        Method method = targetClass.getMethod(methodName, paramTypes);
        return method.invoke(target, args);
    }
}
