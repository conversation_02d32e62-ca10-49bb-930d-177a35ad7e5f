package com.hys.hm.shared.encrypt.example;

import com.hys.hm.shared.encrypt.util.FrontendCryptoUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * 前后端交互加密工具使用示例
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Slf4j
@Component
public class FrontendCryptoExample {

    @Resource
    private FrontendCryptoUtil frontendCryptoUtil;

    /**
     * AES加密解密示例
     */
    public void aesExample() {
        log.info("=== AES加密解密示例 ===");

        try {
            String originalData = "这是需要加密的敏感数据：用户手机号13812345678";
            String keyId = "user_session_001";

            // 加密
            FrontendCryptoUtil.EncryptedData encryptedData = frontendCryptoUtil.encryptWithAES(originalData, keyId);
            log.info("原始数据: {}", originalData);
            log.info("加密后密文: {}", encryptedData.getCipherText());
            log.info("初始向量: {}", encryptedData.getIv());
            log.info("时间戳: {}", encryptedData.getTimestamp());
            log.info("算法类型: {}", encryptedData.getAlgorithm());

            // 解密
            String decryptedData = frontendCryptoUtil.decryptWithAES(encryptedData, keyId);
            log.info("解密后数据: {}", decryptedData);
            log.info("数据一致性: {}", originalData.equals(decryptedData));

        } catch (Exception e) {
            log.error("AES示例执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * RSA加密解密示例
     */
    public void rsaExample() {
        log.info("=== RSA加密解密示例 ===");

        try {
            // 首先生成RSA密钥对
            String[] keyPair = frontendCryptoUtil.generateRSAKeyPair();
            log.info("RSA公钥: {}", keyPair[0]);
            log.info("RSA私钥: {}", keyPair[1].substring(0, 50) + "...");

            String originalData = "RSA加密测试数据";

            // 加密（注意：RSA适用于小数据量）
            String encryptedData = frontendCryptoUtil.encryptWithRSA(originalData);
            log.info("原始数据: {}", originalData);
            log.info("RSA加密后: {}", encryptedData);

            // 解密
            String decryptedData = frontendCryptoUtil.decryptWithRSA(encryptedData);
            log.info("解密后数据: {}", decryptedData);
            log.info("数据一致性: {}", originalData.equals(decryptedData));

        } catch (Exception e) {
            log.error("RSA示例执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 国密SM4加密解密示例
     */
    public void sm4Example() {
        log.info("=== 国密SM4加密解密示例 ===");

        try {
            String originalData = "国密SM4算法测试数据：符合国产化要求";
            String keyId = "sm4_key_001";

            // 加密
            FrontendCryptoUtil.EncryptedData encryptedData = frontendCryptoUtil.encryptWithSM4(originalData, keyId);
            log.info("原始数据: {}", originalData);
            log.info("SM4加密后: {}", encryptedData.getCipherText());
            log.info("算法类型: {}", encryptedData.getAlgorithm());

            // 解密
            String decryptedData = frontendCryptoUtil.decryptWithSM4(encryptedData, keyId);
            log.info("解密后数据: {}", decryptedData);
            log.info("数据一致性: {}", originalData.equals(decryptedData));

        } catch (Exception e) {
            log.error("SM4示例执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 混合加密解密示例
     */
    public void hybridExample() {
        log.info("=== 混合加密解密示例 ===");

        try {
            String originalData = "这是一个大数据量的测试文本，适合使用混合加密。" +
                "混合加密结合了RSA和AES的优点，既保证了安全性，又提高了性能。" +
                "RSA用于加密AES密钥，AES用于加密实际数据。";

            // 混合加密
            FrontendCryptoUtil.HybridEncryptedData hybridData = frontendCryptoUtil.hybridEncrypt(originalData);
            log.info("原始数据长度: {} 字符", originalData.length());
            log.info("加密的AES密钥: {}", hybridData.getEncryptedKey());
            log.info("AES加密的数据: {}", hybridData.getEncryptedData().getCipherText());

            // 混合解密
            String decryptedData = frontendCryptoUtil.hybridDecrypt(hybridData);
            log.info("解密后数据长度: {} 字符", decryptedData.length());
            log.info("数据一致性: {}", originalData.equals(decryptedData));

        } catch (Exception e) {
            log.error("混合加密示例执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 密钥管理示例
     */
    public void keyManagementExample() {
        log.info("=== 密钥管理示例 ===");

        try {
            String keyId = "test_key_management";

            // 生成新的AES密钥
            String aesKey = frontendCryptoUtil.generateNewAESKey(keyId);
            log.info("生成的AES密钥: {}", aesKey);

            // 获取算法强度信息
            for (FrontendCryptoUtil.CryptoAlgorithm algorithm : FrontendCryptoUtil.CryptoAlgorithm.values()) {
                String strength = frontendCryptoUtil.getAlgorithmStrength(algorithm);
                log.info("算法 {} 强度信息: {}", algorithm.getCode(), strength);
            }

            // 获取加密统计信息
            String statistics = frontendCryptoUtil.getCryptoStatistics();
            log.info("当前加密统计: {}", statistics);

            // 密钥轮换
            frontendCryptoUtil.rotateKey(keyId);
            log.info("密钥 {} 已轮换", keyId);

        } catch (Exception e) {
            log.error("密钥管理示例执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 数据完整性验证示例
     */
    public void dataIntegrityExample() {
        log.info("=== 数据完整性验证示例 ===");

        try {
            String originalData = "需要验证完整性的重要数据";
            String keyId = "integrity_test";

            // 加密数据
            FrontendCryptoUtil.EncryptedData encryptedData = frontendCryptoUtil.encryptWithAES(originalData, keyId);

            // 验证数据完整性（正常情况）
            boolean isValid = frontendCryptoUtil.verifyDataIntegrity(originalData, encryptedData.getSignature());
            log.info("数据完整性验证结果: {}", isValid);

            // 模拟数据被篡改的情况
            String tamperedData = originalData + "被篡改";
            boolean isTamperedValid = frontendCryptoUtil.verifyDataIntegrity(tamperedData, encryptedData.getSignature());
            log.info("篡改数据的完整性验证结果: {}", isTamperedValid);

        } catch (Exception e) {
            log.error("数据完整性验证示例执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 执行所有示例
     */
    public void runAllExamples() {
        log.info("开始执行前后端交互加密工具示例...");

        aesExample();
        rsaExample();
        sm4Example();
        hybridExample();
        keyManagementExample();
        dataIntegrityExample();

        log.info("所有示例执行完成！");
    }
}
