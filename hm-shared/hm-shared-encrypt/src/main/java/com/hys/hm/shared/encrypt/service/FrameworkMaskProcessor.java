package com.hys.hm.shared.encrypt.service;

import com.hys.hm.shared.encrypt.annotation.EncryptField;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.util.List;

/**
 * Framework层脱敏处理器
 * 自动处理返回数据的脱敏
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class FrameworkMaskProcessor {

    private final MaskService maskService;

    /**
     * 处理单个实体的脱敏
     */
    public <T> T maskEntity(T entity) {
        if (entity == null) {
            return null;
        }

        try {
            maskEntityFields(entity);
            return entity;
        } catch (Exception e) {
            log.error("实体脱敏处理失败: {}", e.getMessage(), e);
            return entity; // 脱敏失败时返回原实体，不影响业务
        }
    }

    /**
     * 处理实体列表的脱敏
     */
    public <T> List<T> maskEntityList(List<T> entities) {
        if (entities == null || entities.isEmpty()) {
            return entities;
        }

        try {
            entities.forEach(this::maskEntityFields);
            return entities;
        } catch (Exception e) {
            log.error("实体列表脱敏处理失败: {}", e.getMessage(), e);
            return entities; // 脱敏失败时返回原列表，不影响业务
        }
    }

    /**
     * 处理实体字段的脱敏
     */
    private void maskEntityFields(Object entity) {
        if (entity == null) {
            return;
        }

        try {
            Field[] fields = entity.getClass().getDeclaredFields();

            for (Field field : fields) {
                if (field.isAnnotationPresent(EncryptField.class)) {
                    EncryptField encryptField = field.getAnnotation(EncryptField.class);

                    // 如果配置为隐藏字段，则设置为null
                    if (encryptField.hideInResult()) {
                        field.setAccessible(true);
                        field.set(entity, null);
                        continue;
                    }

                    // 根据脱敏级别处理字段
                    if (encryptField.maskLevel() != EncryptField.MaskLevel.NONE) {
                        field.setAccessible(true);
                        Object value = field.get(entity);

                        if (value != null && StringUtils.hasText(value.toString())) {
                            String originalValue = value.toString();
                            String maskedValue = maskService.mask(originalValue, encryptField.maskLevel());
                            field.set(entity, maskedValue);

                            log.debug("字段脱敏: {}#{} -> [MASKED]",
                                     entity.getClass().getSimpleName(), field.getName());
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("处理实体字段脱敏失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 检查实体是否包含需要脱敏的字段
     */
    public boolean hasEncryptFields(Class<?> entityClass) {
        if (entityClass == null) {
            return false;
        }

        try {
            Field[] fields = entityClass.getDeclaredFields();
            for (Field field : fields) {
                if (field.isAnnotationPresent(EncryptField.class)) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            log.debug("检查实体加密字段失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取实体中需要脱敏的字段数量
     */
    public int getEncryptFieldCount(Class<?> entityClass) {
        if (entityClass == null) {
            return 0;
        }

        try {
            Field[] fields = entityClass.getDeclaredFields();
            int count = 0;
            for (Field field : fields) {
                if (field.isAnnotationPresent(EncryptField.class)) {
                    count++;
                }
            }
            return count;
        } catch (Exception e) {
            log.debug("获取实体加密字段数量失败: {}", e.getMessage());
            return 0;
        }
    }
}
