package com.hys.hm.shared.encrypt.util;

import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.time.Instant;
import java.util.Base64;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 前后端交互加密工具类
 * 符合等保三级要求的加密解决方案
 * 
 * 主要特性：
 * 1. AES-256-GCM 认证加密，防止数据篡改
 * 2. RSA-2048 密钥交换，确保密钥安全传输
 * 3. 时间戳防重放攻击
 * 4. 国密SM4算法支持
 * 5. 密钥轮换机制
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Slf4j
@Component
public class FrontendCryptoUtil {

    static {
        // 添加BouncyCastle提供者以支持更多加密算法
        if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
            Security.addProvider(new BouncyCastleProvider());
        }
    }

    // 配置参数
    @Value("${app.security.frontend.aes-key:}")
    private String defaultAesKey;
    
    @Value("${app.security.frontend.rsa-private-key:}")
    private String rsaPrivateKey;
    
    @Value("${app.security.frontend.rsa-public-key:}")
    private String rsaPublicKey;
    
    @Value("${app.security.frontend.key-rotation-interval:3600}")
    private long keyRotationInterval; // 密钥轮换间隔（秒）
    
    @Value("${app.security.frontend.timestamp-tolerance:300}")
    private long timestampTolerance; // 时间戳容忍度（秒）

    // 算法常量
    private static final String AES_ALGORITHM = "AES/GCM/NoPadding";
    private static final String RSA_ALGORITHM = "RSA/ECB/OAEPWITHSHA-256ANDMGF1PADDING";
    private static final String SM4_ALGORITHM = "SM4/ECB/PKCS5Padding";
    private static final int GCM_IV_LENGTH = 12;
    private static final int GCM_TAG_LENGTH = 16;
    private static final int AES_KEY_LENGTH = 256;
    private static final int RSA_KEY_LENGTH = 2048;

    // 密钥缓存
    private final ConcurrentHashMap<String, SecretKey> keyCache = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Long> keyTimestamps = new ConcurrentHashMap<>();

    /**
     * 加密数据传输对象
     */
    public static class EncryptedData {
        private String cipherText;      // 密文
        private String iv;              // 初始向量
        private String timestamp;       // 时间戳
        private String signature;       // 签名
        private String algorithm;       // 算法类型

        public EncryptedData() {}

        public EncryptedData(String cipherText, String iv, String timestamp, String signature, String algorithm) {
            this.cipherText = cipherText;
            this.iv = iv;
            this.timestamp = timestamp;
            this.signature = signature;
            this.algorithm = algorithm;
        }

        // Getters and Setters
        public String getCipherText() { return cipherText; }
        public void setCipherText(String cipherText) { this.cipherText = cipherText; }
        public String getIv() { return iv; }
        public void setIv(String iv) { this.iv = iv; }
        public String getTimestamp() { return timestamp; }
        public void setTimestamp(String timestamp) { this.timestamp = timestamp; }
        public String getSignature() { return signature; }
        public void setSignature(String signature) { this.signature = signature; }
        public String getAlgorithm() { return algorithm; }
        public void setAlgorithm(String algorithm) { this.algorithm = algorithm; }
    }

    /**
     * 加密算法类型枚举
     */
    public enum CryptoAlgorithm {
        AES_256_GCM("AES-256-GCM", "高强度对称加密，推荐用于大数据量"),
        RSA_2048("RSA-2048", "非对称加密，适用于密钥交换"),
        SM4_128("SM4-128", "国密算法，符合国产化要求"),
        HYBRID("HYBRID", "RSA+AES混合加密，兼顾安全性和性能");

        private final String code;
        private final String description;

        CryptoAlgorithm(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() { return code; }
        public String getDescription() { return description; }
    }

    /**
     * 使用AES-256-GCM加密数据
     * 
     * @param plainText 要加密的明文
     * @param keyId 密钥标识符
     * @return 加密数据对象
     */
    public EncryptedData encryptWithAES(String plainText, String keyId) {
        try {
            SecretKey secretKey = getOrGenerateAESKey(keyId);
            
            // 生成随机IV
            byte[] iv = new byte[GCM_IV_LENGTH];
            new SecureRandom().nextBytes(iv);
            
            // 创建加密器
            Cipher cipher = Cipher.getInstance(AES_ALGORITHM);
            GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, gcmSpec);
            
            // 添加时间戳作为附加认证数据
            String timestamp = String.valueOf(Instant.now().getEpochSecond());
            cipher.updateAAD(timestamp.getBytes(StandardCharsets.UTF_8));
            
            // 执行加密
            byte[] cipherBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
            
            // 生成签名
            String signature = generateDataSignature(cipherBytes, timestamp);
            
            return new EncryptedData(
                Base64.getEncoder().encodeToString(cipherBytes),
                Base64.getEncoder().encodeToString(iv),
                timestamp,
                signature,
                CryptoAlgorithm.AES_256_GCM.getCode()
            );
            
        } catch (Exception e) {
            log.error("AES加密失败: {}", e.getMessage(), e);
            throw new RuntimeException("AES加密失败", e);
        }
    }

    /**
     * 使用AES-256-GCM解密数据
     * 
     * @param encryptedData 包含密文和相关信息的对象
     * @param keyId 密钥标识符
     * @return 解密后的明文
     */
    public String decryptWithAES(EncryptedData encryptedData, String keyId) {
        try {
            // 验证时间戳
            if (!validateTimestamp(encryptedData.getTimestamp())) {
                throw new SecurityException("时间戳验证失败，可能存在重放攻击");
            }
            
            // 验证签名
            byte[] cipherBytes = Base64.getDecoder().decode(encryptedData.getCipherText());
            if (!verifyDataSignature(cipherBytes, encryptedData.getTimestamp(), encryptedData.getSignature())) {
                throw new SecurityException("数据签名验证失败，数据可能被篡改");
            }
            
            SecretKey secretKey = getOrGenerateAESKey(keyId);
            byte[] iv = Base64.getDecoder().decode(encryptedData.getIv());
            
            // 创建解密器
            Cipher cipher = Cipher.getInstance(AES_ALGORITHM);
            GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, gcmSpec);
            
            // 添加时间戳作为附加认证数据
            cipher.updateAAD(encryptedData.getTimestamp().getBytes(StandardCharsets.UTF_8));
            
            // 执行解密
            byte[] plainBytes = cipher.doFinal(cipherBytes);
            
            return new String(plainBytes, StandardCharsets.UTF_8);
            
        } catch (Exception e) {
            log.error("AES解密失败: {}", e.getMessage(), e);
            throw new RuntimeException("AES解密失败", e);
        }
    }

    /**
     * 使用RSA加密（适用于小数据量或密钥交换）
     * 
     * @param plainText 明文数据
     * @return Base64编码的密文
     */
    public String encryptWithRSA(String plainText) {
        try {
            PublicKey publicKey = getRSAPublicKey();
            Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            
            byte[] cipherBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(cipherBytes);
            
        } catch (Exception e) {
            log.error("RSA加密失败: {}", e.getMessage(), e);
            throw new RuntimeException("RSA加密失败", e);
        }
    }

    /**
     * 使用RSA解密
     * 
     * @param cipherText Base64编码的密文
     * @return 解密后的明文
     */
    public String decryptWithRSA(String cipherText) {
        try {
            PrivateKey privateKey = getRSAPrivateKey();
            Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, privateKey);
            
            byte[] cipherBytes = Base64.getDecoder().decode(cipherText);
            byte[] plainBytes = cipher.doFinal(cipherBytes);
            
            return new String(plainBytes, StandardCharsets.UTF_8);
            
        } catch (Exception e) {
            log.error("RSA解密失败: {}", e.getMessage(), e);
            throw new RuntimeException("RSA解密失败", e);
        }
    }

    /**
     * 使用国密SM4算法加密数据
     *
     * @param plainText 明文数据
     * @param keyId 密钥标识符
     * @return 加密数据对象
     */
    public EncryptedData encryptWithSM4(String plainText, String keyId) {
        try {
            SecretKey secretKey = getOrGenerateSM4Key(keyId);

            // SM4使用ECB模式，生成随机IV（虽然ECB不使用IV，但为了接口一致性）
            byte[] iv = new byte[16];
            new SecureRandom().nextBytes(iv);

            Cipher cipher = Cipher.getInstance(SM4_ALGORITHM, BouncyCastleProvider.PROVIDER_NAME);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);

            String timestamp = String.valueOf(Instant.now().getEpochSecond());
            byte[] cipherBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));

            String signature = generateDataSignature(cipherBytes, timestamp);

            return new EncryptedData(
                Base64.getEncoder().encodeToString(cipherBytes),
                Base64.getEncoder().encodeToString(iv),
                timestamp,
                signature,
                CryptoAlgorithm.SM4_128.getCode()
            );

        } catch (Exception e) {
            log.error("SM4加密失败: {}", e.getMessage(), e);
            throw new RuntimeException("SM4加密失败", e);
        }
    }

    /**
     * 使用国密SM4算法解密数据
     *
     * @param encryptedData 加密数据对象
     * @param keyId 密钥标识符
     * @return 解密后的明文
     */
    public String decryptWithSM4(EncryptedData encryptedData, String keyId) {
        try {
            // 验证时间戳和签名
            if (!validateTimestamp(encryptedData.getTimestamp())) {
                throw new SecurityException("时间戳验证失败");
            }

            byte[] cipherBytes = Base64.getDecoder().decode(encryptedData.getCipherText());
            if (!verifyDataSignature(cipherBytes, encryptedData.getTimestamp(), encryptedData.getSignature())) {
                throw new SecurityException("数据签名验证失败");
            }

            SecretKey secretKey = getOrGenerateSM4Key(keyId);

            Cipher cipher = Cipher.getInstance(SM4_ALGORITHM, BouncyCastleProvider.PROVIDER_NAME);
            cipher.init(Cipher.DECRYPT_MODE, secretKey);

            byte[] plainBytes = cipher.doFinal(cipherBytes);
            return new String(plainBytes, StandardCharsets.UTF_8);

        } catch (Exception e) {
            log.error("SM4解密失败: {}", e.getMessage(), e);
            throw new RuntimeException("SM4解密失败", e);
        }
    }

    /**
     * 混合加密：使用RSA加密AES密钥，AES加密数据
     * 适用于大数据量的安全传输
     *
     * @param plainText 明文数据
     * @return 包含加密密钥和数据的对象
     */
    public HybridEncryptedData hybridEncrypt(String plainText) {
        try {
            // 生成随机AES密钥
            KeyGenerator keyGen = KeyGenerator.getInstance("AES");
            keyGen.init(AES_KEY_LENGTH);
            SecretKey aesKey = keyGen.generateKey();

            // 使用AES加密数据
            String tempKeyId = "temp_" + System.currentTimeMillis();
            keyCache.put(tempKeyId, aesKey);
            EncryptedData encryptedData = encryptWithAES(plainText, tempKeyId);

            // 使用RSA加密AES密钥
            String encodedKey = Base64.getEncoder().encodeToString(aesKey.getEncoded());
            String encryptedKey = encryptWithRSA(encodedKey);

            // 清理临时密钥
            keyCache.remove(tempKeyId);

            return new HybridEncryptedData(encryptedData, encryptedKey);

        } catch (Exception e) {
            log.error("混合加密失败: {}", e.getMessage(), e);
            throw new RuntimeException("混合加密失败", e);
        }
    }

    /**
     * 混合解密：使用RSA解密AES密钥，AES解密数据
     *
     * @param hybridData 混合加密数据
     * @return 解密后的明文
     */
    public String hybridDecrypt(HybridEncryptedData hybridData) {
        try {
            // 使用RSA解密AES密钥
            String encodedKey = decryptWithRSA(hybridData.getEncryptedKey());
            byte[] keyBytes = Base64.getDecoder().decode(encodedKey);
            SecretKey aesKey = new SecretKeySpec(keyBytes, "AES");

            // 使用解密的AES密钥解密数据
            String tempKeyId = "temp_" + System.currentTimeMillis();
            keyCache.put(tempKeyId, aesKey);
            String plainText = decryptWithAES(hybridData.getEncryptedData(), tempKeyId);

            // 清理临时密钥
            keyCache.remove(tempKeyId);

            return plainText;

        } catch (Exception e) {
            log.error("混合解密失败: {}", e.getMessage(), e);
            throw new RuntimeException("混合解密失败", e);
        }
    }

    /**
     * 混合加密数据传输对象
     */
    public static class HybridEncryptedData {
        private EncryptedData encryptedData;    // AES加密的数据
        private String encryptedKey;            // RSA加密的AES密钥

        public HybridEncryptedData() {}

        public HybridEncryptedData(EncryptedData encryptedData, String encryptedKey) {
            this.encryptedData = encryptedData;
            this.encryptedKey = encryptedKey;
        }

        public EncryptedData getEncryptedData() { return encryptedData; }
        public void setEncryptedData(EncryptedData encryptedData) { this.encryptedData = encryptedData; }
        public String getEncryptedKey() { return encryptedKey; }
        public void setEncryptedKey(String encryptedKey) { this.encryptedKey = encryptedKey; }
    }

    /**
     * 生成新的AES密钥
     *
     * @param keyId 密钥标识符
     * @return AES密钥
     */
    public String generateNewAESKey(String keyId) {
        try {
            KeyGenerator keyGen = KeyGenerator.getInstance("AES");
            keyGen.init(AES_KEY_LENGTH);
            SecretKey secretKey = keyGen.generateKey();

            // 缓存密钥
            keyCache.put(keyId, secretKey);
            keyTimestamps.put(keyId, System.currentTimeMillis());

            // 返回Base64编码的密钥（用于前端存储）
            return Base64.getEncoder().encodeToString(secretKey.getEncoded());

        } catch (Exception e) {
            log.error("生成AES密钥失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成AES密钥失败", e);
        }
    }

    /**
     * 生成RSA密钥对
     *
     * @return 包含公钥和私钥的数组 [publicKey, privateKey]
     */
    public String[] generateRSAKeyPair() {
        try {
            KeyPairGenerator keyGen = KeyPairGenerator.getInstance("RSA");
            keyGen.initialize(RSA_KEY_LENGTH);
            KeyPair keyPair = keyGen.generateKeyPair();

            String publicKey = Base64.getEncoder().encodeToString(keyPair.getPublic().getEncoded());
            String privateKey = Base64.getEncoder().encodeToString(keyPair.getPrivate().getEncoded());

            return new String[]{publicKey, privateKey};

        } catch (Exception e) {
            log.error("生成RSA密钥对失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成RSA密钥对失败", e);
        }
    }

    /**
     * 轮换密钥
     *
     * @param keyId 密钥标识符
     */
    public void rotateKey(String keyId) {
        keyCache.remove(keyId);
        keyTimestamps.remove(keyId);
        log.info("密钥已轮换: {}", keyId);
    }

    /**
     * 清理过期密钥
     */
    public void cleanupExpiredKeys() {
        long currentTime = System.currentTimeMillis();
        keyTimestamps.entrySet().removeIf(entry -> {
            boolean expired = (currentTime - entry.getValue()) > (keyRotationInterval * 1000);
            if (expired) {
                keyCache.remove(entry.getKey());
                log.info("清理过期密钥: {}", entry.getKey());
            }
            return expired;
        });
    }

    /**
     * 验证数据完整性
     *
     * @param data 原始数据
     * @param signature 签名
     * @return 验证结果
     */
    public boolean verifyDataIntegrity(String data, String signature) {
        try {
            String computedSignature = generateDataSignature(data.getBytes(StandardCharsets.UTF_8),
                String.valueOf(Instant.now().getEpochSecond()));
            return computedSignature.equals(signature);
        } catch (Exception e) {
            log.error("数据完整性验证失败: {}", e.getMessage(), e);
            return false;
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 获取或生成AES密钥
     */
    private SecretKey getOrGenerateAESKey(String keyId) {
        SecretKey key = keyCache.get(keyId);
        if (key == null || isKeyExpired(keyId)) {
            synchronized (this) {
                key = keyCache.get(keyId);
                if (key == null || isKeyExpired(keyId)) {
                    if (defaultAesKey != null && !defaultAesKey.isEmpty()) {
                        // 使用配置的默认密钥
                        byte[] keyBytes = Base64.getDecoder().decode(defaultAesKey);
                        key = new SecretKeySpec(keyBytes, "AES");
                    } else {
                        // 生成新密钥
                        try {
                            KeyGenerator keyGen = KeyGenerator.getInstance("AES");
                            keyGen.init(AES_KEY_LENGTH);
                            key = keyGen.generateKey();
                        } catch (Exception e) {
                            throw new RuntimeException("生成AES密钥失败", e);
                        }
                    }
                    keyCache.put(keyId, key);
                    keyTimestamps.put(keyId, System.currentTimeMillis());
                }
            }
        }
        return key;
    }

    /**
     * 获取或生成SM4密钥
     */
    private SecretKey getOrGenerateSM4Key(String keyId) {
        SecretKey key = keyCache.get(keyId + "_SM4");
        if (key == null || isKeyExpired(keyId + "_SM4")) {
            synchronized (this) {
                key = keyCache.get(keyId + "_SM4");
                if (key == null || isKeyExpired(keyId + "_SM4")) {
                    try {
                        KeyGenerator keyGen = KeyGenerator.getInstance("SM4", BouncyCastleProvider.PROVIDER_NAME);
                        keyGen.init(128); // SM4使用128位密钥
                        key = keyGen.generateKey();
                        keyCache.put(keyId + "_SM4", key);
                        keyTimestamps.put(keyId + "_SM4", System.currentTimeMillis());
                    } catch (Exception e) {
                        throw new RuntimeException("生成SM4密钥失败", e);
                    }
                }
            }
        }
        return key;
    }

    /**
     * 获取RSA公钥
     */
    private PublicKey getRSAPublicKey() {
        try {
            if (rsaPublicKey == null || rsaPublicKey.isEmpty()) {
                throw new IllegalStateException("RSA公钥未配置");
            }
            byte[] keyBytes = Base64.getDecoder().decode(rsaPublicKey);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            return keyFactory.generatePublic(keySpec);
        } catch (Exception e) {
            throw new RuntimeException("获取RSA公钥失败", e);
        }
    }

    /**
     * 获取RSA私钥
     */
    private PrivateKey getRSAPrivateKey() {
        try {
            if (rsaPrivateKey == null || rsaPrivateKey.isEmpty()) {
                throw new IllegalStateException("RSA私钥未配置");
            }
            byte[] keyBytes = Base64.getDecoder().decode(rsaPrivateKey);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            return keyFactory.generatePrivate(keySpec);
        } catch (Exception e) {
            throw new RuntimeException("获取RSA私钥失败", e);
        }
    }

    /**
     * 生成数据签名
     */
    private String generateDataSignature(byte[] data, String timestamp) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            digest.update(data);
            digest.update(timestamp.getBytes(StandardCharsets.UTF_8));
            if (defaultAesKey != null && !defaultAesKey.isEmpty()) {
                digest.update(defaultAesKey.getBytes(StandardCharsets.UTF_8));
            }
            byte[] hash = digest.digest();
            return Base64.getEncoder().encodeToString(hash);
        } catch (Exception e) {
            throw new RuntimeException("生成数据签名失败", e);
        }
    }

    /**
     * 验证数据签名
     */
    private boolean verifyDataSignature(byte[] data, String timestamp, String signature) {
        try {
            String computedSignature = generateDataSignature(data, timestamp);
            return computedSignature.equals(signature);
        } catch (Exception e) {
            log.error("验证数据签名失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 验证时间戳
     */
    private boolean validateTimestamp(String timestamp) {
        try {
            long ts = Long.parseLong(timestamp);
            long currentTime = Instant.now().getEpochSecond();
            long diff = Math.abs(currentTime - ts);
            return diff <= timestampTolerance;
        } catch (Exception e) {
            log.error("时间戳验证失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查密钥是否过期
     */
    private boolean isKeyExpired(String keyId) {
        Long timestamp = keyTimestamps.get(keyId);
        if (timestamp == null) {
            return true;
        }
        long currentTime = System.currentTimeMillis();
        return (currentTime - timestamp) > (keyRotationInterval * 1000);
    }

    /**
     * 安全地清除敏感数据
     */
    private void secureClear(byte[] data) {
        if (data != null) {
            for (int i = 0; i < data.length; i++) {
                data[i] = 0;
            }
        }
    }

    /**
     * 获取算法强度信息
     */
    public String getAlgorithmStrength(CryptoAlgorithm algorithm) {
        switch (algorithm) {
            case AES_256_GCM:
                return "256位密钥长度，GCM模式提供认证加密";
            case RSA_2048:
                return "2048位密钥长度，OAEP填充模式";
            case SM4_128:
                return "128位密钥长度，国密标准算法";
            case HYBRID:
                return "RSA-2048 + AES-256-GCM 混合加密";
            default:
                return "未知算法";
        }
    }

    /**
     * 检查系统加密能力
     */
    public boolean checkCryptoCapabilities() {
        try {
            // 检查AES-256支持
            Cipher.getInstance(AES_ALGORITHM);

            // 检查RSA支持
            Cipher.getInstance(RSA_ALGORITHM);

            // 检查SM4支持（需要BouncyCastle）
            Cipher.getInstance(SM4_ALGORITHM, BouncyCastleProvider.PROVIDER_NAME);

            log.info("加密能力检查通过：支持AES-256-GCM、RSA-2048、SM4-128");
            return true;

        } catch (Exception e) {
            log.error("加密能力检查失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取加密统计信息
     */
    public String getCryptoStatistics() {
        return String.format(
            "缓存密钥数量: %d, 密钥轮换间隔: %d秒, 时间戳容忍度: %d秒",
            keyCache.size(),
            keyRotationInterval,
            timestampTolerance
        );
    }
}
