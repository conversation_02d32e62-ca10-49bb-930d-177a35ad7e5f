package com.hys.hm.shared.encrypt.service;


import com.hys.hm.shared.encrypt.annotation.EncryptField;

/**
 * 数据脱敏服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
public interface MaskService {

    /**
     * 根据脱敏级别对数据进行脱敏处理
     *
     * @param data 原始数据
     * @param maskLevel 脱敏级别
     * @return 脱敏后的数据
     */
    String mask(String data, EncryptField.MaskLevel maskLevel);

    /**
     * 手机号脱敏
     *
     * @param phone 手机号
     * @param maskLevel 脱敏级别
     * @return 脱敏后的手机号
     */
    String maskPhone(String phone, EncryptField.MaskLevel maskLevel);

    /**
     * 身份证号脱敏
     *
     * @param idCard 身份证号
     * @param maskLevel 脱敏级别
     * @return 脱敏后的身份证号
     */
    String maskIdCard(String idCard, EncryptField.MaskLevel maskLevel);

    /**
     * 姓名脱敏
     *
     * @param name 姓名
     * @param maskLevel 脱敏级别
     * @return 脱敏后的姓名
     */
    String maskName(String name, EncryptField.MaskLevel maskLevel);

    /**
     * 地址脱敏
     *
     * @param address 地址
     * @param maskLevel 脱敏级别
     * @return 脱敏后的地址
     */
    String maskAddress(String address, EncryptField.MaskLevel maskLevel);

    /**
     * 通用文本脱敏
     *
     * @param text 文本
     * @param maskLevel 脱敏级别
     * @return 脱敏后的文本
     */
    String maskText(String text, EncryptField.MaskLevel maskLevel);
}
