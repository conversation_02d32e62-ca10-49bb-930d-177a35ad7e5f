package com.hys.hm.shared.encrypt.util;

import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import java.security.*;
import java.util.Base64;

/**
 * 加密密钥生成工具类
 * 用于生成各种加密算法所需的密钥
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Slf4j
public class CryptoKeyGenerator {

    static {
        // 添加BouncyCastle提供者
        if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
            Security.addProvider(new BouncyCastleProvider());
        }
    }

    /**
     * 生成AES-256密钥
     * 
     * @return Base64编码的AES密钥
     */
    public static String generateAES256Key() {
        try {
            KeyGenerator keyGen = KeyGenerator.getInstance("AES");
            keyGen.init(256);
            SecretKey secretKey = keyGen.generateKey();
            return Base64.getEncoder().encodeToString(secretKey.getEncoded());
        } catch (Exception e) {
            log.error("生成AES-256密钥失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成AES-256密钥失败", e);
        }
    }

    /**
     * 生成RSA-2048密钥对
     * 
     * @return 包含公钥和私钥的KeyPairResult对象
     */
    public static KeyPairResult generateRSA2048KeyPair() {
        try {
            KeyPairGenerator keyGen = KeyPairGenerator.getInstance("RSA");
            keyGen.initialize(2048);
            KeyPair keyPair = keyGen.generateKeyPair();
            
            String publicKey = Base64.getEncoder().encodeToString(keyPair.getPublic().getEncoded());
            String privateKey = Base64.getEncoder().encodeToString(keyPair.getPrivate().getEncoded());
            
            return new KeyPairResult(publicKey, privateKey);
        } catch (Exception e) {
            log.error("生成RSA-2048密钥对失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成RSA-2048密钥对失败", e);
        }
    }

    /**
     * 生成SM4密钥
     * 
     * @return Base64编码的SM4密钥
     */
    public static String generateSM4Key() {
        try {
            KeyGenerator keyGen = KeyGenerator.getInstance("SM4", BouncyCastleProvider.PROVIDER_NAME);
            keyGen.init(128);
            SecretKey secretKey = keyGen.generateKey();
            return Base64.getEncoder().encodeToString(secretKey.getEncoded());
        } catch (Exception e) {
            log.error("生成SM4密钥失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成SM4密钥失败", e);
        }
    }

    /**
     * 生成随机盐值
     * 
     * @param length 盐值长度（字节）
     * @return Base64编码的盐值
     */
    public static String generateSalt(int length) {
        try {
            byte[] salt = new byte[length];
            new SecureRandom().nextBytes(salt);
            return Base64.getEncoder().encodeToString(salt);
        } catch (Exception e) {
            log.error("生成盐值失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成盐值失败", e);
        }
    }

    /**
     * 生成完整的加密配置
     * 
     * @return 完整的加密配置对象
     */
    public static CryptoConfig generateFullCryptoConfig() {
        try {
            String aesKey = generateAES256Key();
            KeyPairResult rsaKeyPair = generateRSA2048KeyPair();
            String sm4Key = generateSM4Key();
            String salt = generateSalt(32);
            
            return new CryptoConfig(aesKey, rsaKeyPair.getPublicKey(), 
                rsaKeyPair.getPrivateKey(), sm4Key, salt);
        } catch (Exception e) {
            log.error("生成完整加密配置失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成完整加密配置失败", e);
        }
    }

    /**
     * 密钥对结果类
     */
    public static class KeyPairResult {
        private final String publicKey;
        private final String privateKey;

        public KeyPairResult(String publicKey, String privateKey) {
            this.publicKey = publicKey;
            this.privateKey = privateKey;
        }

        public String getPublicKey() { return publicKey; }
        public String getPrivateKey() { return privateKey; }

        @Override
        public String toString() {
            return String.format("KeyPairResult{publicKey='%s...', privateKey='%s...'}",
                publicKey.substring(0, Math.min(50, publicKey.length())),
                privateKey.substring(0, Math.min(50, privateKey.length())));
        }
    }

    /**
     * 完整加密配置类
     */
    public static class CryptoConfig {
        private final String aesKey;
        private final String rsaPublicKey;
        private final String rsaPrivateKey;
        private final String sm4Key;
        private final String salt;

        public CryptoConfig(String aesKey, String rsaPublicKey, String rsaPrivateKey, 
                           String sm4Key, String salt) {
            this.aesKey = aesKey;
            this.rsaPublicKey = rsaPublicKey;
            this.rsaPrivateKey = rsaPrivateKey;
            this.sm4Key = sm4Key;
            this.salt = salt;
        }

        public String getAesKey() { return aesKey; }
        public String getRsaPublicKey() { return rsaPublicKey; }
        public String getRsaPrivateKey() { return rsaPrivateKey; }
        public String getSm4Key() { return sm4Key; }
        public String getSalt() { return salt; }

        /**
         * 生成YAML配置格式
         */
        public String toYamlConfig() {
            return String.format(
                "app:\n" +
                "  security:\n" +
                "    frontend:\n" +
                "      enabled: true\n" +
                "      aes-key: \"%s\"\n" +
                "      rsa-public-key: \"%s\"\n" +
                "      rsa-private-key: \"%s\"\n" +
                "      sm4-key: \"%s\"\n" +
                "      salt: \"%s\"\n" +
                "      key-rotation-interval: 3600\n" +
                "      timestamp-tolerance: 300\n" +
                "      auto-key-rotation: true\n" +
                "      default-algorithm: \"AES_256_GCM\"",
                aesKey, rsaPublicKey, rsaPrivateKey, sm4Key, salt
            );
        }

        /**
         * 生成环境变量格式
         */
        public String toEnvironmentVariables() {
            return String.format(
                "# 前后端交互加密环境变量\n" +
                "export FRONTEND_AES_KEY=\"%s\"\n" +
                "export FRONTEND_RSA_PUBLIC_KEY=\"%s\"\n" +
                "export FRONTEND_RSA_PRIVATE_KEY=\"%s\"\n" +
                "export FRONTEND_SM4_KEY=\"%s\"\n" +
                "export FRONTEND_SALT=\"%s\"\n" +
                "export FRONTEND_KEY_ROTATION_INTERVAL=\"3600\"\n" +
                "export FRONTEND_TIMESTAMP_TOLERANCE=\"300\"",
                aesKey, rsaPublicKey, rsaPrivateKey, sm4Key, salt
            );
        }

        @Override
        public String toString() {
            return String.format(
                "CryptoConfig{\n" +
                "  aesKey='%s...'\n" +
                "  rsaPublicKey='%s...'\n" +
                "  rsaPrivateKey='%s...'\n" +
                "  sm4Key='%s...'\n" +
                "  salt='%s...'\n" +
                "}",
                aesKey.substring(0, Math.min(20, aesKey.length())),
                rsaPublicKey.substring(0, Math.min(20, rsaPublicKey.length())),
                rsaPrivateKey.substring(0, Math.min(20, rsaPrivateKey.length())),
                sm4Key.substring(0, Math.min(20, sm4Key.length())),
                salt.substring(0, Math.min(20, salt.length()))
            );
        }
    }

    /**
     * 主方法 - 用于命令行生成密钥
     */
    public static void main(String[] args) {
        System.out.println("=== 前后端交互加密密钥生成工具 ===\n");

        try {
            // 生成完整配置
            CryptoConfig config = generateFullCryptoConfig();
            
            System.out.println("生成的加密配置：");
            System.out.println(config);
            System.out.println();
            
            System.out.println("YAML配置格式：");
            System.out.println(config.toYamlConfig());
            System.out.println();
            
            System.out.println("环境变量格式：");
            System.out.println(config.toEnvironmentVariables());
            System.out.println();
            
            System.out.println("=== 密钥生成完成 ===");
            System.out.println("请将上述配置保存到安全的位置，并根据需要配置到应用程序中。");
            System.out.println("注意：私钥信息非常敏感，请妥善保管！");
            
        } catch (Exception e) {
            System.err.println("密钥生成失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
