package com.hys.hm.shared.encrypt.service.impl;

import com.hys.hm.shared.encrypt.annotation.EncryptField;
import com.hys.hm.shared.encrypt.service.MaskService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.regex.Pattern;

/**
 * 数据脱敏服务实现
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Service
public class MaskServiceImpl implements MaskService {

    private static final String MASK_CHAR = "*";
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");
    private static final Pattern ID_CARD_PATTERN = Pattern.compile("^[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx]$");

    @Override
    public String mask(String data, EncryptField.MaskLevel maskLevel) {
        if (!StringUtils.hasText(data) || maskLevel == EncryptField.MaskLevel.NONE) {
            return data;
        }

        // 根据数据格式智能选择脱敏方式
        if (PHONE_PATTERN.matcher(data).matches()) {
            return maskPhone(data, maskLevel);
        } else if (ID_CARD_PATTERN.matcher(data).matches()) {
            return maskIdCard(data, maskLevel);
        } else if (isChineseName(data)) {
            return maskName(data, maskLevel);
        } else {
            return maskText(data, maskLevel);
        }
    }

    @Override
    public String maskPhone(String phone, EncryptField.MaskLevel maskLevel) {
        if (!StringUtils.hasText(phone) || phone.length() != 11) {
            return phone;
        }

        switch (maskLevel) {
            case NONE:
                return phone;
            case PARTIAL:
                return phone.substring(0, 3) + "****" + phone.substring(7);
            case FULL:
                return "***********";
            case FIRST_LAST:
                return phone.substring(0, 1) + "*********" + phone.substring(10);
            case MIDDLE:
                return "***" + phone.substring(3, 8) + "***";
            default:
                return phone.substring(0, 3) + "****" + phone.substring(7);
        }
    }

    @Override
    public String maskIdCard(String idCard, EncryptField.MaskLevel maskLevel) {
        if (!StringUtils.hasText(idCard) || idCard.length() < 15) {
            return idCard;
        }

        switch (maskLevel) {
            case NONE:
                return idCard;
            case PARTIAL:
                return idCard.substring(0, 6) + "********" + idCard.substring(idCard.length() - 4);
            case FULL:
                return "*".repeat(idCard.length());
            case FIRST_LAST:
                return idCard.substring(0, 2) + "*".repeat(idCard.length() - 4) + idCard.substring(idCard.length() - 2);
            case MIDDLE:
                int start = Math.max(0, idCard.length() / 3);
                int end = Math.min(idCard.length(), idCard.length() * 2 / 3);
                return "*".repeat(start) + idCard.substring(start, end) + "*".repeat(idCard.length() - end);
            default:
                return idCard.substring(0, 6) + "********" + idCard.substring(idCard.length() - 4);
        }
    }

    @Override
    public String maskName(String name, EncryptField.MaskLevel maskLevel) {
        if (!StringUtils.hasText(name)) {
            return name;
        }

        switch (maskLevel) {
            case NONE:
                return name;
            case PARTIAL:
            case FIRST_LAST:
                if (name.length() <= 2) {
                    return name.substring(0, 1) + "*";
                } else {
                    return name.substring(0, 1) + "*".repeat(name.length() - 2) + name.substring(name.length() - 1);
                }
            case FULL:
                return "*".repeat(name.length());
            case MIDDLE:
                if (name.length() <= 2) {
                    return name;
                } else {
                    int mid = name.length() / 2;
                    return "*".repeat(mid) + name.substring(mid, mid + 1) + "*".repeat(name.length() - mid - 1);
                }
            default:
                return name.substring(0, 1) + "*".repeat(name.length() - 1);
        }
    }

    @Override
    public String maskAddress(String address, EncryptField.MaskLevel maskLevel) {
        if (!StringUtils.hasText(address)) {
            return address;
        }

        switch (maskLevel) {
            case NONE:
                return address;
            case PARTIAL:
                if (address.length() <= 6) {
                    return address.substring(0, 2) + "*".repeat(address.length() - 2);
                } else {
                    return address.substring(0, 6) + "*".repeat(Math.min(6, address.length() - 6));
                }
            case FULL:
                return "*".repeat(Math.min(address.length(), 10));
            case FIRST_LAST:
                if (address.length() <= 4) {
                    return address;
                } else {
                    return address.substring(0, 2) + "*".repeat(address.length() - 4) + address.substring(address.length() - 2);
                }
            case MIDDLE:
                if (address.length() <= 6) {
                    return address;
                } else {
                    int start = address.length() / 3;
                    int end = address.length() * 2 / 3;
                    return "*".repeat(start) + address.substring(start, end) + "*".repeat(address.length() - end);
                }
            default:
                return maskText(address, maskLevel);
        }
    }

    @Override
    public String maskText(String text, EncryptField.MaskLevel maskLevel) {
        if (!StringUtils.hasText(text)) {
            return text;
        }

        switch (maskLevel) {
            case NONE:
                return text;
            case PARTIAL:
                if (text.length() <= 4) {
                    return text.substring(0, 1) + "*".repeat(text.length() - 1);
                } else {
                    return text.substring(0, 2) + "*".repeat(Math.min(4, text.length() - 4)) + text.substring(text.length() - 2);
                }
            case FULL:
                return "*".repeat(Math.min(text.length(), 8));
            case FIRST_LAST:
                if (text.length() <= 2) {
                    return text;
                } else {
                    return text.substring(0, 1) + "*".repeat(text.length() - 2) + text.substring(text.length() - 1);
                }
            case MIDDLE:
                if (text.length() <= 4) {
                    return text;
                } else {
                    int start = text.length() / 4;
                    int end = text.length() * 3 / 4;
                    return "*".repeat(start) + text.substring(start, end) + "*".repeat(text.length() - end);
                }
            default:
                return text.substring(0, 1) + "*".repeat(text.length() - 1);
        }
    }

    /**
     * 判断是否为中文姓名
     */
    private boolean isChineseName(String text) {
        if (!StringUtils.hasText(text) || text.length() > 10) {
            return false;
        }

        // 简单判断：长度2-4，且包含中文字符
        return text.length() >= 2 && text.length() <= 4 &&
               text.chars().anyMatch(c -> c >= 0x4E00 && c <= 0x9FFF);
    }
}
