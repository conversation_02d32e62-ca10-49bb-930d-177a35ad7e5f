package com.hys.hm.shared.encrypt.aspect;

import com.hys.hm.shared.common.Result;
import com.hys.hm.shared.common.page.PageResult;
import com.hys.hm.shared.encrypt.annotation.EncryptField;
import com.hys.hm.shared.encrypt.service.MaskResponseWrapper;
import com.hys.hm.shared.encrypt.service.MaskService;
import com.hys.hm.shared.encrypt.util.FrontendCryptoUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.http.ResponseEntity;

import java.lang.reflect.Field;
import java.util.Collection;
import java.util.List;

/**
 * 响应数据脱敏切面
 * 自动对Controller返回的数据进行脱敏和加密处理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Aspect
@RequiredArgsConstructor
@Slf4j
@Order(100) // 确保在其他切面之后执行
public class MaskResponseAspect {

    private final MaskService maskService;

    /**
     * 前后端交互加密工具（可选）
     */
    @Autowired(required = false)
    private FrontendCryptoUtil frontendCryptoUtil;

    /**
     * 拦截所有Controller的方法
     */
    @Around("execution(* com.hys..*Controller.*(..))")
    public Object maskResponse(ProceedingJoinPoint joinPoint) throws Throwable {
        log.info("MaskResponseAspect 被触发: {}.{}",
                joinPoint.getTarget().getClass().getSimpleName(),
                joinPoint.getSignature().getName());

        // 执行原方法
        Object result = joinPoint.proceed();

        log.info("原始返回结果类型: {}", result != null ? result.getClass().getSimpleName() : "null");

        // 对返回结果进行脱敏和加密处理
        Object processedResult = maskAndEncryptResponseData(result);

        log.info("脱敏和加密处理完成，结果类型: {}", processedResult != null ? processedResult.getClass().getSimpleName() : "null");

        return processedResult;
    }

    /**
     * 对响应数据进行脱敏和加密处理
     */
    private Object maskAndEncryptResponseData(Object response) {
        if (response == null) {
            log.debug("数据为null，跳过脱敏处理");
            return null;
        }

        log.debug("开始脱敏和加密处理，数据类型: {}", response.getClass().getSimpleName());

        try {
            // 处理ResponseEntity包装的响应
            if (response instanceof ResponseEntity) {
                ResponseEntity<?> responseEntity = (ResponseEntity<?>) response;
                Object body = responseEntity.getBody();
                Object processedBody = maskAndEncryptResponseData(body);
                return ResponseEntity.status(responseEntity.getStatusCode())
                    .headers(responseEntity.getHeaders())
                    .body(processedBody);
            }

            // 处理Result包装的响应
            if (response instanceof Result) {
                Result<?> result = (Result<?>) response;
                Object data = result.getData();
                Object processedData = maskAndEncryptResponseData(data);

                // 创建新的Result对象，保持其他字段不变
                Result<Object> processedResult = new Result<>();
                processedResult.setCode(result.getCode());
                processedResult.setMessage(result.getMessage());
                processedResult.setTimestamp(result.getTimestamp());
                processedResult.setTraceId(result.getTraceId());
                processedResult.setData(processedData);

                return processedResult;
            }

            // 处理PageResult包装的响应
            if (response instanceof PageResult) {
                PageResult<?> pageResult = (PageResult<?>) response;
                List<?> content = pageResult.getContent();
                List<?> processedContent = (List<?>) maskAndEncryptResponseData(content);

                return PageResult.of(processedContent, pageResult.getPage(),
                                   pageResult.getSize(), pageResult.getTotal());
            }

            // 处理集合类型
            if (response instanceof Collection) {
                Collection<?> collection = (Collection<?>) response;
                return collection.stream()
                    .map(this::maskAndEncryptEntity)
                    .collect(java.util.stream.Collectors.toList());
            }

            // 处理单个实体对象
            return maskAndEncryptEntity(response);

        } catch (Exception e) {
            log.error("响应数据脱敏和加密失败: {}", e.getMessage(), e);
            return response; // 处理失败时返回原数据，不影响业务
        }
    }

    /**
     * 对单个实体进行脱敏和加密处理
     */
    private Object maskAndEncryptEntity(Object entity) {
        if (entity == null) {
            return null;
        }

        // 检查是否需要脱敏和加密处理
        if (!hasEncryptFields(entity.getClass())) {
            return entity;
        }

        try {
            // 使用包装器方式，保留原数据并添加脱敏和加密字段
            return MaskResponseWrapper.wrap(entity, maskService, frontendCryptoUtil);

        } catch (Exception e) {
            log.error("实体脱敏和加密失败: {}", e.getMessage(), e);
            return entity; // 处理失败时返回原实体
        }
    }

    /**
     * 检查实体类是否包含加密字段
     */
    private boolean hasEncryptFields(Class<?> entityClass) {
        if (entityClass == null) {
            return false;
        }

        try {
            Field[] fields = entityClass.getDeclaredFields();
            for (Field field : fields) {
                if (field.isAnnotationPresent(EncryptField.class)) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            log.debug("检查实体加密字段失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查前后端加密工具是否可用
     */
    public boolean isCryptoUtilAvailable() {
        return frontendCryptoUtil != null;
    }

    /**
     * 获取加密工具状态信息
     */
    public String getCryptoUtilStatus() {
        if (frontendCryptoUtil == null) {
            return "前后端加密工具未启用";
        }

        try {
            boolean capabilities = frontendCryptoUtil.checkCryptoCapabilities();
            String statistics = frontendCryptoUtil.getCryptoStatistics();
            return String.format("前后端加密工具已启用 - 能力检查: %s, 统计: %s", capabilities, statistics);
        } catch (Exception e) {
            return "前后端加密工具状态检查失败: " + e.getMessage();
        }
    }

}
