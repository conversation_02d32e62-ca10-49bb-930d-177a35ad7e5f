package com.hys.hm.shared.encrypt.config;

import com.hys.hm.shared.encrypt.aspect.MaskResponseAspect;
import com.hys.hm.shared.encrypt.listener.FrameworkEncryptEntityListener;
import com.hys.hm.shared.encrypt.service.*;
import com.hys.hm.shared.encrypt.service.impl.EncryptFieldServiceImpl;
import com.hys.hm.shared.encrypt.service.impl.MaskServiceImpl;
import com.hys.hm.shared.encrypt.util.FrontendCryptoUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Framework层加密功能自动配置
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Configuration
@ConditionalOnProperty(name = "hm.framework.encrypt.enabled", havingValue = "true", matchIfMissing = true)
@Slf4j
public class EncryptAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public MaskService maskService() {
        log.info("初始化Framework脱敏服务");
        return new MaskServiceImpl();
    }

    @Bean
    @ConditionalOnMissingBean
    public FrameworkMaskProcessor frameworkMaskProcessor(MaskService maskService) {
        log.info("初始化Framework脱敏处理器");
        return new FrameworkMaskProcessor(maskService);
    }

    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnClass(EncryptService.class)
    public FrameworkEncryptIndexService frameworkEncryptIndexService(EncryptService encryptService, EncryptFieldServiceImpl encryptFieldService) {
        log.info("初始化Framework加密索引服务");
        return new FrameworkEncryptIndexService(encryptService, encryptFieldService);
    }

    @Bean
    @ConditionalOnMissingBean
    public FrameworkEncryptEntityListener frameworkEncryptEntityListener() {
        log.info("初始化Framework加密实体监听器");
        return new FrameworkEncryptEntityListener();
    }

    @Bean
    @ConditionalOnMissingBean
    public EncryptFieldQueryHelper encryptFieldQueryHelper() {
        log.info("初始化Framework加密字段查询助手");
        return new EncryptFieldQueryHelper();
    }

    @Bean
    @ConditionalOnMissingBean
    public MaskResponseAspect maskResponseAspect(MaskService maskService) {
        log.info("初始化Framework响应脱敏切面");
        return new MaskResponseAspect(maskService);
    }

    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(name = "app.security.frontend.enabled", havingValue = "true", matchIfMissing = true)
    public FrontendCryptoUtil frontendCryptoUtil() {
        log.info("初始化前后端交互加密工具");
        return new FrontendCryptoUtil();
    }

}
