package com.hys.hm.shared.encrypt.service;

import com.hys.hm.shared.encrypt.annotation.EncryptField;
import com.hys.hm.shared.encrypt.service.impl.EncryptFieldServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Framework层加密索引服务
 * 自动管理加密字段的搜索索引
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Service
@Slf4j
public class FrameworkEncryptIndexService {

    private final EncryptService encryptService;
    private final EncryptFieldServiceImpl encryptFieldService;

    public FrameworkEncryptIndexService(EncryptService encryptService, EncryptFieldServiceImpl encryptFieldService) {
        this.encryptService = encryptService;
        this.encryptFieldService = encryptFieldService;
    }

    /**
     * 创建加密索引
     */
    public void createEncryptIndex(Object entity) {
        CompletableFuture.runAsync(() -> {
            try {
                String entityId = getEntityId(entity);
                if (!StringUtils.hasText(entityId)) {
                    return;
                }

                String entityName = entity.getClass().getSimpleName();
                log.debug("创建加密索引: entity={}, id={}", entityName, entityId);

                processEncryptFields(entity, entityId, entityName, IndexOperation.CREATE);

            } catch (Exception e) {
                log.error("创建加密索引失败: {}", e.getMessage(), e);
            }
        });
    }

    /**
     * 更新加密索引
     */
    public void updateEncryptIndex(Object entity) {
        CompletableFuture.runAsync(() -> {
            try {
                String entityId = getEntityId(entity);
                if (!StringUtils.hasText(entityId)) {
                    return;
                }

                String entityName = entity.getClass().getSimpleName();
                log.debug("更新加密索引: entity={}, id={}", entityName, entityId);

                // 先删除旧索引，再创建新索引
                processEncryptFields(entity, entityId, entityName, IndexOperation.DELETE);
                processEncryptFields(entity, entityId, entityName, IndexOperation.CREATE);

            } catch (Exception e) {
                log.error("更新加密索引失败: {}", e.getMessage(), e);
            }
        });
    }

    /**
     * 删除加密索引
     */
    public void deleteEncryptIndex(Object entity) {
        CompletableFuture.runAsync(() -> {
            try {
                String entityId = getEntityId(entity);
                if (!StringUtils.hasText(entityId)) {
                    return;
                }

                String entityName = entity.getClass().getSimpleName();
                log.debug("删除加密索引: entity={}, id={}", entityName, entityId);

                processEncryptFields(entity, entityId, entityName, IndexOperation.DELETE);

            } catch (Exception e) {
                log.error("删除加密索引失败: {}", e.getMessage(), e);
            }
        });
    }

    /**
     * 处理加密字段索引
     */
    private void processEncryptFields(Object entity, String entityId, String entityName, IndexOperation operation) {
        try {
            Field[] fields = entity.getClass().getDeclaredFields();

            for (Field field : fields) {
                if (field.isAnnotationPresent(EncryptField.class)) {
                    EncryptField encryptField = field.getAnnotation(EncryptField.class);

                    field.setAccessible(true);
                    Object value = field.get(entity);

                    if (value != null && StringUtils.hasText(value.toString())) {
                        String fieldValue = value.toString();
                        String fieldName = field.getName();

                        processFieldIndex(entityName, entityId, fieldName, fieldValue, encryptField, operation);
                    }
                }
            }

        } catch (Exception e) {
            log.error("处理加密字段索引失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理单个字段的索引
     */
    private void processFieldIndex(String entityName, String entityId, String fieldName,
                                 String fieldValue, EncryptField encryptField, IndexOperation operation) {
        try {
            switch (operation) {
                case CREATE:
                    createFieldIndex(entityName, entityId, fieldName, fieldValue, encryptField);
                    break;
                case DELETE:
                    deleteFieldIndex(entityName, entityId, fieldName);
                    break;
            }
        } catch (Exception e) {
            log.error("处理字段索引失败: entity={}, field={}, operation={}, error={}",
                     entityName, fieldName, operation, e.getMessage());
        }
    }

    /**
     * 创建字段索引
     */
    private void createFieldIndex(String entityName, String entityId, String fieldName,
                                String fieldValue, EncryptField encryptField) {
        try {
            // 创建精确查询的哈希索引
            String exactHash = encryptService.generateExactHash(fieldValue);
            saveFieldHash(entityName, entityId, fieldName, exactHash);

            // 如果支持模糊查询，创建分词索引
            if (encryptField.fuzzySearch()) {
                List<String> fuzzyTokens = encryptService.generateFuzzyTokens(
                    fieldValue,
                    encryptField.tokenLength()
                );

                for (String token : fuzzyTokens) {
                    saveFuzzyToken(entityName, entityId, fieldName, token);
                }
            }

            log.debug("创建字段索引成功: entity={}, id={}, field={}, fuzzy={}",
                     entityName, entityId, fieldName, encryptField.fuzzySearch());

        } catch (Exception e) {
            log.error("创建字段索引失败: entity={}, field={}, error={}",
                     entityName, fieldName, e.getMessage());
        }
    }

    /**
     * 删除字段索引
     */
    private void deleteFieldIndex(String entityName, String entityId, String fieldName) {
        try {
            log.debug("删除字段索引: entity={}, id={}, field={}", entityName, entityId, fieldName);

            if (encryptFieldService != null) {
                // 使用反射调用infrastructure层的删除方法
                invokeMethod(encryptFieldService, "deleteSearchIndex", entityName, entityId);
            } else {
                log.debug("EncryptFieldService不可用，跳过索引删除");
            }

        } catch (Exception e) {
            log.error("删除字段索引失败: entity={}, field={}, error={}",
                     entityName, fieldName, e.getMessage());
        }
    }

    /**
     * 保存字段哈希
     */
    private void saveFieldHash(String entityName, String entityId, String fieldName, String hash) {
        try {
            log.debug("保存字段哈希: entity={}, id={}, field={}, hash=[HASH]",
                     entityName, entityId, fieldName);

            if (encryptFieldService != null) {
                // 使用反射调用infrastructure层的保存方法
                invokeMethod(encryptFieldService, "saveFieldHash", entityName, entityId, fieldName, hash);
            } else {
                log.debug("EncryptFieldService不可用，跳过哈希保存");
            }

        } catch (Exception e) {
            log.error("保存字段哈希失败: entity={}, field={}, error={}",
                     entityName, fieldName, e.getMessage());
        }
    }

    /**
     * 保存模糊查询分词
     */
    private void saveFuzzyToken(String entityName, String entityId, String fieldName, String token) {
        try {
            log.debug("保存模糊分词: entity={}, id={}, field={}, token=[TOKEN]",
                     entityName, entityId, fieldName);

            if (encryptFieldService != null) {
                // 使用反射调用infrastructure层的保存方法
                invokeMethod(encryptFieldService, "saveFuzzyToken", entityName, entityId, fieldName, token);
            } else {
                log.debug("EncryptFieldService不可用，跳过分词保存");
            }

        } catch (Exception e) {
            log.error("保存模糊分词失败: entity={}, field={}, error={}",
                     entityName, fieldName, e.getMessage());
        }
    }

    /**
     * 使用反射调用方法
     */
    private void invokeMethod(Object target, String methodName, Object... args) {
        try {
            Class<?> targetClass = target.getClass();
            Class<?>[] paramTypes = new Class[args.length];
            for (int i = 0; i < args.length; i++) {
                paramTypes[i] = args[i] != null ? args[i].getClass() : String.class;
            }

            Method method = targetClass.getMethod(methodName, paramTypes);
            method.invoke(target, args);

        } catch (Exception e) {
            log.debug("反射调用方法失败: method={}, error={}", methodName, e.getMessage());
        }
    }

    /**
     * 获取实体ID
     */
    private String getEntityId(Object entity) {
        try {
            // 尝试调用getId方法
            Method getIdMethod = entity.getClass().getMethod("getId");
            Object id = getIdMethod.invoke(entity);
            return id != null ? id.toString() : null;
        } catch (Exception e) {
            log.debug("获取实体ID失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 索引操作类型
     */
    private enum IndexOperation {
        CREATE, DELETE
    }
}
