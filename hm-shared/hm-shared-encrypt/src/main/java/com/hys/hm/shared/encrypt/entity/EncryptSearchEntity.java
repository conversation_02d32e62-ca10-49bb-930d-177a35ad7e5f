package com.hys.hm.shared.encrypt.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.Comment;

/**
 * 加密字段搜索索引实体
 * 用于支持加密字段的模糊查询
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
@Entity
@Table(name = "encrypt_search_index", indexes = {
        @Index(name = "idx_entity_field", columnList = "entityType,fieldName"),
        @Index(name = "idx_token_hash", columnList = "tokenHash"),
        @Index(name = "idx_exact_hash", columnList = "exactHash"),
        @Index(name = "idx_entity_id", columnList = "entityId")
})
@Data
@Comment("加密字段搜索索引表")
public class EncryptSearchEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("主键ID")
    private Long id;

    @Column(length = 50, nullable = false)
    @Comment("实体类型")
    private String entityType;

    @Column(length = 32, nullable = false)
    @Comment("实体ID")
    private String entityId;

    @Column(length = 50, nullable = false)
    @Comment("字段名称")
    private String fieldName;

    @Column(length = 100)
    @Comment("分词哈希值（用于模糊查询）")
    private String tokenHash;

    @Column(length = 100)
    @Comment("精确哈希值（用于精确查询）")
    private String exactHash;

    @Column(nullable = false)
    @Comment("创建时间")
    private java.time.LocalDateTime createTime;

    @Column(nullable = false)
    @Comment("更新时间")
    private java.time.LocalDateTime updateTime;

    /**
     * 创建搜索索引
     */
    public static EncryptSearchEntity create(String entityType, String entityId,
                                           String fieldName, String tokenHash, String exactHash) {
        EncryptSearchEntity entity = new EncryptSearchEntity();
        entity.entityType = entityType;
        entity.entityId = entityId;
        entity.fieldName = fieldName;
        entity.tokenHash = tokenHash;
        entity.exactHash = exactHash;
        entity.createTime = java.time.LocalDateTime.now();
        entity.updateTime = java.time.LocalDateTime.now();
        return entity;
    }
}
