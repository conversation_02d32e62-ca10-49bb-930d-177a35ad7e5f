package com.hys.hm.shared.encrypt.listener;

import com.hys.hm.shared.encrypt.annotation.EncryptField;
import com.hys.hm.shared.encrypt.service.EncryptService;
import com.hys.hm.shared.encrypt.service.FrameworkEncryptIndexService;
import jakarta.persistence.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;

/**
 * Framework层加密实体监听器
 * 自动处理实体的加密字段
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Component
@Slf4j
public class FrameworkEncryptEntityListener {

    private static EncryptService encryptService;
    private static FrameworkEncryptIndexService encryptIndexService;

    @Autowired
    public void setEncryptService(EncryptService encryptService) {
        FrameworkEncryptEntityListener.encryptService = encryptService;
    }

    @Autowired
    public void setEncryptIndexService(FrameworkEncryptIndexService encryptIndexService) {
        FrameworkEncryptEntityListener.encryptIndexService = encryptIndexService;
    }

    /**
     * 保存前加密
     */
    @PrePersist
    public void prePersist(Object entity) {
        log.debug("PrePersist: 加密实体字段 - {}", entity.getClass().getSimpleName());
        encryptFields(entity);
    }

    /**
     * 更新前加密
     */
    @PreUpdate
    public void preUpdate(Object entity) {
        log.debug("PreUpdate: 加密实体字段 - {}", entity.getClass().getSimpleName());
        encryptFields(entity);
    }

    /**
     * 保存后创建索引
     */
    @PostPersist
    public void postPersist(Object entity) {
        log.debug("PostPersist: 创建加密索引 - {}", entity.getClass().getSimpleName());
        if (encryptIndexService != null) {
            encryptIndexService.createEncryptIndex(entity);
        }
    }

    /**
     * 更新后更新索引
     */
    @PostUpdate
    public void postUpdate(Object entity) {
        log.debug("PostUpdate: 更新加密索引 - {}", entity.getClass().getSimpleName());
        if (encryptIndexService != null) {
            encryptIndexService.updateEncryptIndex(entity);
        }
    }

    /**
     * 删除后清理索引
     */
    @PostRemove
    public void postRemove(Object entity) {
        log.debug("PostRemove: 清理加密索引 - {}", entity.getClass().getSimpleName());
        if (encryptIndexService != null) {
            encryptIndexService.deleteEncryptIndex(entity);
        }
    }

    /**
     * 查询后解密
     */
    @PostLoad
    public void postLoad(Object entity) {
        log.debug("PostLoad: 解密实体字段 - {}", entity.getClass().getSimpleName());
        decryptFields(entity);
    }

    /**
     * 加密实体字段
     */
    private void encryptFields(Object entity) {
        if (entity == null || encryptService == null) {
            return;
        }

        try {
            Field[] fields = entity.getClass().getDeclaredFields();

            for (Field field : fields) {
                if (field.isAnnotationPresent(EncryptField.class)) {
                    EncryptField encryptField = field.getAnnotation(EncryptField.class);

                    field.setAccessible(true);
                    Object value = field.get(entity);

                    if (value != null && StringUtils.hasText(value.toString())) {
                        String plainText = value.toString();

                        // 检查是否已经加密（避免重复加密）
                        if (!isEncrypted(plainText)) {
                            String encryptedText = encryptService.encrypt(plainText, encryptField.type());
                            field.set(entity, encryptedText);

                            log.debug("加密字段: {}#{} -> [ENCRYPTED]",
                                     entity.getClass().getSimpleName(), field.getName());
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("加密实体字段失败: {}", e.getMessage(), e);
            // 不抛异常，避免影响正常业务
        }
    }

    /**
     * 解密实体字段
     */
    private void decryptFields(Object entity) {
        if (entity == null || encryptService == null) {
            return;
        }

        try {
            Field[] fields = entity.getClass().getDeclaredFields();

            for (Field field : fields) {
                if (field.isAnnotationPresent(EncryptField.class)) {
                    EncryptField encryptField = field.getAnnotation(EncryptField.class);

                    field.setAccessible(true);
                    Object value = field.get(entity);

                    if (value != null && StringUtils.hasText(value.toString())) {
                        String encryptedText = value.toString();

                        try {
                            String plainText = encryptService.decrypt(encryptedText, encryptField.type());
                            field.set(entity, plainText);

                            log.debug("解密字段: {}#{} -> [DECRYPTED]",
                                     entity.getClass().getSimpleName(), field.getName());
                        } catch (Exception e) {
                            log.debug("解密字段失败，可能是未加密的历史数据: {}#{}",
                                     entity.getClass().getSimpleName(), field.getName());
                            // 解密失败时保持原值，可能是未加密的历史数据
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("解密实体字段失败: {}", e.getMessage(), e);
            // 不抛异常，避免影响正常业务
        }
    }

    /**
     * 简单判断字符串是否已加密
     * 这里使用简单的启发式方法，实际项目中可以使用更复杂的判断逻辑
     */
    private boolean isEncrypted(String text) {
        if (!StringUtils.hasText(text)) {
            return false;
        }

        // 简单判断：如果包含特定的加密前缀或者长度明显超出正常范围
        return text.startsWith("ENC:") ||
               (text.length() > 50 && !text.matches(".*[\\u4e00-\\u9fa5].*")); // 不包含中文且长度过长
    }
}
