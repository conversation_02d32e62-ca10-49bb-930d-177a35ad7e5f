package com.hys.hm.shared.encrypt.config;

import com.hys.hm.shared.encrypt.util.FrontendCryptoUtil;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;


/**
 * 前后端交互加密配置类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Slf4j
@Configuration
@EnableScheduling
@ConfigurationProperties(prefix = "app.security.frontend")
@ConditionalOnProperty(name = "app.security.frontend.enabled", havingValue = "true", matchIfMissing = true)
public class FrontendCryptoConfig {

    @Resource
    private FrontendCryptoUtil frontendCryptoUtil;

    /**
     * 前端加密配置属性
     */
    public static class CryptoProperties {
        private boolean enabled = true;
        private String aesKey;
        private String rsaPrivateKey;
        private String rsaPublicKey;
        private long keyRotationInterval = 3600; // 1小时
        private long timestampTolerance = 300;   // 5分钟
        private boolean autoKeyRotation = true;
        private String defaultAlgorithm = "AES_256_GCM";

        // Getters and Setters
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }
        public String getAesKey() { return aesKey; }
        public void setAesKey(String aesKey) { this.aesKey = aesKey; }
        public String getRsaPrivateKey() { return rsaPrivateKey; }
        public void setRsaPrivateKey(String rsaPrivateKey) { this.rsaPrivateKey = rsaPrivateKey; }
        public String getRsaPublicKey() { return rsaPublicKey; }
        public void setRsaPublicKey(String rsaPublicKey) { this.rsaPublicKey = rsaPublicKey; }
        public long getKeyRotationInterval() { return keyRotationInterval; }
        public void setKeyRotationInterval(long keyRotationInterval) { this.keyRotationInterval = keyRotationInterval; }
        public long getTimestampTolerance() { return timestampTolerance; }
        public void setTimestampTolerance(long timestampTolerance) { this.timestampTolerance = timestampTolerance; }
        public boolean isAutoKeyRotation() { return autoKeyRotation; }
        public void setAutoKeyRotation(boolean autoKeyRotation) { this.autoKeyRotation = autoKeyRotation; }
        public String getDefaultAlgorithm() { return defaultAlgorithm; }
        public void setDefaultAlgorithm(String defaultAlgorithm) { this.defaultAlgorithm = defaultAlgorithm; }
    }

    @Bean
    @ConditionalOnProperty(name = "app.security.frontend.enabled", havingValue = "true")
    public CryptoProperties cryptoProperties() {
        return new CryptoProperties();
    }

    /**
     * 初始化加密配置
     */
    @PostConstruct
    public void initCryptoConfig() {
        log.info("初始化前后端交互加密配置...");

        // 检查加密能力
        if (frontendCryptoUtil != null && frontendCryptoUtil.checkCryptoCapabilities()) {
            log.info("加密能力检查通过");
            log.info("加密统计信息: {}", frontendCryptoUtil.getCryptoStatistics());
        } else {
            log.warn("加密能力检查失败，请检查加密库配置");
        }

        log.info("前后端交互加密配置初始化完成");
    }

    /**
     * 定时清理过期密钥
     * 每小时执行一次
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    @ConditionalOnProperty(name = "app.security.frontend.auto-key-rotation", havingValue = "true", matchIfMissing = true)
    public void cleanupExpiredKeys() {
        if (frontendCryptoUtil != null) {
            try {
                frontendCryptoUtil.cleanupExpiredKeys();
                log.debug("定时清理过期密钥完成");
            } catch (Exception e) {
                log.error("定时清理过期密钥失败: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * 定时输出加密统计信息
     * 每天执行一次
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点
    public void logCryptoStatistics() {
        if (frontendCryptoUtil != null) {
            try {
                String stats = frontendCryptoUtil.getCryptoStatistics();
                log.info("加密统计信息: {}", stats);
            } catch (Exception e) {
                log.error("获取加密统计信息失败: {}", e.getMessage(), e);
            }
        }
    }
}
