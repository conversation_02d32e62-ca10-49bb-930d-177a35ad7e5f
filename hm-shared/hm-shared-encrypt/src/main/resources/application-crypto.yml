# 前后端交互加密配置
app:
  security:
    frontend:
      # 是否启用前后端交互加密
      enabled: true
      
      # AES密钥（Base64编码，256位）
      # 生产环境中应该从安全的密钥管理系统获取
      aes-key: ${FRONTEND_AES_KEY:}
      
      # RSA私钥（Base64编码，PKCS8格式）
      rsa-private-key: ${FRONTEND_RSA_PRIVATE_KEY:}
      
      # RSA公钥（Base64编码，X509格式）
      rsa-public-key: ${FRONTEND_RSA_PUBLIC_KEY:}
      
      # 密钥轮换间隔（秒）
      key-rotation-interval: ${FRONTEND_KEY_ROTATION_INTERVAL:3600}
      
      # 时间戳容忍度（秒）- 防重放攻击
      timestamp-tolerance: ${FRONTEND_TIMESTAMP_TOLERANCE:300}
      
      # 是否启用自动密钥轮换
      auto-key-rotation: ${FRONTEND_AUTO_KEY_ROTATION:true}
      
      # 默认加密算法
      default-algorithm: ${FRONTEND_DEFAULT_ALGORITHM:AES_256_GCM}

# 日志配置
logging:
  level:
    com.hys.hm.shared.encrypt: DEBUG
    org.bouncycastle: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# Spring Boot配置
spring:
  task:
    scheduling:
      pool:
        size: 2
      thread-name-prefix: "crypto-scheduler-"
