# 前后端交互加密工具使用说明

## 概述

本工具类 `FrontendCryptoUtil` 是专为前后端数据交互设计的加密解决方案，符合等保三级要求，提供多种加密算法和安全特性。

## 主要特性

### 1. 支持的加密算法
- **AES-256-GCM**: 高强度对称加密，提供认证加密，防止数据篡改
- **RSA-2048**: 非对称加密，适用于密钥交换和小数据量加密
- **SM4-128**: 国密算法，符合国产化要求
- **混合加密**: RSA+AES组合，兼顾安全性和性能

### 2. 安全特性
- 时间戳防重放攻击
- 数据完整性验证
- 密钥轮换机制
- 加密强度可配置
- 异常安全处理

## 快速开始

### 1. 添加依赖

在项目中引入 `hm-shared-encrypt` 模块：

```xml
<dependency>
    <groupId>com.hys</groupId>
    <artifactId>hm-shared-encrypt</artifactId>
    <version>${project.version}</version>
</dependency>
```

### 2. 配置文件

在 `application.yml` 中添加配置：

```yaml
app:
  security:
    frontend:
      enabled: true
      aes-key: "your-base64-encoded-aes-key"
      rsa-private-key: "your-base64-encoded-rsa-private-key"
      rsa-public-key: "your-base64-encoded-rsa-public-key"
      key-rotation-interval: 3600
      timestamp-tolerance: 300
```

### 3. 基本使用

```java
@Resource
private FrontendCryptoUtil frontendCryptoUtil;

// AES加密
String plainText = "敏感数据";
String keyId = "user_session_001";
EncryptedData encrypted = frontendCryptoUtil.encryptWithAES(plainText, keyId);

// AES解密
String decrypted = frontendCryptoUtil.decryptWithAES(encrypted, keyId);
```

## 详细使用指南

### 1. AES-256-GCM 加密

```java
// 加密
String originalData = "用户手机号：13812345678";
String keyId = "user_mobile_key";
EncryptedData encryptedData = frontendCryptoUtil.encryptWithAES(originalData, keyId);

// 获取加密结果
String cipherText = encryptedData.getCipherText();  // 密文
String iv = encryptedData.getIv();                  // 初始向量
String timestamp = encryptedData.getTimestamp();    // 时间戳
String signature = encryptedData.getSignature();    // 签名

// 解密
String decryptedData = frontendCryptoUtil.decryptWithAES(encryptedData, keyId);
```

### 2. RSA 加密（适用于密钥交换）

```java
// 生成RSA密钥对
String[] keyPair = frontendCryptoUtil.generateRSAKeyPair();
String publicKey = keyPair[0];
String privateKey = keyPair[1];

// 加密（适用于小数据量）
String plainText = "AES密钥或其他小数据";
String encrypted = frontendCryptoUtil.encryptWithRSA(plainText);

// 解密
String decrypted = frontendCryptoUtil.decryptWithRSA(encrypted);
```

### 3. 国密SM4 加密

```java
// SM4加密
String originalData = "符合国产化要求的数据";
String keyId = "sm4_key_001";
EncryptedData encryptedData = frontendCryptoUtil.encryptWithSM4(originalData, keyId);

// SM4解密
String decryptedData = frontendCryptoUtil.decryptWithSM4(encryptedData, keyId);
```

### 4. 混合加密（推荐用于大数据量）

```java
// 混合加密
String largeData = "大量数据内容...";
HybridEncryptedData hybridData = frontendCryptoUtil.hybridEncrypt(largeData);

// 获取结果
EncryptedData aesEncryptedData = hybridData.getEncryptedData();  // AES加密的数据
String rsaEncryptedKey = hybridData.getEncryptedKey();           // RSA加密的AES密钥

// 混合解密
String decryptedData = frontendCryptoUtil.hybridDecrypt(hybridData);
```

## 前端集成指南

### 1. JavaScript 示例

```javascript
// 前端接收加密数据
const encryptedResponse = {
    cipherText: "base64-encoded-cipher-text",
    iv: "base64-encoded-iv",
    timestamp: "1627834567",
    signature: "base64-encoded-signature",
    algorithm: "AES-256-GCM"
};

// 前端需要实现对应的解密逻辑
// 建议使用 crypto-js 或 Web Crypto API
```

### 2. 前端加密库推荐

- **crypto-js**: 支持AES-GCM加密
- **Web Crypto API**: 浏览器原生加密API
- **node-forge**: 支持RSA和AES加密
- **sm-crypto**: 国密算法JavaScript实现

## 安全最佳实践

### 1. 密钥管理
- 生产环境中密钥应存储在安全的密钥管理系统中
- 定期轮换密钥
- 不要在代码中硬编码密钥

### 2. 传输安全
- 始终使用HTTPS传输加密数据
- 验证时间戳防止重放攻击
- 验证数据签名确保完整性

### 3. 错误处理
- 不要在错误信息中泄露敏感信息
- 记录安全相关的操作日志
- 实现适当的异常处理机制

## 性能考虑

### 1. 算法选择
- **小数据量**: 使用RSA直接加密
- **大数据量**: 使用混合加密（RSA+AES）
- **高性能要求**: 使用AES-256-GCM
- **国产化要求**: 使用SM4算法

### 2. 缓存策略
- 密钥会自动缓存，避免重复生成
- 支持密钥过期和自动清理
- 可配置密钥轮换间隔

## 故障排除

### 1. 常见问题

**问题**: 加密失败，提示"密钥未配置"
**解决**: 检查配置文件中的密钥配置是否正确

**问题**: 解密失败，提示"时间戳验证失败"
**解决**: 检查系统时间是否同步，调整时间戳容忍度

**问题**: SM4算法不可用
**解决**: 确保BouncyCastle库已正确添加到classpath

### 2. 调试模式

启用调试日志：

```yaml
logging:
  level:
    com.hys.hm.shared.encrypt: DEBUG
```

## 示例代码

完整的使用示例请参考 `FrontendCryptoExample` 类，包含了所有加密算法的使用演示。

## 技术支持

如有问题，请联系开发团队或查看相关文档。
