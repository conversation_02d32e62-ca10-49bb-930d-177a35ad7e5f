package com.hys.hm.shared.common.util.system;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.regex.Pattern;

/**
 * IP地址工具类
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-22
 */
@Slf4j
public class IPUtils {
    
    private static final String UNKNOWN = "unknown";
    private static final String LOCALHOST_IPV4 = "127.0.0.1";
    private static final String LOCALHOST_IPV6 = "0:0:0:0:0:0:0:1";
    private static final String SEPARATOR = ",";
    
    // IP地址正则表达式
    private static final Pattern IP_PATTERN = Pattern.compile(
        "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$"
    );
    
    /**
     * 获取客户端真实IP地址
     * 支持通过代理服务器的情况
     */
    public static String getIpAddress(HttpServletRequest request) {
        if (request == null) {
            return UNKNOWN;
        }
        
        String ip = null;
        
        // 1. 检查 X-Forwarded-For 头（最常用的代理头）
        ip = request.getHeader("X-Forwarded-For");
        if (isValidIp(ip)) {
            // 可能包含多个IP，取第一个
            return getFirstValidIp(ip);
        }
        
        // 2. 检查 Proxy-Client-IP 头
        ip = request.getHeader("Proxy-Client-IP");
        if (isValidIp(ip)) {
            return ip;
        }
        
        // 3. 检查 WL-Proxy-Client-IP 头（WebLogic）
        ip = request.getHeader("WL-Proxy-Client-IP");
        if (isValidIp(ip)) {
            return ip;
        }
        
        // 4. 检查 HTTP_CLIENT_IP 头
        ip = request.getHeader("HTTP_CLIENT_IP");
        if (isValidIp(ip)) {
            return ip;
        }
        
        // 5. 检查 HTTP_X_FORWARDED_FOR 头
        ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        if (isValidIp(ip)) {
            return getFirstValidIp(ip);
        }
        
        // 6. 检查 X-Real-IP 头（Nginx）
        ip = request.getHeader("X-Real-IP");
        if (isValidIp(ip)) {
            return ip;
        }
        
        // 7. 最后使用 getRemoteAddr()
        ip = request.getRemoteAddr();
        
        // 处理本地地址
        if (LOCALHOST_IPV6.equals(ip)) {
            ip = LOCALHOST_IPV4;
        }
        
        // 如果是本地地址，尝试获取本机真实IP
        if (LOCALHOST_IPV4.equals(ip)) {
            try {
                InetAddress inet = InetAddress.getLocalHost();
                ip = inet.getHostAddress();
            } catch (UnknownHostException e) {
                log.warn("获取本机IP失败: {}", e.getMessage());
            }
        }
        
        return ip;
    }
    
    /**
     * 验证IP地址是否有效
     */
    private static boolean isValidIp(String ip) {
        return StringUtils.isNotBlank(ip) && 
               !UNKNOWN.equalsIgnoreCase(ip) && 
               isValidIpFormat(ip);
    }
    
    /**
     * 从多个IP中获取第一个有效IP
     */
    private static String getFirstValidIp(String ips) {
        if (StringUtils.isBlank(ips)) {
            return UNKNOWN;
        }
        
        String[] ipArray = ips.split(SEPARATOR);
        for (String ip : ipArray) {
            ip = ip.trim();
            if (isValidIp(ip)) {
                return ip;
            }
        }
        
        return UNKNOWN;
    }
    
    /**
     * 验证IP地址格式
     */
    public static boolean isValidIpFormat(String ip) {
        if (StringUtils.isBlank(ip)) {
            return false;
        }
        
        // 检查IPv4格式
        if (IP_PATTERN.matcher(ip).matches()) {
            return true;
        }
        
        // 检查是否为IPv6格式（简单检查）
        return ip.contains(":") && ip.length() <= 39;
    }
    
    /**
     * 判断是否为内网IP
     */
    public static boolean isInternalIp(String ip) {
        if (StringUtils.isBlank(ip) || !isValidIpFormat(ip)) {
            return false;
        }
        
        // 本地地址
        if (LOCALHOST_IPV4.equals(ip) || "localhost".equals(ip)) {
            return true;
        }
        
        // IPv6本地地址
        if (LOCALHOST_IPV6.equals(ip) || "::1".equals(ip)) {
            return true;
        }
        
        // 私有IP段
        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }
        
        try {
            int first = Integer.parseInt(parts[0]);
            int second = Integer.parseInt(parts[1]);
            
            // 10.0.0.0 - **************
            if (first == 10) {
                return true;
            }
            
            // ********** - **************
            if (first == 172 && second >= 16 && second <= 31) {
                return true;
            }
            
            // *********** - ***************
            if (first == 192 && second == 168) {
                return true;
            }
            
        } catch (NumberFormatException e) {
            return false;
        }
        
        return false;
    }
    
    /**
     * 获取本机IP地址
     */
    public static String getLocalIpAddress() {
        try {
            InetAddress inet = InetAddress.getLocalHost();
            return inet.getHostAddress();
        } catch (UnknownHostException e) {
            log.error("获取本机IP地址失败: {}", e.getMessage(), e);
            return LOCALHOST_IPV4;
        }
    }
    
    /**
     * 获取本机主机名
     */
    public static String getLocalHostName() {
        try {
            InetAddress inet = InetAddress.getLocalHost();
            return inet.getHostName();
        } catch (UnknownHostException e) {
            log.error("获取本机主机名失败: {}", e.getMessage(), e);
            return "localhost";
        }
    }
    
    /**
     * IP地址转长整型
     */
    public static long ipToLong(String ip) {
        if (!isValidIpFormat(ip)) {
            return 0L;
        }
        
        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return 0L;
        }
        
        try {
            long result = 0L;
            for (int i = 0; i < 4; i++) {
                result = result << 8 | Integer.parseInt(parts[i]);
            }
            return result;
        } catch (NumberFormatException e) {
            return 0L;
        }
    }
    
    /**
     * 长整型转IP地址
     */
    public static String longToIp(long ip) {
        return ((ip >> 24) & 0xFF) + "." +
               ((ip >> 16) & 0xFF) + "." +
               ((ip >> 8) & 0xFF) + "." +
               (ip & 0xFF);
    }
}