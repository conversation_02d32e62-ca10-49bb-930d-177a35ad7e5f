package com.hys.hm.shared.common.util;

import lombok.extern.slf4j.Slf4j;

/**
 * 数据脱敏工具类
 * 提供各种敏感数据的脱敏处理
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
@Slf4j
public class DataMaskingUtil {
    
    private static final String MASK_CHAR = "*";
    
    /**
     * 手机号脱敏
     * 保留前3位和后4位，中间用*替换
     * 例：13812345678 -> 138****5678
     */
    public static String maskPhone(String phone) {
        if (phone == null || phone.length() < 7) {
            return phone;
        }
        
        if (phone.length() == 11) {
            return phone.substring(0, 3) + "****" + phone.substring(7);
        } else {
            // 其他长度的手机号，保留前3位和后1位
            return phone.substring(0, Math.min(3, phone.length())) + 
                   "****" + 
                   phone.substring(Math.max(phone.length() - 1, 3));
        }
    }
    
    /**
     * 身份证号脱敏
     * 保留前4位和后4位，中间用*替换
     * 例：110101199001011234 -> 1101****1234
     */
    public static String maskIdCard(String idCard) {
        if (idCard == null || idCard.length() < 8) {
            return idCard;
        }
        
        if (idCard.length() == 18) {
            return idCard.substring(0, 4) + "**********" + idCard.substring(14);
        } else if (idCard.length() == 15) {
            return idCard.substring(0, 4) + "*******" + idCard.substring(11);
        } else {
            // 其他长度的身份证号
            int keepStart = Math.min(4, idCard.length() / 3);
            int keepEnd = Math.min(4, idCard.length() / 3);
            return idCard.substring(0, keepStart) + 
                   "****" + 
                   idCard.substring(Math.max(idCard.length() - keepEnd, keepStart));
        }
    }
    
    /**
     * 姓名脱敏
     * 保留姓氏，名字用*替换
     * 例：张三丰 -> 张**
     */
    public static String maskName(String name) {
        if (name == null || name.length() <= 1) {
            return name;
        }
        
        if (name.length() == 2) {
            return name.substring(0, 1) + "*";
        } else {
            return name.substring(0, 1) + "*".repeat(name.length() - 1);
        }
    }
    
    /**
     * 邮箱脱敏
     * 保留用户名前2位和@后的域名，中间用*替换
     * 例：<EMAIL> -> ex****@gmail.com
     */
    public static String maskEmail(String email) {
        if (email == null || !email.contains("@")) {
            return email;
        }
        
        String[] parts = email.split("@");
        if (parts.length != 2) {
            return email;
        }
        
        String username = parts[0];
        String domain = parts[1];
        
        if (username.length() <= 2) {
            return "**@" + domain;
        } else {
            return username.substring(0, 2) + "****@" + domain;
        }
    }
    
    /**
     * 银行卡号脱敏
     * 保留前4位和后4位，中间用*替换
     * 例：**************** -> 6222****7890
     */
    public static String maskBankCard(String bankCard) {
        if (bankCard == null || bankCard.length() < 8) {
            return bankCard;
        }
        
        return bankCard.substring(0, 4) + 
               "****" + 
               bankCard.substring(bankCard.length() - 4);
    }
    
    /**
     * 地址脱敏
     * 保留省市，详细地址用*替换
     * 例：北京市朝阳区某某街道123号 -> 北京市朝阳区****
     */
    public static String maskAddress(String address) {
        if (address == null || address.length() <= 6) {
            return address;
        }
        
        // 简单的地址脱敏，保留前6个字符
        return address.substring(0, 6) + "****";
    }
    
    /**
     * 通用脱敏方法
     * 根据数据长度自动选择脱敏策略
     */
    public static String maskGeneric(String data, int keepStart, int keepEnd) {
        if (data == null || data.length() <= keepStart + keepEnd) {
            return data;
        }
        
        return data.substring(0, keepStart) + 
               "****" + 
               data.substring(data.length() - keepEnd);
    }
    
    /**
     * 根据数据类型自动脱敏
     */
    public static String autoMask(String data, String dataType) {
        if (data == null || data.isEmpty()) {
            return data;
        }
        
        switch (dataType.toLowerCase()) {
            case "phone":
            case "mobile":
                return maskPhone(data);
            case "idcard":
            case "id_card":
                return maskIdCard(data);
            case "name":
                return maskName(data);
            case "email":
                return maskEmail(data);
            case "bankcard":
            case "bank_card":
                return maskBankCard(data);
            case "address":
                return maskAddress(data);
            default:
                // 默认脱敏策略：保留前3位和后1位
                return maskGeneric(data, 3, 1);
        }
    }
    
    /**
     * 检查字符串是否已经被脱敏
     */
    public static boolean isMasked(String data) {
        return data != null && data.contains(MASK_CHAR);
    }
    
    /**
     * 获取脱敏字符
     */
    public static String getMaskChar() {
        return MASK_CHAR;
    }
}
