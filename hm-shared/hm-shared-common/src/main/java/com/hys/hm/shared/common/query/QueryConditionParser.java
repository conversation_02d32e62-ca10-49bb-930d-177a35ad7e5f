package com.hys.hm.shared.common.query;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 查询条件解析器
 * 用于解析HTTP请求参数中的动态查询条件
 *
 * 支持的查询格式：
 * - fieldName=value : 等于查询
 * - fieldName_EQ=value : 等于查询
 * - fieldName_LIKE=value : 模糊查询（自动添加 %value% 通配符）
 * - fieldName_GT=value : 大于查询
 * - fieldName_LT=value : 小于查询
 * - fieldName_GTE=value : 大于等于查询
 * - fieldName_LTE=value : 小于等于查询
 * - fieldName_IN=value1,value2,value3 : IN查询
 * - fieldName_NOT_NULL=true : 非空查询
 * - fieldName_IS_NULL=true : 空值查询
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Slf4j
@Component
public class QueryConditionParser {

    /**
     * 操作符分隔符
     */
    private static final String OPERATOR_SEPARATOR = "_";

    /**
     * IN操作符的值分隔符
     */
    private static final String IN_VALUE_SEPARATOR = ",";

    /**
     * 日期时间格式化器
     */
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 需要忽略的参数名（通常是分页和排序参数）
     */
    private static final Set<String> IGNORED_PARAMS = Set.of(
        "page", "size", "sort", "orders", "order", "limit", "offset"
    );

    /**
     * 解析请求参数为查询条件列表
     *
     * @param parameterMap 请求参数Map
     * @return 查询条件列表
     */
    public List<QueryCondition> parseConditions(Map<String, String[]> parameterMap) {
        List<QueryCondition> conditions = new ArrayList<>();

        if (parameterMap == null || parameterMap.isEmpty()) {
            return conditions;
        }

        for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
            String paramName = entry.getKey();
            String[] paramValues = entry.getValue();

            // 跳过忽略的参数
            if (IGNORED_PARAMS.contains(paramName.toLowerCase())) {
                continue;
            }

            // 跳过空值参数
            if (paramValues == null || paramValues.length == 0 ||
                (paramValues.length == 1 && !StringUtils.hasText(paramValues[0]))) {
                continue;
            }

            try {
                QueryCondition condition = parseCondition(paramName, paramValues);
                if (condition != null && condition.isValid()) {
                    conditions.add(condition);
                }
            } catch (Exception e) {
                log.warn("解析查询条件失败: 参数名={}, 参数值={}, 错误={}",
                        paramName, Arrays.toString(paramValues), e.getMessage());
            }
        }

        return conditions;
    }

    /**
     * 解析单个查询条件
     *
     * @param paramName 参数名
     * @param paramValues 参数值数组
     * @return 查询条件
     */
    private QueryCondition parseCondition(String paramName, String[] paramValues) {
        // 解析字段名和操作符
        String fieldName;
        QueryOperator operator;

        if (paramName.contains(OPERATOR_SEPARATOR)) {
            String[] parts = paramName.split(OPERATOR_SEPARATOR, 2);
            fieldName = parts[0];
            operator = QueryOperator.fromString(parts[1]);
        } else {
            fieldName = paramName;
            operator = QueryOperator.EQ; // 默认为等于操作
        }

        // 验证字段名
        if (!StringUtils.hasText(fieldName)) {
            log.warn("字段名为空: {}", paramName);
            return null;
        }

        // 根据操作符处理参数值
        return createCondition(fieldName, operator, paramValues);
    }

    /**
     * 根据操作符创建查询条件
     */
    private QueryCondition createCondition(String fieldName, QueryOperator operator, String[] paramValues) {
        switch (operator) {
            case IS_NULL:
                return QueryCondition.isNull(fieldName);

            case IS_NOT_NULL:
                return QueryCondition.isNotNull(fieldName);

            case IN:
            case NOT_IN:
                List<Object> values = parseInValues(paramValues);
                return operator == QueryOperator.IN ?
                    QueryCondition.in(fieldName, values) :
                    QueryCondition.notIn(fieldName, values);

            case BETWEEN:
                if (paramValues.length >= 2) {
                    Object startValue = convertValue(paramValues[0]);
                    Object endValue = convertValue(paramValues[1]);
                    return QueryCondition.between(fieldName, startValue, endValue);
                }
                log.warn("BETWEEN操作需要两个值: 字段={}, 值={}", fieldName, Arrays.toString(paramValues));
                return null;

            default:
                // 单值操作符
                if (paramValues.length > 0) {
                    Object value = convertValue(paramValues[0]);
                    return createSingleValueCondition(fieldName, operator, value);
                }
                return null;
        }
    }

    /**
     * 创建单值查询条件
     */
    private QueryCondition createSingleValueCondition(String fieldName, QueryOperator operator, Object value) {
        switch (operator) {
            case EQ:
                return QueryCondition.eq(fieldName, value);
            case NE:
                return QueryCondition.ne(fieldName, value);
            case LIKE:
                // 自动为 LIKE 查询添加通配符
                String likeValue = value.toString();
                if (!likeValue.contains("%")) {
                    likeValue = "%" + likeValue + "%";
                }
                return QueryCondition.like(fieldName, likeValue);
            case GT:
                return QueryCondition.gt(fieldName, value);
            case GTE:
                return QueryCondition.gte(fieldName, value);
            case LT:
                return QueryCondition.lt(fieldName, value);
            case LTE:
                return QueryCondition.lte(fieldName, value);
            default:
                log.warn("不支持的单值操作符: {}", operator);
                return null;
        }
    }

    /**
     * 解析IN操作的多个值
     */
    private List<Object> parseInValues(String[] paramValues) {
        List<Object> values = new ArrayList<>();

        for (String paramValue : paramValues) {
            if (StringUtils.hasText(paramValue)) {
                // 支持逗号分隔的多个值
                String[] splitValues = paramValue.split(IN_VALUE_SEPARATOR);
                for (String splitValue : splitValues) {
                    String trimmedValue = splitValue.trim();
                    if (StringUtils.hasText(trimmedValue)) {
                        values.add(convertValue(trimmedValue));
                    }
                }
            }
        }

        return values;
    }

    /**
     * 转换参数值为合适的类型
     * 支持字符串、数字、布尔值、日期等类型的自动转换
     */
    private Object convertValue(String value) {
        if (!StringUtils.hasText(value)) {
            return value;
        }

        String trimmedValue = value.trim();

        // 布尔值转换
        if ("true".equalsIgnoreCase(trimmedValue) || "false".equalsIgnoreCase(trimmedValue)) {
            return Boolean.parseBoolean(trimmedValue);
        }

        // 数字转换
        try {
            // 尝试转换为Long
            if (trimmedValue.matches("-?\\d+")) {
                return Long.parseLong(trimmedValue);
            }

            // 尝试转换为Double
            if (trimmedValue.matches("-?\\d*\\.\\d+")) {
                return Double.parseDouble(trimmedValue);
            }
        } catch (NumberFormatException e) {
            // 忽略数字转换异常，继续尝试其他类型
        }

        // 日期时间转换
        try {
            // 尝试解析为LocalDateTime
            if (trimmedValue.contains(" ") && trimmedValue.length() >= 19) {
                return LocalDateTime.parse(trimmedValue, DATE_TIME_FORMATTER);
            }

            // 尝试解析为LocalDate
            if (trimmedValue.matches("\\d{4}-\\d{2}-\\d{2}")) {
                return LocalDate.parse(trimmedValue, DATE_FORMATTER);
            }
        } catch (DateTimeParseException e) {
            // 忽略日期转换异常，返回原始字符串
        }

        // 默认返回字符串
        return trimmedValue;
    }

    /**
     * 从单个参数字符串解析查询条件
     * 格式: "fieldName_OPERATOR=value"
     */
    public QueryCondition parseCondition(String conditionStr) {
        if (!StringUtils.hasText(conditionStr)) {
            return null;
        }

        String[] parts = conditionStr.split("=", 2);
        if (parts.length != 2) {
            log.warn("查询条件格式错误: {}", conditionStr);
            return null;
        }

        String paramName = parts[0].trim();
        String paramValue = parts[1].trim();

        return parseCondition(paramName, new String[]{paramValue});
    }

    /**
     * 批量解析查询条件字符串
     */
    public List<QueryCondition> parseConditions(String... conditionStrs) {
        return Arrays.stream(conditionStrs)
                .map(this::parseCondition)
                .filter(Objects::nonNull)
                .filter(QueryCondition::isValid)
                .collect(Collectors.toList());
    }
}
