package com.hys.hm.shared.common.query;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Sort;

import java.util.ArrayList;
import java.util.List;

/**
 * 排序条件封装类
 * 用于封装排序字段和排序方向
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SortOrder {

    /**
     * 排序字段名
     */
    private String fieldName;

    /**
     * 排序方向
     */
    private Sort.Direction direction;

    /**
     * 创建升序排序
     */
    public static SortOrder asc(String fieldName) {
        return new SortOrder(fieldName, Sort.Direction.ASC);
    }

    /**
     * 创建降序排序
     */
    public static SortOrder desc(String fieldName) {
        return new SortOrder(fieldName, Sort.Direction.DESC);
    }

    /**
     * 解析排序字符串
     * 支持以下格式：
     * - "+fieldName" 或 "fieldName" : 升序
     * - "-fieldName" : 降序
     * - "+field1,-field2,field3" : 多字段排序
     *
     * @param orderStr 排序字符串
     * @return 排序条件列表
     */
    public static List<SortOrder> parseOrderString(String orderStr) {
        List<SortOrder> sortOrders = new ArrayList<>();

        if (orderStr == null || orderStr.trim().isEmpty()) {
            return sortOrders;
        }

        // 按逗号分割多个排序字段
        String[] orderFields = orderStr.split(",");

        for (String field : orderFields) {
            field = field.trim();
            if (field.isEmpty()) {
                continue;
            }

            Sort.Direction direction = Sort.Direction.ASC; // 默认升序
            String fieldName = field;

            // 检查排序方向前缀
            if (field.startsWith("-")) {
                direction = Sort.Direction.DESC;
                fieldName = field.substring(1);
            } else if (field.startsWith("+")) {
                direction = Sort.Direction.ASC;
                fieldName = field.substring(1);
            }

            // 验证字段名不为空
            if (!fieldName.trim().isEmpty()) {
                sortOrders.add(new SortOrder(fieldName.trim(), direction));
            }
        }

        return sortOrders;
    }

    /**
     * 将排序条件列表转换为Spring Data的Sort对象
     */
    public static Sort toSort(List<SortOrder> sortOrders) {
        if (sortOrders == null || sortOrders.isEmpty()) {
            return Sort.unsorted();
        }

        List<Sort.Order> orders = new ArrayList<>();
        for (SortOrder sortOrder : sortOrders) {
            if (sortOrder.getFieldName() != null && !sortOrder.getFieldName().trim().isEmpty()) {
                orders.add(new Sort.Order(sortOrder.getDirection(), sortOrder.getFieldName()));
            }
        }

        return orders.isEmpty() ? Sort.unsorted() : Sort.by(orders);
    }

    /**
     * 直接从排序字符串创建Sort对象
     */
    public static Sort fromString(String orderStr) {
        return toSort(parseOrderString(orderStr));
    }

    /**
     * 验证排序条件是否有效
     */
    public boolean isValid() {
        return fieldName != null && !fieldName.trim().isEmpty() && direction != null;
    }

    /**
     * 获取排序方向的字符串表示
     */
    public String getDirectionString() {
        return direction == Sort.Direction.ASC ? "ASC" : "DESC";
    }

    /**
     * 获取带方向前缀的字段名
     */
    public String getFieldWithDirection() {
        String prefix = direction == Sort.Direction.ASC ? "+" : "-";
        return prefix + fieldName;
    }

    /**
     * 转换为Spring Data的Sort.Order对象
     */
    public Sort.Order toOrder() {
        return new Sort.Order(direction, fieldName);
    }

    @Override
    public String toString() {
        return getFieldWithDirection();
    }

    /**
     * 创建默认排序（按创建时间降序）
     */
    public static List<SortOrder> defaultSort() {
        return List.of(desc("createTime"));
    }

    /**
     * 创建ID升序排序
     */
    public static List<SortOrder> idAsc() {
        return List.of(asc("id"));
    }

    /**
     * 创建ID降序排序
     */
    public static List<SortOrder> idDesc() {
        return List.of(desc("id"));
    }

    /**
     * 合并多个排序条件
     */
    public static List<SortOrder> combine(List<SortOrder>... sortOrderLists) {
        List<SortOrder> combined = new ArrayList<>();
        for (List<SortOrder> sortOrders : sortOrderLists) {
            if (sortOrders != null) {
                combined.addAll(sortOrders);
            }
        }
        return combined;
    }
}
