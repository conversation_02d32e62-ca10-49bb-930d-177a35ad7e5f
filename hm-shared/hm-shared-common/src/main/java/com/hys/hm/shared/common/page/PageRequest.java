package com.hys.hm.shared.common.page;

import com.hys.hm.shared.common.query.QueryCondition;
import com.hys.hm.shared.common.query.SortOrder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 分页请求封装类
 * 封装分页、排序和查询条件
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageRequest {

    /**
     * 页码（从1开始）
     */
    private int page = 1;

    /**
     * 每页大小
     */
    private int size = 20;

    /**
     * 排序条件列表
     */
    private List<SortOrder> sortOrders = new ArrayList<>();

    /**
     * 查询条件列表
     */
    private List<QueryCondition> queryConditions = new ArrayList<>();

    /**
     * 排除字段列表（不查询这些字段的内容，用于性能优化）
     */
    private List<String> excludes = new ArrayList<>();

    /**
     * 是否需要查询总数（用于性能优化）
     */
    private boolean needTotal = true;

    /**
     * 最大页面大小限制
     */
    private static final int MAX_PAGE_SIZE = 1000;

    /**
     * 默认页面大小
     */
    private static final int DEFAULT_PAGE_SIZE = 20;

    /**
     * 创建基本分页请求
     */
    public static PageRequest of(int page, int size) {
        PageRequest request = new PageRequest();
        request.page = page;
        request.size = size;
        request.sortOrders = new ArrayList<>();
        request.queryConditions = new ArrayList<>();
        request.excludes = new ArrayList<>();
        request.needTotal = true;
        return request;
    }

    /**
     * 创建带排序的分页请求
     */
    public static PageRequest of(int page, int size, List<SortOrder> sortOrders) {
        PageRequest request = new PageRequest();
        request.page = page;
        request.size = size;
        request.sortOrders = sortOrders != null ? sortOrders : new ArrayList<>();
        request.queryConditions = new ArrayList<>();
        request.excludes = new ArrayList<>();
        request.needTotal = true;
        return request;
    }

    /**
     * 创建带排序和查询条件的分页请求
     */
    public static PageRequest of(int page, int size, List<SortOrder> sortOrders, List<QueryCondition> queryConditions) {
        PageRequest request = new PageRequest();
        request.page = page;
        request.size = size;
        request.sortOrders = sortOrders != null ? sortOrders : new ArrayList<>();
        request.queryConditions = queryConditions != null ? queryConditions : new ArrayList<>();
        request.excludes = new ArrayList<>();
        request.needTotal = true;
        return request;
    }

    /**
     * 创建带排序、查询条件和排除字段的分页请求
     */
    public static PageRequest of(int page, int size, List<SortOrder> sortOrders, List<QueryCondition> queryConditions, List<String> excludes) {
        PageRequest request = new PageRequest();
        request.page = page;
        request.size = size;
        request.sortOrders = sortOrders != null ? sortOrders : new ArrayList<>();
        request.queryConditions = queryConditions != null ? queryConditions : new ArrayList<>();
        request.excludes = excludes != null ? excludes : new ArrayList<>();
        request.needTotal = true;
        return request;
    }

    /**
     * 创建不需要总数的分页请求（性能优化）
     */
    public static PageRequest ofWithoutTotal(int page, int size) {
        PageRequest request = new PageRequest();
        request.page = page;
        request.size = size;
        request.sortOrders = new ArrayList<>();
        request.queryConditions = new ArrayList<>();
        request.excludes = new ArrayList<>();
        request.needTotal = false;
        return request;
    }

    /**
     * 添加排序条件
     */
    public PageRequest addSort(SortOrder sortOrder) {
        if (sortOrder != null && sortOrder.isValid()) {
            this.sortOrders.add(sortOrder);
        }
        return this;
    }

    /**
     * 添加排序条件（升序）
     */
    public PageRequest addSortAsc(String fieldName) {
        return addSort(SortOrder.asc(fieldName));
    }

    /**
     * 添加排序条件（降序）
     */
    public PageRequest addSortDesc(String fieldName) {
        return addSort(SortOrder.desc(fieldName));
    }

    /**
     * 添加查询条件
     */
    public PageRequest addCondition(QueryCondition condition) {
        if (condition != null && condition.isValid()) {
            this.queryConditions.add(condition);
        }
        return this;
    }

    /**
     * 添加等于查询条件
     */
    public PageRequest addEq(String fieldName, Object value) {
        return addCondition(QueryCondition.eq(fieldName, value));
    }

    /**
     * 添加模糊查询条件
     */
    public PageRequest addLike(String fieldName, Object value) {
        return addCondition(QueryCondition.like(fieldName, value));
    }

    /**
     * 添加排除字段
     */
    public PageRequest addExclude(String fieldName) {
        if (StringUtils.hasText(fieldName) && !this.excludes.contains(fieldName)) {
            this.excludes.add(fieldName);
        }
        return this;
    }

    /**
     * 添加多个排除字段
     */
    public PageRequest addExcludes(String... fieldNames) {
        if (fieldNames != null) {
            for (String fieldName : fieldNames) {
                addExclude(fieldName);
            }
        }
        return this;
    }

    /**
     * 添加多个排除字段
     */
    public PageRequest addExcludes(List<String> fieldNames) {
        if (fieldNames != null) {
            for (String fieldName : fieldNames) {
                addExclude(fieldName);
            }
        }
        return this;
    }

    /**
     * 转换为Spring Data的Pageable对象
     */
    public Pageable toPageable() {
        // 验证和调整页码和大小
        int validPage = Math.max(1, this.page) - 1; // Spring Data页码从0开始
        int validSize = Math.min(Math.max(1, this.size), MAX_PAGE_SIZE);

        // 创建排序对象
        Sort sort = SortOrder.toSort(this.sortOrders);

        return org.springframework.data.domain.PageRequest.of(validPage, validSize, sort);
    }

    /**
     * 获取偏移量
     */
    public long getOffset() {
        return (long) (Math.max(1, this.page) - 1) * Math.min(Math.max(1, this.size), MAX_PAGE_SIZE);
    }

    /**
     * 获取有效的页面大小
     */
    public int getValidSize() {
        return Math.min(Math.max(1, this.size), MAX_PAGE_SIZE);
    }

    /**
     * 获取有效的页码（从1开始）
     */
    public int getValidPage() {
        return Math.max(1, this.page);
    }

    /**
     * 设置页码（从1开始）
     */
    public PageRequest setPage(int page) {
        this.page = Math.max(1, page);
        return this;
    }

    /**
     * 设置页面大小
     */
    public PageRequest setSize(int size) {
        this.size = Math.min(Math.max(1, size), MAX_PAGE_SIZE);
        return this;
    }

    /**
     * 设置排序字符串
     * 格式: "+field1,-field2,field3"
     */
    public PageRequest setOrderString(String orderString) {
        this.sortOrders = SortOrder.parseOrderString(orderString);
        return this;
    }

    /**
     * 验证分页请求是否有效
     */
    public boolean isValid() {
        return page > 0 && size > 0 && size <= MAX_PAGE_SIZE;
    }

    /**
     * 获取下一页的分页请求
     */
    public PageRequest nextPage() {
        PageRequest nextPageRequest = new PageRequest();
        nextPageRequest.page = this.page + 1;
        nextPageRequest.size = this.size;
        nextPageRequest.sortOrders = new ArrayList<>(this.sortOrders);
        nextPageRequest.queryConditions = new ArrayList<>(this.queryConditions);
        nextPageRequest.excludes = new ArrayList<>(this.excludes);
        nextPageRequest.needTotal = this.needTotal;
        return nextPageRequest;
    }

    /**
     * 获取上一页的分页请求
     */
    public PageRequest previousPage() {
        PageRequest prevPageRequest = new PageRequest();
        prevPageRequest.page = Math.max(1, this.page - 1);
        prevPageRequest.size = this.size;
        prevPageRequest.sortOrders = new ArrayList<>(this.sortOrders);
        prevPageRequest.queryConditions = new ArrayList<>(this.queryConditions);
        prevPageRequest.excludes = new ArrayList<>(this.excludes);
        prevPageRequest.needTotal = this.needTotal;
        return prevPageRequest;
    }

    /**
     * 重置为第一页
     */
    public PageRequest firstPage() {
        this.page = 1;
        return this;
    }

    /**
     * 清空查询条件
     */
    public PageRequest clearConditions() {
        this.queryConditions.clear();
        return this;
    }

    /**
     * 清空排序条件
     */
    public PageRequest clearSort() {
        this.sortOrders.clear();
        return this;
    }

    /**
     * 清空排除字段
     */
    public PageRequest clearExcludes() {
        this.excludes.clear();
        return this;
    }

    /**
     * 创建副本
     */
    public PageRequest copy() {
        PageRequest copy = new PageRequest();
        copy.page = this.page;
        copy.size = this.size;
        copy.sortOrders = new ArrayList<>(this.sortOrders);
        copy.queryConditions = new ArrayList<>(this.queryConditions);
        copy.excludes = new ArrayList<>(this.excludes);
        copy.needTotal = this.needTotal;
        return copy;
    }

    @Override
    public String toString() {
        return String.format("PageRequest{page=%d, size=%d, sorts=%d, conditions=%d, excludes=%d, needTotal=%s}",
                page, size, sortOrders.size(), queryConditions.size(), excludes.size(), needTotal);
    }
}
