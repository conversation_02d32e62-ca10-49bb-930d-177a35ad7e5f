package com.hys.hm.shared.common.query;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 查询条件封装类
 * 用于封装动态查询条件
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryCondition {

    /**
     * 字段名
     */
    private String fieldName;

    /**
     * 操作符
     */
    private QueryOperator operator;

    /**
     * 查询值
     */
    private Object value;

    /**
     * 查询值列表（用于IN操作）
     */
    private List<Object> values;

    /**
     * 是否忽略大小写（用于字符串比较）
     */
    private boolean ignoreCase = false;

    /**
     * 创建等于条件
     */
    public static QueryCondition eq(String fieldName, Object value) {
        return new QueryCondition(fieldName, QueryOperator.EQ, value, null, false);
    }

    /**
     * 创建不等于条件
     */
    public static QueryCondition ne(String fieldName, Object value) {
        return new QueryCondition(fieldName, QueryOperator.NE, value, null, false);
    }

    /**
     * 创建模糊查询条件
     */
    public static QueryCondition like(String fieldName, Object value) {
        return new QueryCondition(fieldName, QueryOperator.LIKE, value, null, false);
    }

    /**
     * 创建模糊查询条件（忽略大小写）
     */
    public static QueryCondition likeIgnoreCase(String fieldName, Object value) {
        return new QueryCondition(fieldName, QueryOperator.LIKE, value, null, true);
    }

    /**
     * 创建大于条件
     */
    public static QueryCondition gt(String fieldName, Object value) {
        return new QueryCondition(fieldName, QueryOperator.GT, value, null, false);
    }

    /**
     * 创建大于等于条件
     */
    public static QueryCondition gte(String fieldName, Object value) {
        return new QueryCondition(fieldName, QueryOperator.GTE, value, null, false);
    }

    /**
     * 创建小于条件
     */
    public static QueryCondition lt(String fieldName, Object value) {
        return new QueryCondition(fieldName, QueryOperator.LT, value, null, false);
    }

    /**
     * 创建小于等于条件
     */
    public static QueryCondition lte(String fieldName, Object value) {
        return new QueryCondition(fieldName, QueryOperator.LTE, value, null, false);
    }

    /**
     * 创建IN条件
     */
    public static QueryCondition in(String fieldName, List<Object> values) {
        return new QueryCondition(fieldName, QueryOperator.IN, null, values, false);
    }

    /**
     * 创建NOT IN条件
     */
    public static QueryCondition notIn(String fieldName, List<Object> values) {
        return new QueryCondition(fieldName, QueryOperator.NOT_IN, null, values, false);
    }

    /**
     * 创建IS NULL条件
     */
    public static QueryCondition isNull(String fieldName) {
        return new QueryCondition(fieldName, QueryOperator.IS_NULL, null, null, false);
    }

    /**
     * 创建IS NOT NULL条件
     */
    public static QueryCondition isNotNull(String fieldName) {
        return new QueryCondition(fieldName, QueryOperator.IS_NOT_NULL, null, null, false);
    }

    /**
     * 创建BETWEEN条件
     */
    public static QueryCondition between(String fieldName, Object startValue, Object endValue) {
        return new QueryCondition(fieldName, QueryOperator.BETWEEN, startValue, List.of(endValue), false);
    }

    /**
     * 验证查询条件是否有效
     */
    public boolean isValid() {
        if (fieldName == null || fieldName.trim().isEmpty()) {
            return false;
        }

        if (operator == null) {
            return false;
        }

        // 检查不同操作符的值要求
        switch (operator) {
            case IS_NULL:
            case IS_NOT_NULL:
                return true; // 这些操作符不需要值
            case IN:
            case NOT_IN:
                return values != null && !values.isEmpty();
            case BETWEEN:
                return value != null && values != null && !values.isEmpty();
            default:
                return value != null;
        }
    }

    /**
     * 获取实际的查询值
     * 对于LIKE操作，自动添加通配符
     */
    public Object getActualValue() {
        if (operator == QueryOperator.LIKE && value instanceof String) {
            String strValue = (String) value;
            if (!strValue.contains("%")) {
                return "%" + strValue + "%";
            }
        }
        return value;
    }
}
