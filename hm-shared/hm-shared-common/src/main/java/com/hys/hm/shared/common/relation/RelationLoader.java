package com.hys.hm.shared.common.relation;

import java.lang.annotation.*;

/**
 * 关联数据加载器注解
 * 标记某个方法为关联数据加载器
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RelationLoader {

    /**
     * 支持的实体类型
     */
    String[] entityTypes();

    /**
     * 加载器优先级
     */
    int priority() default 0;

    /**
     * 是否支持批量加载
     */
    boolean supportBatch() default false;

    /**
     * 缓存时间（秒）
     */
    int cacheSeconds() default 300;
}
