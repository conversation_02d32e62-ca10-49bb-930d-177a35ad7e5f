package com.hys.hm.shared.annotations.relation;

import java.lang.annotation.*;

/**
 * 关联数据提供者注解
 * 标记某个服务类为关联数据提供者
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RelationProvider {

    /**
     * 提供的实体类型
     */
    String[] entityTypes();

    /**
     * 提供者名称
     */
    String name() default "";

    /**
     * 提供者描述
     */
    String description() default "";
}
