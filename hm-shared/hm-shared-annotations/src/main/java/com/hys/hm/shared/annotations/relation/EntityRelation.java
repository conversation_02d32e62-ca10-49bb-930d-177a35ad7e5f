package com.hys.hm.shared.annotations.relation;

import java.lang.annotation.*;

/**
 * 实体关联注解
 * 用于标记实体间的关联关系，避免直接依赖
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Target({ElementType.FIELD, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface EntityRelation {

    /**
     * 关联的实体类型
     */
    String entityType();

    /**
     * 关联字段名
     */
    String relationField() default "id";

    /**
     * 关联类型
     */
    RelationType type() default RelationType.ONE_TO_ONE;

    /**
     * 是否延迟加载
     */
    boolean lazy() default true;

    /**
     * 缓存键前缀
     */
    String cachePrefix() default "";

    /**
     * 关联描述
     */
    String description() default "";

    /**
     * 关联类型枚举
     */
    enum RelationType {
        ONE_TO_ONE,
        ONE_TO_MANY,
        MANY_TO_ONE,
        MANY_TO_MANY
    }
}
