package com.hys.hm.shared.types.dto.model;




import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 用户信息接口
 *
 * <AUTHOR>
 */
public interface IUser extends Serializable {

    /**
     * getId:用于返回用户的唯一标识.
     *
     * @return 用户ID
     * <AUTHOR>
     * @since JDK 1.8
     */
    String getId();

    /**
     * 返回用户名(用于显示的名称，而不是登录帐号)
     *
     * @return
     */
    String getUserName();

    /**
     * 返回角色信息列表
     *
     * @return
     */
    //List<? extends Role> getRoles();

    /**
     * 返回手机号
     *
     * @return
     */
    String getTel();

    /**
     * 返回邮箱
     *
     * @return
     */
    String getMail();

    /**
     * 返回act
     *
     * @return
     */
    String getAct();
    /**
     * 返回departmentId(科室主键)
     *
     * @return
     */
    String getDepartmentId();
    /**
     * 返回登录首页地址
     *
     * @return
     */
    String getHomePath();
    /**
     * 返回登录首页地址
     *
     * @return
     */
    String getDrugtype();
    /**
     * 返回所有路径认证类型的认证类容 返回格式通过&对url进行拼接 示例：url1&url2&url3
     *
     * @return
     */
    String authenticationURL();

    /**
     * 返回用户具有的所有权限code 返回格式通过&对code进行拼接 示例：01001&01002&02005
     *
     * @return
     */
    String JurisdictionCode();

    /**
     * getLanguageType:获得语言类型.
     *
     * @return
     * <AUTHOR>
     * @since JDK 1.8
     */
    //LanguageType getLanguageType();

    /**
     * getDepartment:获取用户所属部门信息.
     *
     * @return 部门信息
     * <AUTHOR>
     * @since JDK 1.8
     */
    //Department getDepartment();

    /**
     * getDepartmentInfo:获取科室信息
     * @return
     */
    //DepartmentInfo getDepartmentInfo();
    /**
     * getUserType:返回用户类型.
     *
     * @return
     * <AUTHOR>
     * @since JDK 1.8
     */
    Integer getUserType();

    /**
     * getData:用户其他绑定数据(登录的时候设置内容)
     *
     * @return
     * <AUTHOR>
     * @since JDK 1.8
     */
    Map<String, Object> getData();

    String getLoginName();

    String getExclude();

    String getProperties();

    void setProperties(String properties);
}
