package com.hys.hm.shared.logging.annotation;

import com.hys.hm.shared.logging.enums.BusinessType;
import com.hys.hm.shared.logging.enums.OperatorType;
import com.hys.hm.shared.logging.enums.PlatformType;

import java.lang.annotation.*;

/**
 * 自定义操作日志记录注解
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-22
 */
@Target({ElementType.PARAMETER, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Log {

    /**
     * 模块标题
     */
    String title() default "";

    /**
     * 业务操作类型
     */
    BusinessType businessType() default BusinessType.OTHER;

    /**
     * 操作人类别
     */
    OperatorType operatorType() default OperatorType.MANAGE;

    /**
     * 平台类型
     */
    PlatformType platformType() default PlatformType.HM;

    /**
     * 是否保存请求的参数
     */
    boolean isSaveRequestData() default true;

    /**
     * 是否保存响应的参数
     */
    boolean isSaveResponseData() default true;

    /**
     * 是否为项目记录日志
     */
    boolean projectRecordLog() default false;

    /**
     * 是否异步保存日志
     */
    boolean async() default true;

    /**
     * 日志级别
     */
    LogLevel level() default LogLevel.INFO;

    /**
     * 日志级别枚举
     */
    enum LogLevel {
        DEBUG, INFO, WARN, ERROR
    }
}
