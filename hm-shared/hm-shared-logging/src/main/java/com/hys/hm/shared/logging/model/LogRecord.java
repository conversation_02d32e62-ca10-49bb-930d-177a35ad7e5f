package com.hys.hm.shared.logging.model;

import com.hys.hm.shared.logging.enums.BusinessStatus;
import com.hys.hm.shared.logging.enums.BusinessType;
import com.hys.hm.shared.logging.enums.OperatorType;
import com.hys.hm.shared.logging.enums.PlatformType;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 日志记录数据模型
 * 用于在各层之间传递日志数据
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LogRecord {

    /**
     * 日志ID
     */
    private Long id;

    /**
     * 模块标题
     */
    private String title;

    /**
     * 请求方式
     */
    private String requestMethod;

    /**
     * 方法名称
     */
    private String methodName;

    /**
     * 请求URL
     */
    private String requestUrl;

    /**
     * 是否项目日志
     */
    private Boolean projectRecordLog;

    /**
     * 主机地址
     */
    private String requestIp;

    /**
     * 操作类别
     */
    private OperatorType operatorType;

    /**
     * 业务类型
     */
    private BusinessType businessType;

    /**
     * 平台类型
     */
    private PlatformType platformType;

    /**
     * 操作人员登录名
     */
    private String userName;

    /**
     * 用户真实姓名
     */
    private String realName;

    /**
     * 操作地点
     */
    private String location;

    /**
     * 操作状态
     */
    private BusinessStatus status;

    /**
     * 请求参数
     */
    private String requestParam;

    /**
     * 返回参数
     */
    private String responseResult;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 操作时间
     */
    private LocalDateTime operateTime;

    /**
     * 执行耗时（毫秒）
     */
    private Long executionTime;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 请求来源
     */
    private String referer;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 追踪ID
     */
    private String traceId;
}
