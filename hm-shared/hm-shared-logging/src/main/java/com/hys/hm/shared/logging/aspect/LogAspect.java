package com.hys.hm.shared.logging.aspect;

import com.hys.hm.shared.common.Result;
import com.hys.hm.shared.common.util.biz.JacksonUtils;
import com.hys.hm.shared.common.util.system.IPUtils;
import com.hys.hm.shared.logging.annotation.Log;
import com.hys.hm.shared.logging.enums.BusinessStatus;
import com.hys.hm.shared.logging.model.LogRecord;
import com.hys.hm.shared.logging.service.LogService;
import com.hys.hm.shared.types.dto.model.IUser;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.HandlerMapping;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * 统一日志记录切面
 * 整合原有的 SystemLogRecordAspect 和 LoggingAspect 功能
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-22
 */
@Aspect
@Component
@Order(800)
@Slf4j
@RequiredArgsConstructor
public class LogAspect {

    private final LogService logService;

    /**
     * 环绕通知 - 处理带有 @Log 注解的方法
     */
    @Around("@annotation(logAnnotation)")
    public Object aroundLog(ProceedingJoinPoint joinPoint, Log logAnnotation) throws Throwable {
        long startTime = System.currentTimeMillis();
        String traceId = UUID.randomUUID().toString().replace("-", "");

        // 获取请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes != null ? attributes.getRequest() : null;

        // 构建日志记录
        LogRecord logRecord = new LogRecord();
        logRecord.setTraceId(traceId);
        logRecord.setOperateTime(LocalDateTime.now());

        // 设置基本信息
        buildBasicInfo(joinPoint, logAnnotation, logRecord, request);

        Object result = null;
        Exception exception = null;

        try {
            // 执行目标方法
            result = joinPoint.proceed();

            // 设置成功状态和响应结果
            logRecord.setStatus(BusinessStatus.SUCCESS);
            if (logAnnotation.isSaveResponseData() && result != null) {
                handleResponseResult(result, logRecord);
            }

        } catch (Exception e) {
            exception = e;
            // 设置失败状态和错误信息
            logRecord.setStatus(BusinessStatus.FAIL);
            logRecord.setErrorMessage(StringUtils.substring(e.getMessage(), 0, 2000));
            throw e;
        } finally {
            // 计算执行时间
            long executionTime = System.currentTimeMillis() - startTime;
            logRecord.setExecutionTime(executionTime);

            // 保存日志
            saveLogRecord(logRecord, logAnnotation.async());

            // 控制台日志输出
            logToConsole(joinPoint, request, result, exception, executionTime, traceId);
        }

        return result;
    }

    /**
     * 构建基本信息
     */
    private void buildBasicInfo(JoinPoint joinPoint, Log logAnnotation,
                               LogRecord logRecord, HttpServletRequest request) {
        // 获取用户信息
        IUser currentUser = getCurrentUser();
        if (currentUser != null) {
            logRecord.setUserName(currentUser.getLoginName());
            logRecord.setRealName(currentUser.getUserName());
        } else {
            logRecord.setUserName("un_login");
            logRecord.setRealName("未登录用户");
        }

        // 获取方法信息
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String className = joinPoint.getTarget().getClass().getName();
        String methodName = signature.getName();

        logRecord.setTitle(logAnnotation.title());
        logRecord.setMethodName(className + "." + methodName + "()");
        logRecord.setBusinessType(logAnnotation.businessType());
        logRecord.setOperatorType(logAnnotation.operatorType());
        logRecord.setPlatformType(logAnnotation.platformType());
        logRecord.setProjectRecordLog(logAnnotation.projectRecordLog());

        // 获取请求信息
        if (request != null) {
            logRecord.setRequestMethod(request.getMethod().toLowerCase());
            logRecord.setRequestUrl(request.getRequestURI());
            logRecord.setRequestIp(IPUtils.getIpAddress(request));
            logRecord.setUserAgent(request.getHeader("User-Agent"));
            logRecord.setReferer(request.getHeader("Referer"));
            logRecord.setSessionId(request.getSession().getId());

            // 设置请求参数
            if (logAnnotation.isSaveRequestData()) {
                setRequestParams(joinPoint, logRecord, request);
            }
        }
    }

    /**
     * 设置请求参数
     */
    private void setRequestParams(JoinPoint joinPoint, LogRecord logRecord, HttpServletRequest request) {
        String requestMethod = request.getMethod().toUpperCase();
        if (HttpMethod.PUT.name().equals(requestMethod) ||
            HttpMethod.POST.name().equals(requestMethod) ||
            HttpMethod.DELETE.name().equals(requestMethod)) {
            String params = argsArrayToString(joinPoint.getArgs());
            logRecord.setRequestParam(params);
        } else {
            @SuppressWarnings("unchecked")
            Map<String, Object> paramsMap = (Map<String, Object>) request.getAttribute(
                    HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE);
            if (paramsMap != null && !paramsMap.isEmpty()) {
                logRecord.setRequestParam(JacksonUtils.toJson(paramsMap));
            }
        }
    }

    /**
     * 处理响应结果
     */
    private void handleResponseResult(Object result, LogRecord logRecord) {
        try {
            String resultJson = JacksonUtils.toJson(result);
            logRecord.setResponseResult(resultJson);

            // 检查是否为标准响应格式
            Result commonResult = JacksonUtils.fromJson(resultJson, Result.class);
            if (commonResult != null && commonResult.getCode() != 200) {
                logRecord.setStatus(BusinessStatus.FAIL);
                logRecord.setErrorMessage(commonResult.getMessage());
            }
        } catch (Exception e) {
            log.warn("处理响应结果时发生异常: {}", e.getMessage());
        }
    }

    /**
     * 保存日志记录
     */
    private void saveLogRecord(LogRecord logRecord, boolean async) {
        try {
            if (async) {
                logService.saveLogAsync(logRecord);
            } else {
                logService.saveLog(logRecord);
            }
        } catch (Exception e) {
            log.error("保存日志记录失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 控制台日志输出
     */
    private void logToConsole(JoinPoint joinPoint, HttpServletRequest request, Object result,
                             Exception exception, long executionTime, String traceId) {
        if (request == null) {
            return;
        }

        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String className = signature.getDeclaringTypeName();
        String methodName = signature.getName();
        String params = Arrays.toString(joinPoint.getArgs());

        IUser currentUser = getCurrentUser();
        String userName = currentUser != null ? currentUser.getUserName() : "未登录";

        if (exception != null) {
            log.error("【请求异常】TraceId: {}, 用户: {}, IP: {}, 接口: {} {}, 方法: {}.{}, 参数: {}, 耗时: {}ms, 异常: {}",
                    traceId, userName, request.getRemoteAddr(), request.getMethod(),
                    request.getRequestURI(), className, methodName, params, executionTime, exception.getMessage());
        } else {
            log.info("【请求完成】TraceId: {}, 用户: {}, IP: {}, 接口: {} {}, 方法: {}.{}, 耗时: {}ms",
                    traceId, userName, request.getRemoteAddr(), request.getMethod(),
                    request.getRequestURI(), className, methodName, executionTime);
        }
    }

    /**
     * 参数拼装
     */
    private String argsArrayToString(Object[] paramsArray) {
        StringBuilder params = new StringBuilder();
        if (paramsArray != null && paramsArray.length > 0) {
            for (Object o : paramsArray) {
                if (Objects.nonNull(o) && !isFilterObject(o)) {
                    try {
                        String jsonObj = JacksonUtils.toJson(o);
                        params.append(jsonObj).append(" ");
                    } catch (Exception e) {
                        log.warn("参数序列化失败: {}", e.getMessage());
                    }
                }
            }
        }
        return params.toString().trim();
    }

    /**
     * 判断是否需要过滤的对象
     */
    private boolean isFilterObject(final Object o) {
        Class<?> clazz = o.getClass();
        if (clazz.isArray()) {
            return clazz.getComponentType().isAssignableFrom(MultipartFile.class);
        } else if (o instanceof MultipartFile) {
            return true;
        } else if (o instanceof HttpServletRequest) {
            return true;
        } else if (o instanceof BindingResult) {
            return true;
        }
        return false;
    }

    /**
     * 获取当前用户信息
     * 这里使用简化的实现，避免循环依赖
     * 在实际使用时，可以通过 Spring Security 或其他方式获取用户信息
     */
    private IUser getCurrentUser() {
        // TODO: 这里可以通过 Spring Security 或其他方式获取当前用户
        // 暂时返回 null，避免循环依赖
        return null;
    }
}
