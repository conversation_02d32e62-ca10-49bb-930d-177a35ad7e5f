package com.hys.hm.shared.logging.config;

import com.hys.hm.shared.logging.aspect.LogAspect;
import com.hys.hm.shared.logging.service.LogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 日志自动配置类
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-22
 */
@Configuration
@Slf4j
public class LogAutoConfiguration {

    /**
     * 注册日志切面
     * 只有当存在 LogService 实现时才注册
     */
    @Bean
    @ConditionalOnBean(LogService.class)
    @ConditionalOnMissingBean(LogAspect.class)
    public LogAspect logAspect(LogService logService) {
        log.info("注册日志切面: LogAspect");
        return new LogAspect(logService);
    }
}
