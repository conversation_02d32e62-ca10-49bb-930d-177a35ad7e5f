package com.hys.hm.shared.logging.enums;

/**
 * 业务操作类型
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-22
 */
public enum BusinessType {

    /**
     * 新增
     */
    INSERT("insert", "新增"),

    /**
     * 修改
     */
    UPDATE("update", "修改"),

    /**
     * 保存或修改
     */
    SAVE_UPDATE("save_update", "保存或修改"),

    /**
     * 删除
     */
    DELETE("delete", "删除"),

    /**
     * 授权
     */
    GRANT("grant", "授权"),

    /**
     * 导出
     */
    EXPORT("export", "导出"),

    /**
     * 导入
     */
    IMPORT("import", "导入"),

    /**
     * 强退
     */
    FORCE("force", "强退"),

    /**
     * 生成代码
     */
    GENCODE("gencode", "生成代码"),

    /**
     * 清空数据
     */
    CLEAN("clear", "清空数据"),

    /**
     * 查询
     */
    SELECT("select", "查询"),

    /**
     * 登录
     */
    LOGIN("login", "登录"),

    /**
     * 登出
     */
    LOGOUT("logout", "登出"),

    /**
     * 其它
     */
    OTHER("other", "其它");

    private final String value;
    private final String description;

    BusinessType(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public String value() {
        return this.value;
    }

    public String getDescription() {
        return this.description;
    }

    /**
     * 根据值获取枚举
     */
    public static BusinessType fromValue(String value) {
        if (value == null) {
            return null;
        }
        for (BusinessType type : values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        return null;
    }
}
