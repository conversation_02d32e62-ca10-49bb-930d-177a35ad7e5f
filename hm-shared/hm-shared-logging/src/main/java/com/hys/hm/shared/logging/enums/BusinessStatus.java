package com.hys.hm.shared.logging.enums;

/**
 * 操作状态
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-22
 */
public enum BusinessStatus {

    /**
     * 成功
     */
    SUCCESS(0, "成功"),

    /**
     * 失败
     */
    FAIL(1, "失败");

    private final int code;
    private final String description;

    BusinessStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return this.code;
    }

    public String getDescription() {
        return this.description;
    }

    /**
     * 根据代码获取枚举
     */
    public static BusinessStatus fromCode(int code) {
        for (BusinessStatus status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return null;
    }
}
