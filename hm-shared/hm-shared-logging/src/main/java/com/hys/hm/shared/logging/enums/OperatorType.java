package com.hys.hm.shared.logging.enums;

/**
 * 操作人类别
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-22
 */
public enum OperatorType {

    /**
     * 后台管理用户
     */
    MANAGE("pc", "后台管理用户"),

    /**
     * 手机端用户
     */
    MOBILE("h5", "手机端用户"),

    /**
     * APP用户
     */
    APP("app", "APP用户"),

    /**
     * 其他
     */
    OTHER("other", "其他");

    private final String value;
    private final String description;

    OperatorType(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public String value() {
        return this.value;
    }

    public String getDescription() {
        return this.description;
    }

    /**
     * 根据值获取枚举
     */
    public static OperatorType fromValue(String value) {
        if (value == null) {
            return null;
        }
        for (OperatorType type : values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        return null;
    }
}
