package com.hys.hm.shared.logging.service;

import com.hys.hm.shared.logging.model.LogRecord;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 日志服务接口
 * 定义日志记录的核心操作
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-22
 */
public interface LogService {

    /**
     * 同步保存日志记录
     * 
     * @param logRecord 日志记录
     * @return 是否保存成功
     */
    boolean saveLog(LogRecord logRecord);

    /**
     * 异步保存日志记录
     * 
     * @param logRecord 日志记录
     * @return 异步结果
     */
    CompletableFuture<Boolean> saveLogAsync(LogRecord logRecord);

    /**
     * 批量保存日志记录
     * 
     * @param logRecords 日志记录列表
     * @return 保存成功的数量
     */
    int batchSaveLog(List<LogRecord> logRecords);

    /**
     * 异步批量保存日志记录
     * 
     * @param logRecords 日志记录列表
     * @return 异步结果
     */
    CompletableFuture<Integer> batchSaveLogAsync(List<LogRecord> logRecords);

    /**
     * 根据ID查询日志记录
     * 
     * @param id 日志ID
     * @return 日志记录
     */
    LogRecord getLogById(Long id);

    /**
     * 根据条件查询日志记录
     * 
     * @param condition 查询条件
     * @return 日志记录列表
     */
    List<LogRecord> queryLogs(LogQueryCondition condition);

    /**
     * 删除过期日志
     * 
     * @param days 保留天数
     * @return 删除的记录数
     */
    int deleteExpiredLogs(int days);

    /**
     * 日志查询条件
     */
    interface LogQueryCondition {
        // 查询条件的具体定义可以根据需要扩展
    }
}
