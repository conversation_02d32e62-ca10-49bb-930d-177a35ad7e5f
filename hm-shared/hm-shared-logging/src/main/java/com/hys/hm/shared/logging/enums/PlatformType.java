package com.hys.hm.shared.logging.enums;

/**
 * 平台类型枚举
 * 定义系统支持的平台类型
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-22
 */
public enum PlatformType {

    /**
     * 健康管理平台
     */
    HM("hm", "健康管理平台"),

    /**
     * 管理后台
     */
    ADMIN("admin", "管理后台"),

    /**
     * 移动端
     */
    MOBILE("mobile", "移动端"),

    /**
     * API接口
     */
    API("api", "API接口"),

    /**
     * 其他平台（预留扩展）
     */
    OTHER("other", "其他平台");

    private final String code;
    private final String description;

    PlatformType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return this.code;
    }

    public String getDescription() {
        return this.description;
    }

    /**
     * 根据代码获取枚举
     */
    public static PlatformType fromCode(String code) {
        if (code == null) {
            return null;
        }
        for (PlatformType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }
}
