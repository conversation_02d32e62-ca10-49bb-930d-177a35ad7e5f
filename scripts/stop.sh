#!/bin/bash

# 健康管理系统 - 停止脚本

echo "========================================="
echo "停止健康管理系统"
echo "========================================="

# 查找Java进程
java_pids=$(ps aux | grep "hm-bootstrap" | grep -v grep | awk '{print $2}')

if [ -z "$java_pids" ]; then
    echo "未找到运行中的健康管理系统进程"
    
    # 检查PID文件
    if [ -f "/var/run/hm.pid" ]; then
        pid=$(cat /var/run/hm.pid)
        if ps -p $pid > /dev/null 2>&1; then
            echo "从PID文件找到进程: $pid"
            java_pids=$pid
        else
            echo "PID文件中的进程已不存在，清理PID文件"
            rm -f /var/run/hm.pid
        fi
    fi
fi

if [ -z "$java_pids" ]; then
    echo "没有需要停止的进程"
    exit 0
fi

echo "找到以下进程:"
for pid in $java_pids; do
    echo "  PID: $pid"
    ps -p $pid -o pid,ppid,cmd --no-headers
done

# 优雅停止
echo ""
echo "尝试优雅停止..."
for pid in $java_pids; do
    echo "发送TERM信号到进程 $pid"
    kill -TERM $pid
done

# 等待进程停止
echo "等待进程停止..."
for i in {1..30}; do
    all_stopped=true
    for pid in $java_pids; do
        if ps -p $pid > /dev/null 2>&1; then
            all_stopped=false
            break
        fi
    done
    
    if $all_stopped; then
        echo "所有进程已优雅停止"
        break
    fi
    
    echo -n "."
    sleep 1
done

# 检查是否还有进程运行
remaining_pids=""
for pid in $java_pids; do
    if ps -p $pid > /dev/null 2>&1; then
        remaining_pids="$remaining_pids $pid"
    fi
done

# 强制停止剩余进程
if [ ! -z "$remaining_pids" ]; then
    echo ""
    echo "强制停止剩余进程..."
    for pid in $remaining_pids; do
        echo "发送KILL信号到进程 $pid"
        kill -KILL $pid
    done
    
    sleep 2
    
    # 最终检查
    for pid in $remaining_pids; do
        if ps -p $pid > /dev/null 2>&1; then
            echo "警告: 进程 $pid 仍在运行"
        else
            echo "进程 $pid 已停止"
        fi
    done
fi

# 清理PID文件
if [ -f "/var/run/hm.pid" ]; then
    rm -f /var/run/hm.pid
    echo "已清理PID文件"
fi

echo "========================================="
echo "停止操作完成"
echo "========================================="
