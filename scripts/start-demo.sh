#!/bin/bash

# 健康管理系统 - 演示环境启动脚本

echo "========================================="
echo "启动健康管理系统 - 演示环境"
echo "========================================="

# 设置环境变量
export SPRING_PROFILES_ACTIVE=demo
export DB_HOST=${DB_HOST:-demo.hm.com}
export DB_USERNAME=${DB_USERNAME:-hm_demo}
export DB_PASSWORD=${DB_PASSWORD:-demo_secure_password}
export REDIS_HOST=${REDIS_HOST:-demo-redis.hm.com}
export REDIS_PASSWORD=${REDIS_PASSWORD:-demo_redis_secure_pass}
export JWT_SECRET=${JWT_SECRET:-demo_jwt_secret_key_for_health_management_system_demo_env}

# 检查必需的环境变量
if [ -z "$DB_PASSWORD" ]; then
    echo "错误: 请设置 DB_PASSWORD 环境变量"
    exit 1
fi

if [ -z "$REDIS_PASSWORD" ]; then
    echo "错误: 请设置 REDIS_PASSWORD 环境变量"
    exit 1
fi

# 检查Java版本
java_version=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}')
echo "Java版本: $java_version"

# 检查数据库连接
echo "检查演示数据库连接..."
mysql -h $DB_HOST -u $DB_USERNAME -p$DB_PASSWORD -e "SELECT 1;" health_management_demo 2>/dev/null
if [ $? -ne 0 ]; then
    echo "错误: 演示数据库连接失败"
    exit 1
fi

# 检查Redis连接
echo "检查Redis连接..."
redis-cli -h $REDIS_HOST -a $REDIS_PASSWORD ping 2>/dev/null
if [ $? -ne 0 ]; then
    echo "错误: Redis连接失败"
    exit 1
fi

# 构建项目
echo "构建项目..."
mvn clean package -DskipTests
if [ $? -ne 0 ]; then
    echo "错误: 项目构建失败"
    exit 1
fi

# 备份当前数据（如果需要）
if [ "$BACKUP_BEFORE_START" = "true" ]; then
    echo "备份演示数据..."
    timestamp=$(date +%Y%m%d_%H%M%S)
    mysqldump -h $DB_HOST -u $DB_USERNAME -p$DB_PASSWORD health_management_demo > "backup/demo_backup_$timestamp.sql"
fi

# 重置演示数据（如果需要）
if [ "$RESET_DEMO_DATA" = "true" ]; then
    echo "重置演示数据..."
    mysql -h $DB_HOST -u $DB_USERNAME -p$DB_PASSWORD health_management_demo < scripts/demo-data.sql
fi

# 启动应用
echo "启动演示环境..."
echo "访问地址: http://localhost:8082/hm-demo"
echo "管理端点: http://localhost:8081/actuator"
echo "演示环境特性:"
echo "  - 最大用户数: 50"
echo "  - 会话超时: 30分钟"
echo "  - 每天凌晨2点自动重置数据"
echo "  - 禁止数据导出/导入"
echo "========================================="

java -jar hm-bootstrap/target/hm-bootstrap-*.jar --spring.profiles.active=demo
