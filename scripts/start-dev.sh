#!/bin/bash

# 健康管理系统 - 开发环境启动脚本

echo "========================================="
echo "启动健康管理系统 - 开发环境"
echo "========================================="

# 设置环境变量
export SPRING_PROFILES_ACTIVE=dev
export DB_USERNAME=${DB_USERNAME:-root}
export DB_PASSWORD=${DB_PASSWORD:-123456}
export JWT_SECRET=${JWT_SECRET:-dev_jwt_secret_key_for_health_management_system}

# 检查Java版本
java_version=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}')
echo "Java版本: $java_version"

if [[ "$java_version" < "21" ]]; then
    echo "错误: 需要Java 21或更高版本"
    exit 1
fi

# 检查数据库连接
echo "检查数据库连接..."
mysql -h localhost -u $DB_USERNAME -p$DB_PASSWORD -e "SELECT 1;" health_management 2>/dev/null
if [ $? -ne 0 ]; then
    echo "警告: 数据库连接失败，请确保MySQL已启动并且数据库存在"
    echo "创建数据库命令: CREATE DATABASE health_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
fi

# 检查Redis连接
echo "检查Redis连接..."
redis-cli ping 2>/dev/null
if [ $? -ne 0 ]; then
    echo "警告: Redis连接失败，请确保Redis已启动"
fi

# 构建项目
echo "构建项目..."
mvn clean compile -DskipTests
if [ $? -ne 0 ]; then
    echo "错误: 项目构建失败"
    exit 1
fi

# 启动应用
echo "启动应用..."
echo "访问地址: http://localhost:8080/hm-dev"
echo "管理端点: http://localhost:8081/actuator"
echo "按 Ctrl+C 停止应用"
echo "========================================="

mvn spring-boot:run -pl hm-bootstrap -Dspring-boot.run.profiles=dev
