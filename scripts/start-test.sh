#!/bin/bash

# 健康管理系统 - 测试环境启动脚本

echo "========================================="
echo "启动健康管理系统 - 测试环境"
echo "========================================="

# 设置环境变量
export SPRING_PROFILES_ACTIVE=test
export DB_HOST=${DB_HOST:-localhost}
export DB_PORT=${DB_PORT:-3306}
export DB_NAME=${DB_NAME:-health_management_test}
export DB_USERNAME=${DB_USERNAME:-hm_test}
export DB_PASSWORD=${DB_PASSWORD:-test_password}
export REDIS_HOST=${REDIS_HOST:-localhost}
export REDIS_PORT=${REDIS_PORT:-6380}
export REDIS_PASSWORD=${REDIS_PASSWORD:-test_redis_pass}
export JWT_SECRET=${JWT_SECRET:-test_jwt_secret_key_for_health_management_system}

# 检查Java版本
java_version=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}')
echo "Java版本: $java_version"

# 检查测试数据库
echo "检查测试数据库连接..."
mysql -h $DB_HOST -P $DB_PORT -u $DB_USERNAME -p$DB_PASSWORD -e "SELECT 1;" $DB_NAME 2>/dev/null
if [ $? -ne 0 ]; then
    echo "创建测试数据库..."
    mysql -h $DB_HOST -P $DB_PORT -u root -p -e "
        CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
        CREATE USER IF NOT EXISTS '$DB_USERNAME'@'%' IDENTIFIED BY '$DB_PASSWORD';
        GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USERNAME'@'%';
        FLUSH PRIVILEGES;
    "
fi

# 检查Redis连接
echo "检查Redis连接..."
redis-cli -h $REDIS_HOST -p $REDIS_PORT ping 2>/dev/null
if [ $? -ne 0 ]; then
    echo "警告: Redis连接失败，请确保Redis已启动"
fi

# 运行测试
echo "运行单元测试..."
mvn test -DskipIntegrationTests=true
if [ $? -ne 0 ]; then
    echo "警告: 单元测试失败"
fi

# 构建项目
echo "构建项目..."
mvn clean compile -DskipTests
if [ $? -ne 0 ]; then
    echo "错误: 项目构建失败"
    exit 1
fi

# 启动应用
echo "启动测试环境..."
echo "访问地址: http://localhost:8081/hm-test"
echo "管理端点: http://localhost:8081/actuator"
echo "========================================="

java -jar hm-bootstrap/target/hm-bootstrap-*.jar --spring.profiles.active=test
