#!/bin/bash

# 健康管理系统 - 生产环境启动脚本

echo "========================================="
echo "启动健康管理系统 - 生产环境"
echo "========================================="

# 设置环境变量
export SPRING_PROFILES_ACTIVE=prod

# 检查必需的环境变量
required_vars=(
    "DB_HOST"
    "DB_USERNAME" 
    "DB_PASSWORD"
    "REDIS_HOST"
    "REDIS_PASSWORD"
    "JWT_SECRET"
    "AI_API_KEY"
    "SMTP_HOST"
    "SMTP_USERNAME"
    "SMTP_PASSWORD"
)

echo "检查必需的环境变量..."
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "错误: 环境变量 $var 未设置"
        exit 1
    fi
done

# 设置默认值
export DB_PORT=${DB_PORT:-3306}
export DB_NAME=${DB_NAME:-health_management_prod}
export REDIS_PORT=${REDIS_PORT:-6379}
export REDIS_DATABASE=${REDIS_DATABASE:-0}
export MANAGEMENT_PORT=${MANAGEMENT_PORT:-8081}
export FILE_UPLOAD_PATH=${FILE_UPLOAD_PATH:-/var/hm/uploads}
export BACKUP_LOCATION=${BACKUP_LOCATION:-/var/hm/backups}

# 检查Java版本
java_version=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}')
echo "Java版本: $java_version"

if [[ "$java_version" < "21" ]]; then
    echo "错误: 生产环境需要Java 21或更高版本"
    exit 1
fi

# 检查数据库连接
echo "检查生产数据库连接..."
mysql -h $DB_HOST -P $DB_PORT -u $DB_USERNAME -p$DB_PASSWORD -e "SELECT 1;" $DB_NAME 2>/dev/null
if [ $? -ne 0 ]; then
    echo "错误: 生产数据库连接失败"
    exit 1
fi

# 检查Redis连接
echo "检查Redis连接..."
redis-cli -h $REDIS_HOST -p $REDIS_PORT -a $REDIS_PASSWORD ping 2>/dev/null
if [ $? -ne 0 ]; then
    echo "错误: Redis连接失败"
    exit 1
fi

# 检查文件目录权限
echo "检查文件目录..."
if [ ! -d "$FILE_UPLOAD_PATH" ]; then
    echo "创建上传目录: $FILE_UPLOAD_PATH"
    mkdir -p "$FILE_UPLOAD_PATH"
fi

if [ ! -d "$BACKUP_LOCATION" ]; then
    echo "创建备份目录: $BACKUP_LOCATION"
    mkdir -p "$BACKUP_LOCATION"
fi

if [ ! -d "/var/log/hm" ]; then
    echo "创建日志目录: /var/log/hm"
    mkdir -p "/var/log/hm"
fi

# 检查端口占用
echo "检查端口占用..."
if lsof -i:8080 > /dev/null 2>&1; then
    echo "错误: 端口8080已被占用"
    exit 1
fi

if lsof -i:$MANAGEMENT_PORT > /dev/null 2>&1; then
    echo "错误: 管理端口$MANAGEMENT_PORT已被占用"
    exit 1
fi

# 构建项目
echo "构建生产版本..."
mvn clean package -DskipTests -Pprod
if [ $? -ne 0 ]; then
    echo "错误: 生产版本构建失败"
    exit 1
fi

# 备份数据库（生产环境启动前备份）
echo "备份生产数据库..."
timestamp=$(date +%Y%m%d_%H%M%S)
backup_file="$BACKUP_LOCATION/prod_backup_$timestamp.sql"
mysqldump -h $DB_HOST -P $DB_PORT -u $DB_USERNAME -p$DB_PASSWORD $DB_NAME > "$backup_file"
if [ $? -eq 0 ]; then
    echo "数据库备份完成: $backup_file"
    gzip "$backup_file"
else
    echo "警告: 数据库备份失败"
fi

# 设置JVM参数
export JAVA_OPTS="
    -Xms2g 
    -Xmx4g 
    -XX:+UseG1GC 
    -XX:MaxGCPauseMillis=200 
    -XX:+HeapDumpOnOutOfMemoryError 
    -XX:HeapDumpPath=/var/log/hm/
    -Dfile.encoding=UTF-8
    -Duser.timezone=Asia/Shanghai
"

# 启动应用
echo "启动生产环境..."
echo "访问地址: http://localhost:8080/hm"
echo "管理端点: http://localhost:$MANAGEMENT_PORT/actuator"
echo "日志文件: /var/log/hm/hm-prod.log"
echo "上传目录: $FILE_UPLOAD_PATH"
echo "备份目录: $BACKUP_LOCATION"
echo "========================================="

# 使用nohup在后台启动
nohup java $JAVA_OPTS -jar hm-bootstrap/target/hm-bootstrap-*.jar \
    --spring.profiles.active=prod \
    > /var/log/hm/startup.log 2>&1 &

# 保存进程ID
echo $! > /var/run/hm.pid

echo "应用已在后台启动，进程ID: $(cat /var/run/hm.pid)"
echo "查看启动日志: tail -f /var/log/hm/startup.log"
echo "查看应用日志: tail -f /var/log/hm/hm-prod.log"
echo "停止应用: kill $(cat /var/run/hm.pid)"
