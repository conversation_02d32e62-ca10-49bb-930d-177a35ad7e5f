#!/bin/bash

# 健康管理系统版本管理脚本
# 使用方法：
#   ./scripts/version-manager.sh current                    # 显示当前版本
#   ./scripts/version-manager.sh set 1.0.0                 # 设置版本号
#   ./scripts/version-manager.sh bump major                # 升级主版本号
#   ./scripts/version-manager.sh bump minor                # 升级次版本号
#   ./scripts/version-manager.sh bump patch                # 升级补丁版本号
#   ./scripts/version-manager.sh snapshot                  # 切换到SNAPSHOT版本
#   ./scripts/version-manager.sh release                   # 切换到正式版本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
POM_FILE="$PROJECT_ROOT/pom.xml"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取当前版本
get_current_version() {
    local revision=$(grep -o '<revision>[^<]*</revision>' "$POM_FILE" | sed 's/<revision>\(.*\)<\/revision>/\1/')
    local changelist=$(grep -o '<changelist>[^<]*</changelist>' "$POM_FILE" | sed 's/<changelist>\(.*\)<\/changelist>/\1/')
    local sha1=$(grep -o '<sha1>[^<]*</sha1>' "$POM_FILE" | sed 's/<sha1>\(.*\)<\/sha1>/\1/')
    echo "${revision}${changelist}${sha1}"
}

# 设置版本号
set_version() {
    local new_version="$1"

    if [[ -z "$new_version" ]]; then
        log_error "版本号不能为空"
        exit 1
    fi

    log_info "设置版本号为: $new_version"

    # 检查Maven是否可用
    if ! command -v mvn &> /dev/null; then
        log_warning "Maven不可用，使用传统方式更新版本"
        update_version_manually "$new_version"
        return
    fi

    # 使用Maven versions插件更新所有模块版本
    log_info "使用Maven versions插件更新所有模块版本..."
    cd "$PROJECT_ROOT"

    # 更新所有模块的版本号
    mvn versions:set -DnewVersion="$new_version" -DgenerateBackupPoms=false -q

    if [[ $? -eq 0 ]]; then
        # Maven插件成功后，还需要手动更新根POM的revision属性
        log_info "更新根POM的revision属性..."
        update_revision_property "$new_version"
        log_success "所有模块版本号已更新为: $new_version"
    else
        log_error "Maven版本更新失败，尝试手动更新"
        update_version_manually "$new_version"
    fi
}

# 更新根POM的revision属性
update_revision_property() {
    local new_version="$1"

    # 更新revision
    sed -i.bak "s/<revision>[^<]*<\/revision>/<revision>$new_version<\/revision>/" "$POM_FILE"

    # 清空changelist和sha1
    sed -i.bak "s/<changelist>[^<]*<\/changelist>/<changelist><\/changelist>/" "$POM_FILE"
    sed -i.bak "s/<sha1>[^<]*<\/sha1>/<sha1><\/sha1>/" "$POM_FILE"

    # 删除备份文件
    rm -f "$POM_FILE.bak"
}

# 手动更新版本号（备用方案）
update_version_manually() {
    local new_version="$1"

    log_info "手动更新根POM文件版本..."

    # 更新revision属性
    update_revision_property "$new_version"

    log_success "根POM版本号已更新为: $new_version"
    log_warning "注意：只更新了根POM文件，子模块版本可能需要手动检查"
}

# 解析版本号
parse_version() {
    local version="$1"
    # 移除-SNAPSHOT后缀
    version=$(echo "$version" | sed 's/-SNAPSHOT//')

    # 分割版本号
    IFS='.' read -ra VERSION_PARTS <<< "$version"

    MAJOR=${VERSION_PARTS[0]:-0}
    MINOR=${VERSION_PARTS[1]:-0}
    PATCH=${VERSION_PARTS[2]:-0}
}

# 升级版本号
bump_version() {
    local bump_type="$1"
    local current_version=$(get_current_version)

    log_info "当前版本: $current_version"

    parse_version "$current_version"

    case "$bump_type" in
        major)
            MAJOR=$((MAJOR + 1))
            MINOR=0
            PATCH=0
            ;;
        minor)
            MINOR=$((MINOR + 1))
            PATCH=0
            ;;
        patch)
            PATCH=$((PATCH + 1))
            ;;
        *)
            log_error "无效的升级类型: $bump_type (支持: major, minor, patch)"
            exit 1
            ;;
    esac

    local new_version="$MAJOR.$MINOR.$PATCH"

    # 如果当前版本是SNAPSHOT，新版本也保持SNAPSHOT
    if [[ "$current_version" == *"-SNAPSHOT" ]]; then
        new_version="$new_version-SNAPSHOT"
    fi

    set_version "$new_version"
}

# 切换到SNAPSHOT版本
set_snapshot() {
    local current_version=$(get_current_version)

    if [[ "$current_version" == *"-SNAPSHOT" ]]; then
        log_warning "当前版本已经是SNAPSHOT版本: $current_version"
        return
    fi

    local snapshot_version="$current_version-SNAPSHOT"
    set_version "$snapshot_version"
}

# 切换到正式版本
set_release() {
    local current_version=$(get_current_version)

    if [[ "$current_version" != *"-SNAPSHOT" ]]; then
        log_warning "当前版本已经是正式版本: $current_version"
        return
    fi

    local release_version=$(echo "$current_version" | sed 's/-SNAPSHOT//')
    set_version "$release_version"
}

# 检查所有模块版本一致性
check_versions() {
    log_info "检查所有模块版本一致性..."

    local root_version=$(get_current_version)
    log_info "根模块版本: $root_version"

    # 查找所有pom.xml文件
    local inconsistent_count=0

    while IFS= read -r -d '' pom_file; do
        # 跳过根pom文件
        if [[ "$pom_file" == "$POM_FILE" ]]; then
            continue
        fi

        # 获取模块版本
        local module_version=""
        if grep -q "<version>" "$pom_file"; then
            module_version=$(grep -o '<version>[^<]*</version>' "$pom_file" | head -1 | sed 's/<version>\(.*\)<\/version>/\1/')
        fi

        # 如果模块有明确的版本号且与根版本不一致
        if [[ -n "$module_version" && "$module_version" != "$root_version" && "$module_version" != "\${project.version}" && "$module_version" != "\${revision}\${changelist}\${sha1}" ]]; then
            log_warning "版本不一致: $pom_file -> $module_version"
            inconsistent_count=$((inconsistent_count + 1))
        fi
    done < <(find "$PROJECT_ROOT" -name "pom.xml" -print0)

    if [[ $inconsistent_count -eq 0 ]]; then
        log_success "所有模块版本一致"
    else
        log_warning "发现 $inconsistent_count 个模块版本不一致"
        log_info "建议运行: $0 set $root_version 来统一所有模块版本"
    fi
}

# 显示帮助信息
show_help() {
    echo "健康管理系统版本管理脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 current                    显示当前版本"
    echo "  $0 set <version>              设置所有模块版本号"
    echo "  $0 bump <type>                升级版本号 (major|minor|patch)"
    echo "  $0 snapshot                   切换到SNAPSHOT版本"
    echo "  $0 release                    切换到正式版本"
    echo "  $0 check                      检查所有模块版本一致性"
    echo "  $0 help                       显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 current                    # 显示: 0.0.1-SNAPSHOT"
    echo "  $0 set 1.0.0                 # 设置所有模块为: 1.0.0"
    echo "  $0 bump major                 # 1.0.0 -> 2.0.0"
    echo "  $0 bump minor                 # 1.0.0 -> 1.1.0"
    echo "  $0 bump patch                 # 1.0.0 -> 1.0.1"
    echo "  $0 snapshot                   # 1.0.0 -> 1.0.0-SNAPSHOT"
    echo "  $0 release                    # 1.0.0-SNAPSHOT -> 1.0.0"
    echo "  $0 check                      # 检查版本一致性"
    echo ""
    echo "注意:"
    echo "  - 使用Maven versions插件统一更新所有模块版本"
    echo "  - 如果Maven不可用，将只更新根POM文件"
}

# 主函数
main() {
    case "${1:-help}" in
        current)
            echo "当前版本: $(get_current_version)"
            ;;
        set)
            set_version "$2"
            ;;
        bump)
            bump_version "$2"
            ;;
        snapshot)
            set_snapshot
            ;;
        release)
            set_release
            ;;
        check)
            check_versions
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 检查pom.xml文件是否存在
if [[ ! -f "$POM_FILE" ]]; then
    log_error "找不到pom.xml文件: $POM_FILE"
    exit 1
fi

# 执行主函数
main "$@"
