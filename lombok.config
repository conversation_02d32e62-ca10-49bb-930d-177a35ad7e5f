# Lombok configuration file
# This file configures Lombok behavior for the entire project

# Enable annotation processing
lombok.addLombokGeneratedAnnotation = true

# Configure log field name
lombok.log.fieldName = log

# Configure log field as static final
lombok.log.fieldIsStatic = true

# Configure builder
lombok.builder.className = Builder

# Configure accessors
lombok.accessors.chain = false
lombok.accessors.fluent = false

# Configure equals and hashcode
lombok.equalsAndHashCode.callSuper = call

# Configure toString
lombok.toString.includeFieldNames = true

# Configure copy annotations
lombok.copyableAnnotations += org.springframework.beans.factory.annotation.Qualifier
lombok.copyableAnnotations += org.springframework.beans.factory.annotation.Value
lombok.copyableAnnotations += org.springframework.context.annotation.Lazy
