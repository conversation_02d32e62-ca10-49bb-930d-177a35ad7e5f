# 外部服务配置
hm:
  external:
    # 是否启用外部服务
    enabled: true

    # 连接超时时间（秒）
    connect-timeout-seconds: 10

    # 读取超时时间（秒）
    read-timeout-seconds: 30

    # 重试次数
    retry-count: 3

    # 微信服务配置
    wechat:
      # 微信服务类型: mp(公众号), miniprogram(小程序), work(企业微信)
      service-type: ${WECHAT_SERVICE_TYPE:mp}

      # 微信公众号配置
      mp:
        enabled: true
        app-id: ${WECHAT_MP_APP_ID:your-app-id}
        app-secret: ${WECHAT_MP_APP_SECRET:your-app-secret}
        token: ${WECHAT_MP_TOKEN:your-token}
        encoding-aes-key: ${WECHAT_MP_ENCODING_AES_KEY:your-encoding-aes-key}
        server-url: "https://api.weixin.qq.com"
        timeout-seconds: 30
        max-retries: 3
        enable-message-encryption: false
        token-refresh-advance-seconds: 300

      # 微信小程序配置
      miniprogram:
        enabled: false
        app-id: ${WECHAT_MINIPROGRAM_APP_ID:your-miniprogram-app-id}
        app-secret: ${WECHAT_MINIPROGRAM_APP_SECRET:your-miniprogram-app-secret}
        server-url: "https://api.weixin.qq.com"
        timeout-seconds: 30
        max-retries: 3
        token-refresh-advance-seconds: 300
        enable-analytics: true
        enable-content-security: true
        enable-subscribe-message: true
        enable-uniform-message: true

    # 短信服务配置
    sms:
      # 短信服务提供商: tencent, aliyun, baidu
      provider: ${SMS_PROVIDER:tencent}

      # 腾讯云短信配置
      tencent:
        enabled: true
        secret-id: ${TENCENT_SMS_SECRET_ID:your-secret-id}
        secret-key: ${TENCENT_SMS_SECRET_KEY:your-secret-key}
        app-id: ${TENCENT_SMS_APP_ID:your-app-id}
        region: "ap-beijing"
        default-sign-name: ${TENCENT_SMS_SIGN_NAME:your-sign-name}
        timeout-seconds: 30
        max-retries: 3

      # 阿里云短信配置
      aliyun:
        enabled: false
        access-key-id: ${ALIYUN_SMS_ACCESS_KEY_ID:your-access-key-id}
        access-key-secret: ${ALIYUN_SMS_ACCESS_KEY_SECRET:your-access-key-secret}
        region-id: "cn-hangzhou"
        default-sign-name: ${ALIYUN_SMS_SIGN_NAME:your-sign-name}
        timeout-seconds: 30
        max-retries: 3

    # 自定义服务配置
    custom:
      third-party-api: "https://api.thirdparty.com"
      internal-service: "http://internal.service.com"

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev

hm:
  external:
    wechat:
      server-url: "https://api.weixin.qq.com"
    sms:
      provider: "tencent"

---
# 测试环境配置
spring:
  config:
    activate:
      on-profile: test

hm:
  external:
    wechat:
      server-url: "https://api.weixin.qq.com"
    sms:
      provider: "tencent"

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod

hm:
  external:
    wechat:
      server-url: "https://api.weixin.qq.com"
    sms:
      provider: "tencent"
