<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.hys</groupId>
        <artifactId>hm-infrastructure</artifactId>
        <version>3.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>hm-infrastructure-logging</artifactId>
    <packaging>jar</packaging>
    <name>hm-infrastructure-logging</name>
    <description>基础设施日志模块 - 实体、持久化、外部集成</description>

    <dependencies>
        <!-- 共享日志模块 -->
        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-shared-logging</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- 共享框架模块 -->
        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-shared-framework</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- Spring Boot Starter Data JPA -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>

        <!-- Spring Boot Starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <!-- MySQL Driver -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>

</project>
