package com.hys.hm.infrastructure.logging.service;

import com.hys.hm.infrastructure.logging.entity.SystemLogEntity;
import com.hys.hm.infrastructure.logging.repository.SystemLogRepository;
import com.hys.hm.shared.framework.repository.BaseRepository;
import com.hys.hm.shared.framework.service.BaseServiceImpl;
import com.hys.hm.shared.logging.model.LogRecord;
import com.hys.hm.shared.logging.service.LogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 日志服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-22
 */
@Service
@Slf4j
public class LogServiceImpl extends BaseServiceImpl<SystemLogEntity, Long> implements LogService {

    private final SystemLogRepository systemLogRepository;

    public LogServiceImpl(SystemLogRepository systemLogRepository) {
        super(systemLogRepository);
        this.systemLogRepository = systemLogRepository;
    }

    @Override
    @Transactional
    public boolean saveLog(LogRecord logRecord) {
        try {
            SystemLogEntity entity = convertToEntity(logRecord);
            systemLogRepository.save(entity);
            return true;
        } catch (Exception e) {
            log.error("保存日志记录失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Async("logTaskExecutor")
    public CompletableFuture<Boolean> saveLogAsync(LogRecord logRecord) {
        return CompletableFuture.supplyAsync(() -> saveLog(logRecord));
    }

    @Override
    @Transactional
    public int batchSaveLog(List<LogRecord> logRecords) {
        try {
            List<SystemLogEntity> entities = logRecords.stream()
                    .map(this::convertToEntity)
                    .collect(Collectors.toList());

            List<SystemLogEntity> savedEntities = systemLogRepository.saveAll(entities);
            return savedEntities.size();
        } catch (Exception e) {
            log.error("批量保存日志记录失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    @Async("logTaskExecutor")
    public CompletableFuture<Integer> batchSaveLogAsync(List<LogRecord> logRecords) {
        return CompletableFuture.supplyAsync(() -> batchSaveLog(logRecords));
    }

    @Override
    public LogRecord getLogById(Long id) {
        return systemLogRepository.findById(id)
                .map(this::convertToLogRecord)
                .orElse(null);
    }

    @Override
    public List<LogRecord> queryLogs(LogQueryCondition condition) {
        // 这里可以根据具体的查询条件实现复杂查询
        // 暂时返回最近的100条记录
        List<SystemLogEntity> entities = systemLogRepository.findAll()
                .stream()
                .sorted((a, b) -> b.getOperateTime().compareTo(a.getOperateTime()))
                .limit(100)
                .collect(Collectors.toList());

        return entities.stream()
                .map(this::convertToLogRecord)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public int deleteExpiredLogs(int days) {
        LocalDateTime expiredTime = LocalDateTime.now().minusDays(days);
        try {
            int deletedCount = systemLogRepository.deleteByOperateTimeBefore(expiredTime);
            log.info("删除{}天前的日志记录，共删除{}条", days, deletedCount);
            return deletedCount;
        } catch (Exception e) {
            log.error("删除过期日志失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 将 LogRecord 转换为 SystemLogEntity
     */
    private SystemLogEntity convertToEntity(LogRecord logRecord) {
        SystemLogEntity entity = new SystemLogEntity();
        BeanUtils.copyProperties(logRecord, entity);

        // 设置创建时间
        if (entity.getOperateTime() == null) {
            entity.setOperateTime(LocalDateTime.now());
        }

        return entity;
    }

    /**
     * 将 SystemLogEntity 转换为 LogRecord
     */
    private LogRecord convertToLogRecord(SystemLogEntity entity) {
        LogRecord logRecord = new LogRecord();
        BeanUtils.copyProperties(entity, logRecord);
        return logRecord;
    }

    /**
     * 根据加密字段值精确查找实体列表
     * 自动处理加密字段的查询逻辑
     *
     * @param fieldName 字段名
     * @param value     字段值（明文）
     * @return 实体列表
     */
    @Override
    public List<SystemLogEntity> findByEncryptedField(String fieldName, Object value) {
        return List.of();
    }

    /**
     * 根据加密字段值精确查找第一个实体
     * 自动处理加密字段的查询逻辑
     *
     * @param fieldName 字段名
     * @param value     字段值（明文）
     * @return 实体（可能为空）
     */
    @Override
    public Optional<SystemLogEntity> findFirstByEncryptedField(String fieldName, Object value) {
        return Optional.empty();
    }

    /**
     * 根据加密字段值模糊查找实体列表
     * 自动处理加密字段的模糊查询逻辑
     *
     * @param fieldName 字段名
     * @param value     字段值（明文）
     * @return 实体列表
     */
    @Override
    public List<SystemLogEntity> findByEncryptedFieldLike(String fieldName, Object value) {
        return List.of();
    }
}
