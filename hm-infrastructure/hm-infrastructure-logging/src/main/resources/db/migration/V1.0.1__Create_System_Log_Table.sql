-- 创建系统操作日志表
CREATE TABLE IF NOT EXISTS `sys_operation_log` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '日志主键',
    `trace_id` VARCHAR(50) NOT NULL COMMENT '追踪ID',
    `title` VARCHAR(100) COMMENT '模块标题',
    `request_method` VARCHAR(10) COMMENT '请求方式',
    `method_name` VARCHAR(200) COMMENT '方法名称',
    `request_url` VARCHAR(500) COMMENT '请求URL',
    `project_record_log` TINYINT(1) DEFAULT 0 COMMENT '是否项目日志',
    `request_ip` VARCHAR(50) COMMENT '主机地址',
    `operator_type` VARCHAR(20) COMMENT '操作类别',
    `business_type` VARCHAR(20) COMMENT '业务类型',
    `platform_type` VARCHAR(20) COMMENT '平台类型',
    `user_name` VARCHAR(50) COMMENT '操作人员登录名',
    `real_name` VARCHAR(50) COMMENT '用户真实姓名',
    `location` VARCHAR(100) COMMENT '操作地点',
    `status` INT DEFAULT 0 COMMENT '操作状态(0成功 1失败)',
    `request_param` TEXT COMMENT '请求参数',
    `response_result` TEXT COMMENT '返回参数',
    `error_message` TEXT COMMENT '错误消息',
    `operate_time` DATETIME NOT NULL COMMENT '操作时间',
    `execution_time` BIGINT COMMENT '执行耗时（毫秒）',
    `user_agent` VARCHAR(500) COMMENT '用户代理',
    `referer` VARCHAR(200) COMMENT '请求来源',
    `session_id` VARCHAR(50) COMMENT '会话ID',
    -- BaseEntity 继承字段
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` VARCHAR(32) COMMENT '创建者ID',
    `update_by` VARCHAR(32) COMMENT '更新者ID',
    `deleted` INT DEFAULT 0 COMMENT '逻辑删除标记：0-未删除，1-已删除',
    `version` BIGINT DEFAULT 0 COMMENT '版本号（乐观锁）',
    `remark` VARCHAR(500) COMMENT '备注',
    PRIMARY KEY (`id`),
    INDEX `idx_trace_id` (`trace_id`),
    INDEX `idx_user_name` (`user_name`),
    INDEX `idx_operate_time` (`operate_time`),
    INDEX `idx_request_url` (`request_url`),
    INDEX `idx_status` (`status`),
    INDEX `idx_business_type` (`business_type`),
    INDEX `idx_platform_type` (`platform_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统操作日志表';
