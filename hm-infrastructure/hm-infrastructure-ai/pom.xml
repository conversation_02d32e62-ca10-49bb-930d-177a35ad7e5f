<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <parent>
        <groupId>com.hys</groupId>
        <artifactId>hm-infrastructure</artifactId>
        <version>3.0.1-SNAPSHOT</version>
    </parent>
    
    <artifactId>hm-infrastructure-ai</artifactId>
    <name>hm-infrastructure-ai</name>
    <description>AI服务基础设施模块</description>

    <dependencies>
        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-shared-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-shared-types</artifactId>
        </dependency>
    </dependencies>

</project>
