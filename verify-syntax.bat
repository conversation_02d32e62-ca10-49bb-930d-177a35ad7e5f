@echo off
echo 正在验证代码语法...

echo.
echo 检查 BaseRepositoryImpl.java 语法...
javac -cp "hm-shared\hm-shared-framework\src\main\java;hm-shared\hm-shared-common\src\main\java;hm-shared\hm-shared-encrypt\src\main\java" ^
      -sourcepath "hm-shared\hm-shared-framework\src\main\java;hm-shared\hm-shared-common\src\main\java;hm-shared\hm-shared-encrypt\src\main\java" ^
      -d temp ^
      hm-shared\hm-shared-framework\src\main\java\com\hys\hm\shared\framework\repository\BaseRepositoryImpl.java 2>nul

if %errorlevel% equ 0 (
    echo ✓ BaseRepositoryImpl.java 语法检查通过
) else (
    echo ✗ BaseRepositoryImpl.java 语法检查失败
)

echo.
echo 检查 EncryptFieldService.java 语法...
javac -cp "hm-shared\hm-shared-encrypt\src\main\java" ^
      -sourcepath "hm-shared\hm-shared-encrypt\src\main\java" ^
      -d temp ^
      hm-shared\hm-shared-encrypt\src\main\java\com\hys\hm\shared\encrypt\service\EncryptFieldService.java 2>nul

if %errorlevel% equ 0 (
    echo ✓ EncryptFieldService.java 语法检查通过
) else (
    echo ✗ EncryptFieldService.java 语法检查失败
)

echo.
echo 检查 EncryptFieldServiceImpl.java 语法...
javac -cp "hm-shared\hm-shared-encrypt\src\main\java" ^
      -sourcepath "hm-shared\hm-shared-encrypt\src\main\java" ^
      -d temp ^
      hm-shared\hm-shared-encrypt\src\main\java\com\hys\hm\shared\encrypt\service\impl\EncryptFieldServiceImpl.java 2>nul

if %errorlevel% equ 0 (
    echo ✓ EncryptFieldServiceImpl.java 语法检查通过
) else (
    echo ✗ EncryptFieldServiceImpl.java 语法检查失败
)

echo.
echo 检查 EncryptQueryTestController.java 语法...
javac -cp "hm-interfaces\hm-interfaces-web\src\main\java;hm-domain\hm-domain-patient\src\main\java;hm-shared\hm-shared-common\src\main\java" ^
      -sourcepath "hm-interfaces\hm-interfaces-web\src\main\java;hm-domain\hm-domain-patient\src\main\java;hm-shared\hm-shared-common\src\main\java" ^
      -d temp ^
      hm-interfaces\hm-interfaces-web\src\main\java\com\hys\hm\interfaces\web\controller\test\EncryptQueryTestController.java 2>nul

if %errorlevel% equ 0 (
    echo ✓ EncryptQueryTestController.java 语法检查通过
) else (
    echo ✗ EncryptQueryTestController.java 语法检查失败
)

echo.
echo 清理临时文件...
if exist temp rmdir /s /q temp

echo.
echo 语法验证完成！
pause
